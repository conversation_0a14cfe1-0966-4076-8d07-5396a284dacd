<template>
  <div class="bridge-page-container">
    <!-- 筛选区域 -->
    <div class="filter-section">
      <div class="filter-card">
        <el-form :model="filterForm" ref="filterFormRef" class="filter-form">
          <!-- 第一行：基础筛选条件 -->
          <div class="form-row">
            <el-form-item label="桥梁名称" prop="bridgeId">
              <el-select 
                v-model="filterForm.bridgeId" 
                placeholder="请选择桥梁" 
                style="width: 200px;" 
                clearable
                @change="handleBridgeChange"
              >
                <el-option
                  v-for="bridge in bridgeList"
                  :key="bridge.id"
                  :label="bridge.bridgeName"
                  :value="bridge.id"
                />
              </el-select>
            </el-form-item>
            
            <el-form-item label="传感器分组" prop="sensorGroupIds">
              <el-select 
                v-model="filterForm.sensorGroupIds" 
                placeholder="请选择传感器分组" 
                style="width: 300px;" 
                multiple
                clearable
                collapse-tags
                collapse-tags-tooltip
                :max-collapse-tags="2"
              >
                <el-option
                  v-for="group in sensorGroupList"
                  :key="group.id"
                  :label="group.groupName"
                  :value="group.id"
                />
              </el-select>
            </el-form-item>
          </div>
          
          <!-- 第二行：时间选择和操作按钮 -->
          <div class="form-row">
            <el-form-item label="统计时段" prop="dateRange" :rules="[{ required: true, message: '请选择统计时段', trigger: 'change' }]">
              <el-date-picker
                v-model="filterForm.dateRange"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                style="width: 350px;"
              />
            </el-form-item>
            
            <div class="form-actions">
              <el-button type="primary" @click="handleSearch" :loading="searchLoading">
                <el-icon><Search /></el-icon>
                查询
              </el-button>
              <!-- <el-button @click="handleExportChart" :disabled="chartData.length === 0">
                <el-icon><Picture /></el-icon>
                导出图片
              </el-button>
              <el-button @click="handleExportData" :disabled="chartData.length === 0">
                <el-icon><Download /></el-icon>
                导出数据
              </el-button> -->
            </div>
          </div>
        </el-form>
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="chart-section" v-loading="loading" element-loading-text="数据加载中...">
      <div class="chart-container">
        <div class="chart-header">
          <h3 class="chart-title">挠度分析</h3>
          <div class="chart-info">
            <span class="sensor-count">传感器数量：{{ chartData.length }}</span>
            <span class="unit-info" v-if="unitInfo">单位：{{ unitInfo }}</span>
            <span class="time-range" v-if="filterForm.dateRange?.length">
              {{ formatTimeRange(filterForm.dateRange) }}
            </span>
          </div>
        </div>
        <div class="chart-content">
          <div ref="chartRef" class="chart-canvas"></div>
        </div>
      </div>
    </div>

    <!-- 数据概览 -->
    <div class="data-overview" v-if="chartData.length > 0">
      <div class="overview-header">
        <h3>传感器数据概览</h3>
      </div>
      <div class="overview-cards">
        <div class="overview-card" v-for="(item, index) in chartData" :key="item.deviceName">
          <div class="card-header">
            <div class="sensor-info">
              <span class="sensor-indicator" :style="{ backgroundColor: chartColors[index % chartColors.length] }"></span>
              <h4 class="sensor-name">{{ item.deviceName }}</h4>
            </div>
            <span class="sensor-unit">{{ unitInfo }}</span>
          </div>
          <div class="card-content">
            <div class="stats-grid">
              <div class="stat-item">
                <span class="stat-label">平均值</span>
                <span class="stat-value">{{ getAverageValue(item) }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">最大值</span>
                <span class="stat-value max">{{ item.maxvalue?.toFixed(2) || '0' }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">最小值</span>
                <span class="stat-value min">{{ item.minvalue?.toFixed(2) || '0' }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">数据点数</span>
                <span class="stat-value">{{ item.timeValues?.length || 0 }}</span>
              </div>
            </div>
            <div class="time-period" v-if="item.timePeriodName">
              <span class="period-label">时间段：</span>
              <span class="period-value">{{ item.timePeriodName }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <!-- <div class="empty-state" v-if="!loading && chartData.length === 0">
      <el-empty description="暂无数据">
        <template #image>
          <el-icon :size="60" color="#909399"><TrendCharts /></el-icon>
        </template>
        <template #description>
          <span>请选择查询条件后点击查询按钮</span>
        </template>
      </el-empty>
    </div> -->
  </div>
</template>

<script setup>
import { ref, onMounted, reactive, nextTick, onBeforeUnmount } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Picture, Download, TrendCharts } from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import moment from 'moment'
import {
  getBridgeBasicInfoList,
  getDeviceGroupList,
  getDeflectionAnalysis
} from '@/api/bridge'

// 响应式数据
const loading = ref(false)
const searchLoading = ref(false)
const filterFormRef = ref(null)

// 表单数据
const filterForm = reactive({
  bridgeId: '',
  sensorGroupIds: [], // 多选传感器分组
  dateRange: []
})

// 下拉选项数据
const bridgeList = ref([])
const sensorGroupList = ref([])

// 图表相关
const chartRef = ref(null)
let chartInstance = null
const chartData = ref([])
const unitInfo = ref('')
const chartColors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf']

// 初始化
onMounted(async () => {
  await loadBridgeList()
  await loadSensorGroupList()
  // 设置默认时间范围为最近24小时
  setDefaultDateRange()
})

// 组件卸载时销毁图表
onBeforeUnmount(() => {
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
})

// 设置默认时间范围
const setDefaultDateRange = () => {
  const endTime = moment().format('YYYY-MM-DD HH:mm:ss')
  const startTime = moment().subtract(24, 'hours').format('YYYY-MM-DD HH:mm:ss')
  filterForm.dateRange = [startTime, endTime]
}

// 加载桥梁列表
const loadBridgeList = async () => {
  try {
    const response = await getBridgeBasicInfoList()
    if (response.code === 200 && response.data) {
      bridgeList.value = response.data.map(item => ({
        id: item.id,
        bridgeName: item.bridgeName
      }))
    }
  } catch (error) {
    console.error('加载桥梁列表失败:', error)
    ElMessage.error('加载桥梁列表失败')
  }
}

// 加载传感器分组列表
const loadSensorGroupList = async (bridgeId = '') => {
  try {
    const params = bridgeId ? { bridgeId } : {}
    const response = await getDeviceGroupList(params)
    if (response.code === 200 && response.data) {
      sensorGroupList.value = response.data.map(item => ({
        id: item.id,
        groupName: item.groupName
      }))
    }
  } catch (error) {
    console.error('加载传感器分组列表失败:', error)
    ElMessage.error('加载传感器分组列表失败')
  }
}

// 桥梁选择变化
const handleBridgeChange = (bridgeId) => {
  filterForm.sensorGroupIds = []
  if (bridgeId) {
    loadSensorGroupList(bridgeId)
  } else {
    loadSensorGroupList()
  }
}

// 查询数据
const handleSearch = async () => {
  // 表单验证
  if (!filterForm.dateRange || filterForm.dateRange.length !== 2) {
    ElMessage.warning('请选择统计时段')
    return
  }

  searchLoading.value = true
  loading.value = true

  try {
    // 构建请求参数
    const params = {
      bridgeName: getBridgeName(filterForm.bridgeId),
      sensor: filterForm.sensorGroupIds,
      startDate: filterForm.dateRange[0],
      endDate: filterForm.dateRange[1]
    }

    const response = await getDeflectionAnalysis(params)

    if (response.code === 200) {
      if (!response.data) {
        ElMessage.info('查询结果为空')
        chartData.value = []
        unitInfo.value = ''
        return
      }
      
      chartData.value = response.data.dataValues || []
      unitInfo.value = response.data.unitName || response.data.unit || ''

      // 渲染图表
      await nextTick()
      renderChart()

      if (chartData.value.length === 0) {
        ElMessage.info('查询结果为空')
      }
    } else {
      ElMessage.error(response.message || '查询失败')
    }
  } catch (error) {
    console.error('查询数据失败:', error)
    ElMessage.error('查询数据失败')
  } finally {
    searchLoading.value = false
    loading.value = false
  }
}

// 导出图片
const handleExportChart = () => {
  if (!chartInstance) {
    ElMessage.warning('暂无图表数据')
    return
  }
  
  try {
    const url = chartInstance.getDataURL({
      type: 'png',
      pixelRatio: 2,
      backgroundColor: '#fff'
    })
    
    const link = document.createElement('a')
    link.download = `挠度分析_${moment().format('YYYY-MM-DD_HH-mm-ss')}.png`
    link.href = url
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    
    ElMessage.success('图片导出成功')
  } catch (error) {
    console.error('导出图片失败:', error)
    ElMessage.error('导出图片失败')
  }
}

// 导出数据
const handleExportData = () => {
  if (chartData.value.length === 0) {
    ElMessage.warning('暂无数据')
    return
  }
  
  try {
    // 构建CSV数据
    let csvContent = '\uFEFF' // BOM for UTF-8
    csvContent += '传感器名称,时间,挠度值,最大值,最小值,时间段\n'
    
    chartData.value.forEach(item => {
      if (item.timeValues && item.timeValues.length > 0) {
        item.timeValues.forEach(tv => {
          csvContent += `${item.deviceName},${tv.time},${tv.value || 0},${item.maxvalue || 0},${item.minvalue || 0},${item.timePeriodName || ''}\n`
        })
      } else {
        csvContent += `${item.deviceName},${item.time || ''},${item.value || 0},${item.maxvalue || 0},${item.minvalue || 0},${item.timePeriodName || ''}\n`
      }
    })
    
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    link.download = `挠度分析数据_${moment().format('YYYY-MM-DD_HH-mm-ss')}.csv`
    link.href = URL.createObjectURL(blob)
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    
    ElMessage.success('数据导出成功')
  } catch (error) {
    console.error('导出数据失败:', error)
    ElMessage.error('导出数据失败')
  }
}

// 重置表单
const handleReset = () => {
  filterForm.bridgeId = ''
  filterForm.sensorGroupIds = []
  setDefaultDateRange()
  chartData.value = []
  unitInfo.value = ''

  // 清空图表
  if (chartInstance) {
    chartInstance.clear()
  }
}

// 获取桥梁名称
const getBridgeName = (bridgeId) => {
  const bridge = bridgeList.value.find(item => item.id === bridgeId)
  return bridge ? bridge.bridgeName : ''
}

// 格式化时间范围显示
const formatTimeRange = (dateRange) => {
  if (!dateRange || dateRange.length !== 2) return ''
  const start = moment(dateRange[0]).format('MM-DD HH:mm')
  const end = moment(dateRange[1]).format('MM-DD HH:mm')
  return `${start} ~ ${end}`
}

// 获取平均值
const getAverageValue = (item) => {
  if (!item.timeValues || item.timeValues.length === 0) {
    return item.value?.toFixed(2) || '0'
  }
  
  const values = item.timeValues.map(tv => tv.value || 0).filter(v => v !== null && v !== undefined)
  if (values.length === 0) return '0'
  
  const avg = values.reduce((sum, val) => sum + val, 0) / values.length
  return avg.toFixed(2)
}

// 渲染图表
const renderChart = () => {
  if (!chartRef.value || chartData.value.length === 0) return

  // 销毁已存在的图表实例
  if (chartInstance) {
    chartInstance.dispose()
  }

  chartInstance = echarts.init(chartRef.value)

  // 准备图表数据
  const series = []
  const legend = []
  
  chartData.value.forEach((item, index) => {
    // 处理时间序列数据
    let data = []
    if (item.timeValues && item.timeValues.length > 0) {
      data = item.timeValues
        .sort((a, b) => new Date(a.time) - new Date(b.time))
        .map(tv => [tv.time, tv.value || 0])
    } else if (item.time && item.value !== undefined) {
      data = [[item.time, item.value || 0]]
    }
    
    if (data.length > 0) {
      legend.push(item.deviceName)
      series.push({
        name: item.deviceName,
        type: 'line',
        data: data,
        smooth: true,
        symbol: 'circle',
        symbolSize: 4,
        lineStyle: {
          width: 2,
          color: chartColors[index % chartColors.length]
        },
        itemStyle: {
          color: chartColors[index % chartColors.length]
        }
      })
    }
  })

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#999'
        }
      },
      formatter: function (params) {
        let tooltip = `<div style="margin-bottom: 5px; font-weight: bold;">${moment(params[0].axisValue).format('YYYY-MM-DD HH:mm:ss')}</div>`
        params.forEach(param => {
          tooltip += `<div style="margin-bottom: 3px;">
            <span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${param.color};"></span>
            ${param.seriesName}: ${param.value[1]} ${unitInfo.value}
          </div>`
        })
        return tooltip
      }
    },
    legend: {
      data: legend,
      top: 10,
      type: 'scroll',
      textStyle: {
        color: '#666'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'time',
      name: '时间',
      nameLocation: 'end',
      nameGap: 15,
      nameTextStyle: {
        color: '#666',
        fontSize: 12
      },
      axisLine: {
        lineStyle: {
          color: '#e6e6e6'
        }
      },
      axisLabel: {
        color: '#666',
        formatter: function (value) {
          return moment(value).format('HH:mm')
        }
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: '#f5f5f5'
        }
      }
    },
    yAxis: {
      type: 'value',
      name: `挠度(${unitInfo.value})`,
      nameLocation: 'end',
      nameGap: 15,
      nameTextStyle: {
        color: '#666',
        fontSize: 12
      },
      axisLine: {
        lineStyle: {
          color: '#e6e6e6'
        }
      },
      axisLabel: {
        color: '#666'
      },
      splitLine: {
        lineStyle: {
          color: '#f5f5f5'
        }
      }
    },
    series: series,
    animation: true,
    animationDuration: 1000,
    animationEasing: 'cubicOut'
  }

  chartInstance.setOption(option)

  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    chartInstance && chartInstance.resize()
  })
}

// 清理事件监听
onBeforeUnmount(() => {
  window.removeEventListener('resize', () => {
    chartInstance && chartInstance.resize()
  })
})
</script>

<style scoped>
.bridge-page-container {
  background-color: #f5f7fa;
  height: calc(100vh - 200px);
  overflow-y: auto;
}

/* 筛选区域 */
.filter-section {
  margin-bottom: 20px;
}

.filter-card {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.filter-form {
  margin: 0;
}

.form-row {
  display: flex;
  align-items: flex-start;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 16px;
}

.form-row:last-child {
  margin-bottom: 0;
}

.form-actions {
  display: flex;
  gap: 10px;
  margin-left: auto;
}

/* 图表区域 */
.chart-section {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.chart-container {
  padding: 20px;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #f0f0f0;
}

.chart-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.chart-info {
  display: flex;
  align-items: center;
  gap: 20px;
  font-size: 14px;
  color: #909399;
}

.sensor-count {
  color: #409eff;
  font-weight: 500;
}

.unit-info {
  color: #67c23a;
  font-weight: 500;
}

.time-range {
  color: #606266;
}

.chart-content {
  height: 450px;
}

.chart-canvas {
  width: 100%;
  height: 100%;
}

/* 数据概览区域 */
.data-overview {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
}

.overview-header {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #f0f0f0;
}

.overview-header h3 {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.overview-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 20px;
}

.overview-card {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #e9ecef;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.sensor-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.sensor-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.sensor-name {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.sensor-unit {
  font-size: 12px;
  color: #909399;
  background: #e7f3ff;
  padding: 2px 8px;
  border-radius: 4px;
}

.card-content {
  margin-top: 12px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
  margin-bottom: 12px;
}

.stat-item {
  text-align: center;
  padding: 8px;
  background: white;
  border-radius: 6px;
  border: 1px solid #ebeef5;
}

.stat-label {
  display: block;
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
}

.stat-value {
  display: block;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.stat-value.max {
  color: #f56c6c;
}

.stat-value.min {
  color: #409eff;
}

.time-period {
  padding: 8px;
  background: white;
  border-radius: 6px;
  border: 1px solid #ebeef5;
  font-size: 12px;
}

.period-label {
  color: #909399;
}

.period-value {
  color: #606266;
  font-weight: 500;
}

/* 空状态 */
.empty-state {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 60px;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .form-row {
    flex-direction: column;
    align-items: stretch;
  }
  
  .form-actions {
    margin-left: 0;
    justify-content: flex-start;
  }
}

@media (max-width: 1200px) {
  .chart-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .chart-info {
    align-self: flex-start;
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }
}

@media (max-width: 768px) {
  .bridge-page-container {
    padding: 10px;
  }

  .filter-card {
    padding: 15px;
  }

  .chart-container {
    padding: 15px;
  }

  .chart-content {
    height: 300px;
  }

  .overview-cards {
    grid-template-columns: 1fr;
  }

  .stats-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 8px;
  }

  .stat-item {
    padding: 6px;
  }

  .stat-value {
    font-size: 14px;
  }

  .form-actions {
    flex-direction: column;
    gap: 8px;
  }

  .form-actions .el-button {
    width: 100%;
  }
}
</style>