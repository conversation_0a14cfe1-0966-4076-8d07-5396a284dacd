<template>
    <div class="slider-demo-block">
        <span class="demonstration">透明度：{{transparencyValue}}</span>
        <el-slider
                v-model="transparencyValue"
                @change="handleTransparencyChange"
                size="small"/>
    </div>
</template>

<script setup>
import {ref, watch} from 'vue'
import { useRoute } from "vue-router";
import {mapStates} from "@/components/GisMap/mapStates.js";
import bus from "@/utils/mitt.js";
const route = useRoute();
const transparencyValue = ref(100);
const handleTransparencyChange = (value) => {
    console.log('Transparency value changed:', value);
    value /= 100.0;
    let alpha = value;
    alpha = Cesium.Math.clamp(alpha, 0.0, 1.0);
    mapStates.viewer.scene.globe.translucency.enabled = true;
    mapStates.viewer.scene.globe.translucency.frontFaceAlpha = alpha;
    mapStates.viewer.scene.globe.translucency.backFaceAlpha = alpha;
    const osgbLayers = mapStates.viewer.scene.layers._layers.filter(layer => ["倾斜摄影","桥梁"].includes(layer.name));
    osgbLayers.forEach(layer => {
        layer.transparent = alpha;
    })
};

// 透视地图
bus.on("resetEyesEarth",  () => {
    transparencyValue.value = 0;
    mapStates.viewer.scene.globe.translucency.enabled = true;
    mapStates.viewer.scene.globe.translucency.frontFaceAlpha = 0;
    mapStates.viewer.scene.globe.translucency.backFaceAlpha = 0;
    const osgbLayers = mapStates.viewer.scene.layers._layers.filter(layer => ["倾斜摄影","桥梁"].includes(layer.name));
    osgbLayers.forEach(layer => {
        layer.transparent = 0;
    })
})

watch(
    () => route,
    (val) => {
        transparencyValue.value = 100;
        mapStates.viewer.scene.globe.translucency.enabled = false;
        mapStates.viewer.scene.globe.translucency.frontFaceAlpha = 1.0;
        mapStates.viewer.scene.globe.translucency.backFaceAlpha = 1.0;
        const osgbLayers = mapStates.viewer.scene.layers._layers.filter(layer => ["倾斜摄影","桥梁"].includes(layer.name));
        osgbLayers.forEach(layer => {
            layer.transparent = 100;
        })
    },
    {
        deep: true,
    }
);

</script>

<style scoped>
.slider-demo-block {
    width: 300px;
    display: flex;
    align-items: center;
    background: rgba(13, 37, 82, 0.8);
}
.slider-demo-block .el-slider {
    margin-top: 0;
    margin-left: 12px;
}
.slider-demo-block .demonstration {
    font-size: 14px;
    font-weight: 500;
    color: #fff;
    line-height: 32px;
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-bottom: 0;
    margin-left: 10px;
}
.slider-demo-block .demonstration + .el-slider {
    margin-right: 10px;
    flex: 0 0 61%;
}
</style>