<template>
  <div class="component-model-list">
      <div class="model_level2">
          <div
                  v-for="item in model_l2Data"
                  :key="item.title"
                  class="butt-normal"
                  :class="[state.selectedLevel2 === item.id ? 'butt-active' : '']"
                  @click="handleClickLevel2(item.id)"
          >
              {{ item.title }}
          </div>
      </div>
  </div>
</template>

<script setup>
import bus from "@/utils/mitt";
import {reactive} from "vue";

const state = reactive({
    selectedLevel2: 1,
});
const model_l2Data = reactive([
    {
        id: 1,
        title: "结构瓶颈模拟分析",
    },
    {
        id: 2,
        title: "管网淤积预测预警分析",
    },
    {
        id: 3,
        title: "管网溢流预测预警分析",
    },
    {
        id: 4,
        title: "泵站负荷能力分析",
    },
]);

const handleClickLevel2 = (e) => {
    //todo 激活模型
    state.selectedLevel2 = e;
};
</script>

<style lang="scss" scoped>
.component-model-list {
  pointer-events: all;
  width: 178px;

  .model_level2 {
    width: 178px;
  }

  .butt-normal {
    margin-bottom: 10px;
    padding: 8px 10px;
    border-radius: 4px;
    cursor: pointer;
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.9);
    font-size: 14px;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.7);
    text-align: center;

    &:hover {
      background: rgba(26, 142, 231, 0.9);
      border: 1px solid rgba(26, 142, 231, 1);
    }
  }

  .butt-active {
    background: rgba(26, 142, 231, 0.9);
    border: 1px solid rgba(26, 142, 231, 1);
    color: #ffffff;
  }

}
</style>
