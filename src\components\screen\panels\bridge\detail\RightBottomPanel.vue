<template>
  <PanelBox title="桥梁病害">
    <template #extra>
      <div class="com-select">
        <CommonSelect v-model="timeRange" :options="timeOptions" @change="handleTimeChange" />
      </div>
    </template>
    <div class="panel-content">
      <!-- 病害数量统计区域 -->
      <div class="stats-row">
        <div class="stat-item">
          <div class="stat-info">
            <span class="stat-dot"><span class="stat-dot-inner red"></span></span>
            <span class="stat-label">病害总数</span>
            <span class="stat-value red-gradient">{{ statsData.total }}</span>
          </div>
        </div>
        <div class="stat-item">
          <div class="stat-info">
            <span class="stat-dot"><span class="stat-dot-inner blue"></span></span>
            <span class="stat-label">已处理</span>
            <span class="stat-value blue-value">{{ statsData.handled }}</span>
          </div>
        </div>
        <div class="stat-item">
          <div class="stat-info">
            <span class="stat-dot"><span class="stat-dot-inner green"></span></span>
            <span class="stat-label">处理完成率</span>
            <span class="stat-value green-gradient">{{ statsData.rate }}</span>
          </div>
        </div>
      </div>

      <!-- 病害列表区域 -->
      <ScrollTable :columns="tableColumns" :data="alarmList" :autoScroll="true" :scrollSpeed="2000"
        :tableHeight="tableHeight" :visibleRows="2" :hiddenHeader="true">
        <template #custom="{ row }">
          <div class="alarm-row" @click="openAlarmDetail(row)">
            <div class="alarm-main">
              <span class="alarm-type">{{ row.type }}</span>
              <div class="alarm-level">
                <span class="level-tag" :style="{ background: getLevelColor(row.level) }">{{ row.level }}</span>
              </div>
              <div class="alarm-status">
                <span class="status-text">{{ row.status }}</span>
              </div>
            </div>
            <div class="alarm-location">
              <img src="@/assets/images/screen/common/location.svg" alt="location" class="location-icon" />
              <span class="location-text">{{ row.location }}</span>
              <span class="time-text">{{ row.time }}</span>
            </div>
            <div class="alarm-divider"></div>
          </div>
        </template>
      </ScrollTable>
    </div>
  </PanelBox>
</template>

<script setup>
import { ref, computed } from 'vue'
import PanelBox from '@/components/screen/PanelBox.vue'
import CommonSelect from '@/components/screen/CommonSelect.vue'
import ScrollTable from '@/components/screen/common/ScrollTable.vue'

// 时间选择
const timeRange = ref('week')
const timeOptions = [
  { label: '近一周', value: 'week' },
  { label: '近一月', value: 'month' },
  { label: '近一年', value: 'year' }
]

// 统计数据
const statsData = ref({
  total: 10,
  handled: 6,
  rate: '60%'
})

// 不同时间范围的数据
const timeRangeData = {
  week: {
    total: 10,
    handled: 6,
    rate: '60%'
  },
  month: {
    total: 10,
    handled: 6,
    rate: '60%'
  },
  year: {
    total: 10,
    handled: 6,
    rate: '60%'
  }
}

// 不同时间范围的病害列表数据
const alarmListData = {
  week: [
    {
      type: '************病害名称',
      level: '三级病害',
      status: '处理中',
      location: '陆圈镇大桥',
      time: '10月1日 9:00'
    },
    {
      type: '************病害名称',
      level: '三级病害',
      status: '处理中',
      location: '陆圈镇大桥',
      time: '10月1日 9:00'
    },
    {
      type: '************病害名称',
      level: '三级病害',
      status: '处理中',
      location: '陆圈镇大桥',
      time: '10月1日 9:00'
    },
    {
      type: '************病害名称',
      level: '二级病害',
      status: '待处理',
      location: '陆圈镇大桥',
      time: '10月1日 8:30'
    },
    {
      type: '************病害名称',
      level: '一级病害',
      status: '已处理',
      location: '陆圈镇大桥',
      time: '9月30日 19:45'
    }
  ],
  month: [
    {
      type: '************病害名称',
      level: '三级病害',
      status: '处理中',
      location: '陆圈镇大桥',
      time: '9月20日 13:20'
    },
    {
      type: '************病害名称',
      level: '二级病害',
      status: '已处理',
      location: '陆圈镇大桥',
      time: '9月15日 10:40'
    },
    {
      type: '************病害名称',
      level: '三级病害',
      status: '已处理',
      location: '陆圈镇大桥',
      time: '9月10日 16:25'
    }
  ],
  year: [
    {
      type: '************病害名称',
      level: '一级病害',
      status: '已处理',
      location: '陆圈镇大桥',
      time: '8月15日 14:30'
    },
    {
      type: '************病害名称',
      level: '二级病害',
      status: '已处理',
      location: '陆圈镇大桥',
      time: '7月10日 09:45'
    },
    {
      type: '************病害名称',
      level: '一级病害',
      status: '已处理',
      location: '陆圈镇大桥',
      time: '6月5日 11:20'
    }
  ]
}

// 表格配置
const tableColumns = [
  { title: '病害信息', dataIndex: 'custom', width: '100%' }
]

// 动态计算表格高度
const tableHeight = computed(() => {
  if (window.innerHeight === 910) {
    return '280px'
  } else if (window.innerHeight >= 940 && window.innerHeight <= 1055) {
    return '320px'
  } else if (window.innerWidth >= 2561) {
    return '360px'
  } else if (window.innerWidth >= 1920 && window.innerWidth <= 2560) {
    return '300px'
  } else {
    return '260px'
  }
})

// 病害列表数据，初始为一周的数据
const alarmList = ref(alarmListData.week)

// 获取病害等级对应的颜色
const getLevelColor = (level) => {
  const colorMap = {
    '一级病害': '#FB3737', // 红色
    '二级病害': '#FF6D28', // 橙色
    '三级病害': '#EAA01B', // 黄色
    '四级病害': '#3FD87C'  // 绿色
  }
  return colorMap[level] || '#3FD87C'
}

// 处理时间范围变化
const handleTimeChange = (value) => {
  console.log('时间范围变更为:', value)
  // 更新统计数据
  statsData.value = { ...timeRangeData[value] }
  // 更新病害列表数据
  alarmList.value = alarmListData[value]
}

// 打开病害详情弹窗
const openAlarmDetail = (row) => {
  console.log('打开病害详情:', row)
  // 预留病害详情弹窗功能
}
</script>

<style scoped>
.panel-content {
  flex: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.com-select {
  margin-right: 20px;
}

/* 统计数据样式 */
.stats-row {
  display: flex;
  justify-content: space-around;
  padding: 0 5px 0px 10px;
}

.stat-item {
  display: flex;
}

.stat-info {
  display: flex;
  align-items: center;
  gap: 4px;
}

.stat-dot {
  position: relative;
  width: 9px;
  height: 9px;
  border-radius: 50%;
}

.stat-dot-inner {
  width: 5px;
  height: 5px;
  border-radius: 50%;
  position: absolute;
  top: 2px;
  left: 2px;
}

.stat-dot-inner.red {
  background: #055ADB;
}

.stat-dot-inner.blue {
  background: #23CAFF;
}

.stat-dot-inner.green {
  background: #3FD87C;
}

.stat-dot:has(.stat-dot-inner.red) {
  background: rgba(5,90,219,0.4);
}

.stat-dot:has(.stat-dot-inner.blue) {
  background: rgba(35, 202, 255, 0.4);
}

.stat-dot:has(.stat-dot-inner.green) {
  background: rgba(63, 216, 124, 0.4);
}

.stat-label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #FFFFFF;
}

.stat-value {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 24px;
  line-height: 26px;
  font-style: normal;
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  text-align: center;
}

.red-gradient {
  background: linear-gradient(90deg, #FFFFFF 0%, #055ADB 100%);;
  -webkit-background-clip: text;
}

.blue-value {
  color: #3CF3FF;
}

.green-gradient {
  background: linear-gradient(90deg, #FFFFFF 0%, #36F281 100%);
  -webkit-background-clip: text;
}

/* 病害列表样式 */
.alarm-row {
  padding: 10px 5px;
  width: 100%;
  position: relative;
  cursor: pointer;
  transition: background-color 0.3s;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.alarm-row:hover {
  background-color: rgba(59, 141, 242, 0.1);
}

.alarm-main {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.alarm-type {
  font-family: PingFangSC, PingFang SC;
  font-weight: 600;
  font-size: 14px;
  color: #FFFFFF;
  width: 40%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.alarm-level {
  width: 30%;
  display: flex;
  justify-content: center;
}

.level-tag {
  width: 60px;
  height: 24px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 12px;
  color: #FFFFFF;
}

.alarm-status {
  width: 30%;
  display: flex;
  justify-content: flex-end;
}

.status-text {
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 14px;
  color: #FFAA28;
}

.alarm-location {
  display: flex;
  align-items: center;
  gap: 10px;
}

.location-icon {
  width: 12px;
  height: 12px;
}

.location-text {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #FFFFFF;
  opacity: 0.6;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.time-text {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #FFFFFF;
  opacity: 0.8;
}

.alarm-divider {
  display: none;
}

/* 响应式布局适配 */
@media screen and (min-width: 1920px) and (max-width: 2560px) {
  .panel-content {
    padding: 15px;
  }
}

@media screen and (max-width: 1919px) {
  .panel-content {
    padding: 12px;
  }
}

@media screen and (min-width: 2561px) {
  .panel-content {
    padding: 18px;
  }
}

/* 添加全屏模式(1080px高度)的适配 */
@media screen and (min-height: 1056px) and (max-height: 1080px) {
  .panel-content {
    padding: 15px;
  }

  .stat-value {
    font-size: 24px;
    line-height: 26px;
  }
}

@media screen and (min-height: 940px) and (max-height: 1055px) {
  .panel-content {
    padding: 10px;
    gap: 10px;
  }

  .stat-value {
    font-size: 20px;
    line-height: 22px;
  }
}

@media screen and (min-height: 900px) and (max-height: 940px) {
  .panel-content {
    padding: 8px;
    gap: 8px;
  }

  .stat-value {
    font-size: 16px;
    line-height: 18px;
  }
  .alarm-main {
    margin-bottom: 2px;
  }
  .alarm-row {
    padding: 5px 5px;
  }
}
</style>