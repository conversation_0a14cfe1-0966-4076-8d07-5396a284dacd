<!-- 设备信息 -->
<template>
    <div class="device-base-info">
        <!-- 有视频时显示播放器 -->
        <div v-if="baseInfo" class="video-container">
            <VideoPlayer
                :src="baseInfo?.hlsUrl"
                :autoplay="true"
                :muted="true"
                :loop="true"
                :showControls="true"
                :showStatus="true"
                :isOnline="baseInfo?.online"
                class="video-player"
            />
        </div>

        <!-- 空白插槽 -->
        <div v-else class="video-placeholder">
            <div class="placeholder-content">
                <i class="el-icon-video-camera placeholder-icon"></i>
                <div class="placeholder-text">暂无预览信息</div>
            </div>
            <div class="video-info">
                <span class="video-title">-</span>
            </div>
        </div>
    </div>
</template>

<script setup>

import {onMounted, watch} from "vue";
import VideoPlayer from "@/components/screen/common/VideoPlayer.vue";

const props = defineProps({
    baseInfo: {
        type: Array,
        default: () => [],
    },
});

// 如果 list 动态变化，重新计算最大宽度
watch(() => props.baseInfo,() => {
    // console.log('baseInfo---xxx', props.baseInfo);
    }, {deep: true});
</script>
<style lang="scss" scoped>
.device-base-info {
    height: 360px;
    overflow-y: auto;
    display: flex;
    /*flex-wrap: wrap;
    align-content: flex-start;*/

    .video-container,
    .video-placeholder {
        display: flex;
        flex-direction: column;
        width: 100%;
        height: 100%;
    }

    .video-player {
        height: 335px;
        border-radius: 4px 4px 0 0;
        overflow: hidden;
        flex-shrink: 0;
    }

    .video-placeholder {
        height: 200px;
        background: #f7f7f7;
        display: flex;
        flex-direction: column;
        border-radius: 4px 4px 0 0;
    }

    .placeholder-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }

    .placeholder-icon {
        font-size: 32px;
        color: #ccc;
        margin-bottom: 8px;
    }

    .placeholder-text {
        color: #999;
        font-size: 12px;
    }

    .video-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 12px;
        background: #f7f7f7;
        border-radius: 0 0 4px 4px;
        min-height: 40px;
        flex-shrink: 0;
    }

    .video-title {
        font-size: 14px;
        color: #333;
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
}
</style>
