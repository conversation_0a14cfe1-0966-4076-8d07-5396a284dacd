<template>
  <PanelBox title="报警处置(近30天)">
    <template #extra>
      <div class="com-select">
      <!-- <CommonSelect
        v-model="timeRange"
        :options="timeOptions"
        @change="handleTimeChange"
      /> -->
    </div>
    </template>
    <div class="panel-content">
      <div class="stats-row">
        <div class="stat-item">
          <span class="stat-dot"><span class="stat-dot-inner"></span></span>
          <span class="stat-label">报警总数</span>
          <span class="stat-value-blue">{{ statsData.totalAlarms }}</span>
          <span class="stat-unit">个</span>
        </div>
        <div class="stat-item">
          <span class="stat-dot"><span class="stat-dot-inner"></span></span>
          <span class="stat-label">已处置</span>
          <span class="stat-value-highlight">{{ statsData.handledAlarms }}</span>
          <span class="stat-unit">个</span>
        </div>
        <div class="stat-item">
          <span class="stat-dot"><span class="stat-dot-inner"></span></span>
          <span class="stat-label">设备在线率</span>
          <span class="stat-value-gradient">{{ statsData.handleRate }}</span>
        </div>
      </div>
      
      <div class="chart-wrapper" ref="chartRef"></div>
    </div>
  </PanelBox>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick, watch } from 'vue'
import * as echarts from 'echarts'
import PanelBox from '@/components/screen/PanelBox.vue'
import CommonSelect from '@/components/screen/CommonSelect.vue'
// 导入API方法
import { getAlarmStatistics } from '@/api/heating'

// 时间选择
const timeRange = ref('week')
const timeOptions = [
  { label: '近一周', value: 'week' },
  { label: '近一月', value: 'month' },
  { label: '近一年', value: 'year' }
]

// 统计数据
const statsData = reactive({
  totalAlarms: 0,
  handledAlarms: 0,
  handleRate: '0%'
})

// 图表数据
const weekChartData = {
  xAxis: [],
  values: []
}

const monthChartData = {
  xAxis: [],
  values: []
}

const yearChartData = {
  xAxis: [],
  values: []
}

// 当前展示数据
const currentChartData = ref(weekChartData)

// 图表实例
const chartRef = ref(null)
let chartInstance = null

// 处理时间范围变化
const handleTimeChange = (value) => {
  // 根据选择的时间范围更新图表数据
  if (value === 'week') {
    currentChartData.value = weekChartData
  } else if (value === 'month') {
    currentChartData.value = monthChartData
  } else if (value === 'year') {
    currentChartData.value = yearChartData
  }
  
  // 更新图表
  updateChart()
  
  // 预留后端数据请求
  // fetchData(value)
}

// 从后端获取数据
const fetchData = async (timeRange) => {
  try {
    const res = await getAlarmStatistics()
    if (res.code === 200 && res.data) {
      // 处理统计数据
      statsData.totalAlarms = res.data.totalAlarms || 0
      statsData.handledAlarms = res.data.handledAlarms || 0
      statsData.handleRate = (res.data.handlingRate !== undefined ? res.data.handlingRate + '%' : '0%')
      
      // 处理图表数据
      if (res.data.alarmTrends && res.data.alarmTrends.length > 0) {
        // 提取日期和报警数量
        const dates = res.data.alarmTrends.map(item => item.date.substring(5)) // 只取月-日部分
        const alarmCounts = res.data.alarmTrends.map(item => item.alarmCount)
        
        // 根据当前选择的时间范围更新对应的图表数据
        if (timeRange === 'week') {
          weekChartData.xAxis = dates
          weekChartData.values = alarmCounts
          currentChartData.value = weekChartData
        } else if (timeRange === 'month') {
          monthChartData.xAxis = dates
          monthChartData.values = alarmCounts
          currentChartData.value = monthChartData
        } else if (timeRange === 'year') {
          yearChartData.xAxis = dates
          yearChartData.values = alarmCounts
          currentChartData.value = yearChartData
        }
        
        // 更新图表
        updateChart()
      }
    }
  } catch (error) {
    console.error('获取报警处置数据失败:', error)
  }
}

// 更新图表
const updateChart = () => {
  if (!chartInstance) return
  
  const option = createChartOption()
  chartInstance.setOption(option)
}

// 创建图表配置
const createChartOption = () => {
  return {
    backgroundColor: 'transparent',
    grid: {
      top: '15%',
      left: '5%',
      right: '4%',
      bottom: '18%',
      containLabel: true
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0, 19, 40, 0.8)',
      borderColor: 'rgba(0, 109, 232, 0.2)',
      textStyle: {
        color: '#fff',
        fontSize: 12,
        fontFamily: 'PingFangSC, PingFang SC'
      },
      formatter: function(params) {
        const param = params[0]
        return `${param.name}<br/>${param.marker}报警数量：${param.value}`
      }
    },
    xAxis: {
      type: 'category',
      data: currentChartData.value.xAxis,
      axisLine: {
        lineStyle: {
          color: '#5F5F60',
          width: 1
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        fontFamily: 'PingFangSC, PingFang SC',
        fontSize: 12,
        color: '#FFFFFF',
        margin: 12
      }
    },
    yAxis: {
      type: 'value',
      name: '单位（个）',
      nameTextStyle: {
        color: "rgba(255, 255, 255, 0.6)",
        fontSize: 12,
        padding: [0, 30, 0, 0]
      },
      splitLine: {
        show: false,
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)',
          type: 'dashed'
        }
      },
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        fontFamily: 'PingFangSC, PingFang SC',
        fontSize: 12,
        color: '#FFFFFF',
        margin: 12
      }
    },
    series: [
      {
        type: 'line',
        data: currentChartData.value.values,
        smooth: false,
        symbol: 'none',
        symbolSize: 6,
        itemStyle: {
          color: '#D9E7FF'
        },
        lineStyle: {
          color: '#D9E7FF',
          width: 2,
          shadowColor: 'rgba(0, 92, 228, 0.4)',
          shadowBlur: 6
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(217, 231, 255, 0.45)' },
              { offset: 1, color: 'rgba(217, 231, 255, 0.01)' }
            ]
          }
        }
      }
    ]
  }
}

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return
  
  chartInstance = echarts.init(chartRef.value)
  const option = createChartOption()
  chartInstance.setOption(option)
  
  // 响应式调整图表大小
  window.addEventListener('resize', () => {
    chartInstance && chartInstance.resize()
  })
}

// 监听时间范围变化
watch(timeRange, (newVal) => {
  handleTimeChange(newVal)
})

// 初始化
onMounted(async () => {
  await nextTick()
  // 初始化图表
  initChart()
  // 获取初始数据
  await fetchData(timeRange.value)
})
</script>

<style scoped>
.panel-content {
  flex: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}
.com-select{
  margin-right: 20px;
}
.stats-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

.stat-dot {
  width: 9px;
  height: 9px;
  background: rgba(5, 90, 219, 0.4);
  border-radius: 50%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.stat-dot-inner {
  width: 5px;
  height: 5px;
  background: #055ADB;
  border-radius: 50%;
  position: absolute;
}

.stat-label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #FFFFFF;
}

.stat-value-blue {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 24px;
  line-height: 26px;
  text-align: left;
  font-style: normal;
  background: linear-gradient(90deg, #FFFFFF 0%, #055ADB 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stat-value-highlight {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 24px;
  color: #3CF3FF;
  line-height: 26px;
}

.stat-value-gradient {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 24px;
  line-height: 26px;
  text-align: left;
  font-style: normal;
  background: linear-gradient(90deg, #FFFFFF 0%, #36F281 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stat-unit {
  color: rgba(255, 255, 255, 0.4);
  font-size: 12px;
}

.chart-wrapper {
  flex: 1;
  width: 100%;
  min-height: 220px;
}

/* 响应式布局适配 */
@media (min-height: 940px) and (max-height: 1055px) {
  .panel-content {
    padding: 10px;
    gap: 10px;
  }
  
  .stat-item {
    gap: 3px;
  }
  
  .stat-dot {
    width: 7px;
    height: 7px;
  }
  
  .stat-dot-inner {
    width: 3px;
    height: 3px;
  }
  
  .stat-label {
    font-size: 11px;
  }
  
  .stat-value-blue,
  .stat-value-highlight,
  .stat-value-gradient {
    font-size: 18px;
    line-height: 22px;
  }
  
  .stat-unit {
    font-size: 10px;
  }
}

@media (max-height: 780px) {
  .chart-wrapper {
    min-height: 170px;
  }
}
/* 适配不同屏幕尺寸 */
@media screen and (min-height: 900px) and (max-height: 940px) {
  .stats-row {
    margin-bottom: -6px;
  }

  .panel-content {
    gap: 0px;
  }
}
</style>