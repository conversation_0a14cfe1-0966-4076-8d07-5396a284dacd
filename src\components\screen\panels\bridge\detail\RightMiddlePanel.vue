<template>
  <PanelBox title="报警趋势">
    <template #extra>
      <div class="com-select">
        <CommonSelect v-model="timeRange" :options="timeOptions" @change="handleTimeChange" />
      </div>
    </template>
    <div class="panel-content">
      <!-- 图表容器 -->
      <div class="chart-container">
        <div ref="chartRef" class="trend-chart"></div>
      </div>
    </div>
  </PanelBox>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import PanelBox from '@/components/screen/PanelBox.vue'
import CommonSelect from '@/components/screen/CommonSelect.vue'
import * as echarts from 'echarts'

// 定义props
const props = defineProps({
  bridgeId: {
    type: [String, Number],
    required: true
  },
  bridgeName: {
    type: String,
    default: '未知桥梁'
  }
})

// 时间选择
const timeRange = ref('week')
const timeOptions = [
  { label: '近7天', value: 'week' },
  { label: '近30天', value: 'month' },
  { label: '近6个月', value: 'half_year' }
]

// 图表实例
const chartRef = ref(null)
let chartInstance = null

// 不同时间范围的数据
const chartData = {
  week: {
    xAxis: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
    series: [
      {
        name: '总数',
        data: [18, 15, 12, 8, 5, 3, 2]
      },
      {
        name: '一级报警',
        data: [8, 6, 5, 3, 2, 1, 1]
      },
      {
        name: '二级报警',
        data: [6, 5, 4, 3, 2, 1, 1]
      },
      {
        name: '三级报警',
        data: [4, 4, 3, 2, 1, 1, 0]
      }
    ]
  },
  month: {
    xAxis: ['第1周', '第2周', '第3周', '第4周'],
    series: [
      {
        name: '总数',
        data: [25, 20, 15, 12]
      },
      {
        name: '一级报警',
        data: [10, 8, 6, 5]
      },
      {
        name: '二级报警',
        data: [8, 7, 5, 4]
      },
      {
        name: '三级报警',
        data: [7, 5, 4, 3]
      }
    ]
  },
  half_year: {
    xAxis: ['1月', '2月', '3月', '4月', '5月', '6月'],
    series: [
      {
        name: '总数',
        data: [45, 38, 32, 28, 22, 18]
      },
      {
        name: '一级报警',
        data: [20, 16, 14, 12, 10, 8]
      },
      {
        name: '二级报警',
        data: [15, 13, 11, 9, 7, 6]
      },
      {
        name: '三级报警',
        data: [10, 9, 7, 7, 5, 4]
      }
    ]
  }
}

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return

  chartInstance = echarts.init(chartRef.value)
  
  const option = {
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      borderColor: 'rgba(0, 242, 241, 0.5)',
      textStyle: {
        color: '#FFFFFF',
        fontSize: 12
      }
    },
    legend: {
      top: '5%',
      right: '5%',
      textStyle: {
        color: '#FFFFFF',
        fontSize: 12
      },
      itemWidth: 20,
      itemHeight: 2,
      icon: 'rect'
    },
    grid: {
      top: '25%',
      left: '8%',
      right: '5%',
      bottom: '10%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: chartData[timeRange.value].xAxis,
      axisLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.2)'
        }
      },
      axisLabel: {
        color: '#FFFFFF',
        fontSize: 10
      },
      axisTick: {
        show: false
      }
    },
    yAxis: {
      type: 'value',
      name: '单位（个）',
      nameTextStyle: {
        color: '#FFFFFF',
        fontSize: 10
      },
      axisLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.2)'
        }
      },
      axisLabel: {
        color: '#FFFFFF',
        fontSize: 10
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)',
          type: 'dashed'
        }
      },
      axisTick: {
        show: false
      }
    },
    series: [
      {
        name: '总数',
        type: 'line',
        data: chartData[timeRange.value].series[0].data,
        smooth: true,
        lineStyle: {
          color: '#3CF3FF',
          width: 2
        },
        itemStyle: {
          color: '#3CF3FF'
        },
        symbol: 'circle',
        symbolSize: 4
      },
      {
        name: '一级报警',
        type: 'line',
        data: chartData[timeRange.value].series[1].data,
        smooth: true,
        lineStyle: {
          color: '#FF6B6B',
          width: 2
        },
        itemStyle: {
          color: '#FF6B6B'
        },
        symbol: 'circle',
        symbolSize: 4
      },
      {
        name: '二级报警',
        type: 'line',
        data: chartData[timeRange.value].series[2].data,
        smooth: true,
        lineStyle: {
          color: '#FFB930',
          width: 2
        },
        itemStyle: {
          color: '#FFB930'
        },
        symbol: 'circle',
        symbolSize: 4
      },
      {
        name: '三级报警',
        type: 'line',
        data: chartData[timeRange.value].series[3].data,
        smooth: true,
        lineStyle: {
          color: '#3FD87C',
          width: 2
        },
        itemStyle: {
          color: '#3FD87C'
        },
        symbol: 'circle',
        symbolSize: 4
      }
    ]
  }

  chartInstance.setOption(option)
}

// 更新图表数据
const updateChart = (range) => {
  if (!chartInstance) return

  const option = {
    xAxis: {
      data: chartData[range].xAxis
    },
    series: chartData[range].series.map((item, index) => ({
      data: item.data
    }))
  }
  
  chartInstance.setOption(option)
}

// 处理时间范围变化
const handleTimeChange = (value) => {
  console.log('时间范围变更为:', value)
  updateChart(value)
}

// 图表自适应
const resizeChart = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

onMounted(() => {
  nextTick(() => {
    initChart()
    window.addEventListener('resize', resizeChart)
  })
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
  window.removeEventListener('resize', resizeChart)
})
</script>

<style scoped>
.panel-content {
  flex: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
}

.com-select {
  margin-right: 20px;
}

.chart-container {
  flex: 1;
  width: 100%;
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.trend-chart {
  width: 100%;
  height: 100%;
  min-height: 200px;
}

/* 响应式布局适配 */
@media screen and (min-width: 1920px) and (max-width: 2560px) {
  .panel-content {
    padding: 15px;
  }
  
  .chart-container {
    min-height: 220px;
  }
  
  .trend-chart {
    min-height: 220px;
  }
}

@media screen and (max-width: 1919px) {
  .panel-content {
    padding: 12px;
  }
  
  .chart-container {
    min-height: 180px;
  }
  
  .trend-chart {
    min-height: 180px;
  }
}

@media screen and (min-width: 2561px) {
  .panel-content {
    padding: 18px;
  }
  
  .chart-container {
    min-height: 240px;
  }
  
  .trend-chart {
    min-height: 240px;
  }
}

@media screen and (min-height: 940px) and (max-height: 1055px) {
  .panel-content {
    padding: 10px;
  }
  
  .chart-container {
    min-height: 160px;
  }
  
  .trend-chart {
    min-height: 160px;
  }
}

@media screen and (min-height: 900px) and (max-height: 940px) {
  .panel-content {
    padding: 8px;
  }
  
  .chart-container {
    min-height: 140px;
  }
  
  .trend-chart {
    min-height: 140px;
  }
}
</style> 