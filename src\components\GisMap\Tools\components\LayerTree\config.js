// 综合管理
export const comprehensiveLayerTreeList = [
  {
    id:"com_project",
    label:"综合专项",
    children:[
      {
        id: "com_warning_info",
        label: "预警信息",
        children: [
          {
            id: "com_warning_level1",
            label: "一级预警",
            icon: "",
            dataList: [],
          },
          {
            id: "com_warning_level2",
            label: "二级预警",
            icon: "",
            dataList: [],
          },
          {
            id: "com_warning_level3",
            label: "三级预警",
            icon: "",
            dataList: [],
          }
        ],
      },
      {
        id: "com_emergency_event",
        label: "应急事件",
        children: [
          {
            id: "com_gas_event",
            label: "燃气事件",
            icon: "",
            dataList: [],
          },
          {
            id: "com_drainage_event",
            label: "排水事件",
            icon: "",
            dataList: [],
          },
          {
            id: "com_heating_event",
            label: "供热事件",
            icon: "",
            dataList: [],
          },
          {
            id: "com_bridge_event",
            label: "桥梁事件",
            icon: "",
            dataList: [],
          }
        ],
      },
      {
        id: "com_emergency_resource",
        label: "应急资源",
        children: [
          {
            id: "com_shelter",
            label: "避难场所",
            icon: "",
            dataList: [],
          },
          {
            id: "com_emergency_team",
            label: "应急队伍",
            icon: "",
            dataList: [],
          },
          {
            id: "com_emergency_material",
            label: "应急物资",
            icon: "",
            dataList: [],
          },
          {
            id: "com_rescue_personnel",
            label: "救援人员",
            icon: "",
            dataList: [],
          },
          {
            id: "com_medical_institution",
            label: "医疗机构",
            icon: "",
            dataList: [],
          },
          {
            id: "com_emergency_warehouse",
            label: "应急仓库",
            icon: "",
            dataList: [],
          }
        ],
      },
      {
        id: "com_hidden_danger",
        label: "隐患排查",
        children: [
          {
            id: "com_gas_hidden",
            label: "燃气隐患",
            icon: "",
            dataList: [],
          },
          {
            id: "com_drainage_hidden",
            label: "排水隐患",
            icon: "",
            dataList: [],
          },
          {
            id: "com_heating_hidden",
            label: "供热隐患",
            icon: "",
            dataList: [],
          },
          {
            id: "com_bridge_hidden",
            label: "桥梁隐患",
            icon: "",
            dataList: [],
          }
        ],
      }
    ]
  },
  {
    id: "gas_project",
    label: "燃气专项",
    children:[
      {
        id: "gas_infrastructure",
        label: "基础设施",
        children: [
          {
            id: "gas_pipeline",
            label: "燃气管线",
            children: [
              {
                id: "high_pressure_gas_pipeline",
                label: "高压管线",
                icon: "",
                dataList: [],
              },
              {
                id: "mid_pressure_gas_pipeline",
                label: "中压管线",
                icon: "",
                dataList: [],
              },
              {
                id: "low_pressure_gas_pipeline",
                label: "低压管线",
                icon: "",
                dataList: [],
              }
            ],
          },
          {
            id: "gas_station",
            label: "场站",
            icon: "",
            dataList: [],
          },
          {
            id: "gas_pipeline_point",
            label: "管点",
            icon: "",
            dataList: [],
          },
          {
            id: "gas_well",
            label: "窨井",
            icon: "",
            dataList: [],
          },
          {
            id: "gas_dangerous_source",
            label: "危险源",
            icon: "",
            dataList: [],
          },
          {
            id: "gas_protection_target",
            label: "防护目标",
            icon: "",
            dataList: [],
          }
        ],
      },
      {
        id: "gas_pipeline_risk",
        label: "管线风险",
        children: [
          {
            id: "gas_pipeline_risk1",
            label: "重大风险",
            icon: "",
            dataList: [],
          },
          {
            id: "gas_pipeline_risk2",
            label: "较大风险",
            icon: "",
            dataList: [],
          },
          {
            id: "gas_pipeline_risk3",
            label: "一般风险",
            icon: "",
            dataList: [],
          },
          {
            id: "gas_pipeline_risk4",
            label: "低风险",
            icon: "",
            dataList: [],
          }
        ],
      },
      {
        id: "gas_station_risk",
        label: "场站风险",
        children: [
          {
            id: "gas_station_risk1",
            label: "重大风险",
            icon: "",
            dataList: [],
          },
          {
            id: "gas_station_risk2",
            label: "较大风险",
            icon: "",
            dataList: [],
          },
          {
            id: "gas_station_risk3",
            label: "一般风险",
            icon: "",
            dataList: [],
          },
          {
            id: "gas_station_risk4",
            label: "低风险",
            icon: "",
            dataList: [],
          }
        ],
      },
      {
        id: "gas_monitor_device",
        label: "监测设备",
        children: [
          {
            id: "gas_flowmeter",
            label: "流量监测",
            icon: "",
            dataList: [],
          },
          {
            id: "gas_manometer",
            label: "压力监测",
            icon: "",
            dataList: [],
          },
          {
            id: "gas_combustible",
            label: "可燃气体监测",
            icon: "",
            dataList: [],
          },
          {
            id: "gas_temperature",
            label: "温度监测",
            icon: "",
            dataList: [],
          },
          {
            id: "gas_manhole_cover",
            label: "井盖位移监测",
            icon: "",
            dataList: [],
          },
          {
            id: "gas_video",
            label: "视频监控",
            icon: "",
            dataList: [],
          },
        ],
      },
      {
        id: "gas_monitor_alarm",
        label: "报警信息",
        children: [
          {
            id: "gas_alarm1",
            label: "一级报警",
            icon: "",
            dataList: [],
          },
          {
            id: "gas_alarm2",
            label: "二级报警",
            icon: "",
            dataList: [],
          },
          {
            id: "gas_alarm3",
            label: "三级报警",
            icon: "",
            dataList: [],
          }
        ],
      }
    ]
  },
  {
    id: "drainage_project",
    label: "排水专项",
    children:[
      {
        id: "drainage_infrastructure",
        label: "基础设施",
        children: [
          {
            id: "drainage_pipeline",
            label: "排水管线",
            children: [
              {
                id: "drainage_rain_pipeline",
                label: "雨水管线",
                icon: "",
                dataList: [],
              },
              {
                id: "drainage_sewage_pipeline",
                label: "污水管线",
                icon: "",
                dataList: [],
              },
            ],
          },
          {
            id: "drainage_pump_station",
            label: "泵站",
            icon: "",
            dataList: [],
          },
          {
            id: "drainage_sewage_works",
            label: "污水厂",
            icon: "",
            dataList: [],
          },
          {
            id: "drainage_water_outlet",
            label: "排水口",
            icon: "",
            dataList: [],
          },
          {
            id: "drainage_pipeline_point",
            label: "管点",
            icon: "",
            dataList: [],
          },
          {
            id: "drainage_well",
            label: "窨井",
            icon: "",
            dataList: [],
          },
          {
            id: "drainage_flooding_point",
            label: "易涝点",
            icon: "",
            dataList: [],
          },
          {
            id: "drainage_dangerous_source",
            label: "危险源",
            icon: "",
            dataList: [],
          },
          {
            id: "drainage_protection_target",
            label: "防护目标",
            icon: "",
            dataList: [],
          },
        ],
      },
      {
        id: "drainage_pipeline_risk",
        label: "管线风险",
        children: [
          {
            id: "drainage_pipeline_risk1",
            label: "重大风险",
            icon: "",
            dataList: [],
          },
          {
            id: "drainage_pipeline_risk2",
            label: "较大风险",
            icon: "",
            dataList: [],
          },
          {
            id: "drainage_pipeline_risk3",
            label: "一般风险",
            icon: "",
            dataList: [],
          },
          {
            id: "drainage_pipeline_risk4",
            label: "低风险",
            icon: "",
            dataList: [],
          }
        ],
      },
      {
        id: "drainage_sewage_risk",
        label: "污水厂风险",
        children: [
          {
            id: "drainage_sewage_risk1",
            label: "重大风险",
            icon: "",
            dataList: [],
          },
          {
            id: "drainage_sewage_risk2",
            label: "较大风险",
            icon: "",
            dataList: [],
          },
          {
            id: "drainage_sewage_risk3",
            label: "一般风险",
            icon: "",
            dataList: [],
          },
          {
            id: "drainage_sewage_risk4",
            label: "低风险",
            icon: "",
            dataList: [],
          }
        ],
      },
      {
        id: "drainage_pump_risk",
        label: "泵站风险",
        children: [
          {
            id: "drainage_pump_risk1",
            label: "重大风险",
            icon: "",
            dataList: [],
          },
          {
            id: "drainage_pump_risk2",
            label: "较大风险",
            icon: "",
            dataList: [],
          },
          {
            id: "drainage_pump_risk3",
            label: "一般风险",
            icon: "",
            dataList: [],
          },
          {
            id: "drainage_pump_risk4",
            label: "低风险",
            icon: "",
            dataList: [],
          }
        ],
      },
      {
        id: "drainage_hidden_risk",
        label: "隐患排查",
        children: [
          {
            id: "drainage_hidden_risk1",
            label: "重大隐患",
            icon: "",
            dataList: [],
          },
          {
            id: "drainage_hidden_risk2",
            label: "较大隐患",
            icon: "",
            dataList: [],
          },
          {
            id: "drainage_hidden_risk3",
            label: "一般隐患",
            icon: "",
            dataList: [],
          },
        ],
      },
      {
        id: "drainage_monitor_device",
        label: "监测设备",
        children: [
          {
            id: "drainage_level",
            label: "液位计",
            icon: "",
            dataList: [],
          },
          {
            id: "drainage_flowmeter",
            label: "流量计",
            icon: "",
            dataList: [],
          },
          /*  {
              id: "drainage_rain",
              label: "雨量计",
              icon: "",
              dataList: [],
            },*/
          {
            id: "drainage_water_quality",
            label: "水质监测仪",
            icon: "",
            dataList: [],
          },
          {
            id: "drainage_combustible",
            label: "可燃气体监测",
            icon: "",
            dataList: [],
          },
          {
            id: "drainage_manhole_cover",
            label: "井盖位移监测",
            icon: "",
            dataList: [],
          },
          {
            id: "drainage_video",
            label: "视频监控",
            icon: "",
            dataList: [],
          },
        ],
      },
      {
        id: "drainage_monitor_alarm",
        label: "报警信息",
        children: [
          {
            id: "drainage_alarm1",
            label: "一级报警",
            icon: "",
            dataList: [],
          },
          {
            id: "drainage_alarm2",
            label: "二级报警",
            icon: "",
            dataList: [],
          },
          {
            id: "drainage_alarm3",
            label: "三级报警",
            icon: "",
            dataList: [],
          }
        ],
      }
    ]
  },
  {
    id: "heating_project",
    label: "供热专项",
    children: [
      {
        id: "heating_infrastructure",
        label: "基础设施",
        children: [
          {
            id: "heating_pipeline",
            label: "供热管线",
            children: [
              {
                id: "heating_pipeline_1",
                label: "一次网",
                icon: "",
                dataList: [],
              },
              {
                id: "heating_pipeline_2",
                label: "二次网",
                icon: "",
                dataList: [],
              },
            ],
          },
          {
            id: "heating_pipeline_point",
            label: "管点",
            icon: "",
            dataList: [],
          },
          {
            id: "heating_well",
            label: "窨井",
            icon: "",
            dataList: [],
          },
          {
            id: "heating_enterprise",
            label: "供热企业",
            icon: "",
            dataList: [],
          },
          {
            id: "heating_source_works",
            label: "热源厂",
            icon: "",
            dataList: [],
          },
          {
            id: "heating_station",
            label: "换热站",
            icon: "",
            dataList: [],
          },
          {
            id: "heating_user",
            label: "供热用户",
            icon: "",
            dataList: [],
          },
          {
            id: "heating_dangerous_source",
            label: "危险源",
            icon: "",
            dataList: [],
          },
          {
            id: "heating_protection_target",
            label: "防护目标",
            icon: "",
            dataList: [],
          },
        ],
      },
      {
        id: "heating_pipeline_risk",
        label: "管线风险",
        children: [
          {
            id: "heating_pipeline_risk1",
            label: "重大风险",
            icon: "",
            dataList: [],
          },
          {
            id: "heating_pipeline_risk2",
            label: "较大风险",
            icon: "",
            dataList: [],
          },
          {
            id: "heating_pipeline_risk3",
            label: "一般风险",
            icon: "",
            dataList: [],
          },
          {
            id: "heating_pipeline_risk4",
            label: "低风险",
            icon: "",
            dataList: [],
          }
        ],
      },
      {
        id: "heating_source_risk",
        label: "热源风险",
        children: [
          {
            id: "heating_source_risk1",
            label: "重大风险",
            icon: "",
            dataList: [],
          },
          {
            id: "heating_source_risk2",
            label: "较大风险",
            icon: "",
            dataList: [],
          },
          {
            id: "heating_source_risk3",
            label: "一般风险",
            icon: "",
            dataList: [],
          },
          {
            id: "heating_source_risk4",
            label: "低风险",
            icon: "",
            dataList: [],
          }
        ],
      },
      {
        id: "heating_station_risk",
        label: "换热站风险",
        children: [
          {
            id: "heating_station_risk1",
            label: "重大风险",
            icon: "",
            dataList: [],
          },
          {
            id: "heating_station_risk2",
            label: "较大风险",
            icon: "",
            dataList: [],
          },
          {
            id: "heating_station_risk3",
            label: "一般风险",
            icon: "",
            dataList: [],
          },
          {
            id: "heating_station_risk4",
            label: "低风险",
            icon: "",
            dataList: [],
          }
        ],
      },
      {
        id: "heating_hidden_risk",
        label: "隐患排查",
        children: [
          {
            id: "heating_hidden_risk1",
            label: "重大隐患",
            icon: "",
            dataList: [],
          },
          {
            id: "heating_hidden_risk2",
            label: "较大隐患",
            icon: "",
            dataList: [],
          },
          {
            id: "heating_hidden_risk3",
            label: "一般隐患",
            icon: "",
            dataList: [],
          },
        ],
      },
      {
        id: "heating_monitor_device",
        label: "监测设备",
        children: [
          /*  {
              id: "heating_level",
              label: "液位计",
              icon: "",
              dataList: [],
            },*/
          {
            id: "heating_flowmeter",
            label: "流量计",
            icon: "",
            dataList: [],
          },
          /*  {
              id: "heating_rain",
              label: "雨量计",
              icon: "",
              dataList: [],
            },*/
          /* {
             id: "heating_water_quality",
             label: "水质监测仪",
             icon: "",
             dataList: [],
           },*/
          {
            id: "heating_combustible",
            label: "可燃气体监测",
            icon: "",
            dataList: [],
          },
          {
            id: "heating_temperature",
            label: "温度监测",
            icon: "",
            dataList: [],
          },
          {
            id: "heating_manhole_cover",
            label: "井盖位移监测",
            icon: "",
            dataList: [],
          },
          {
            id: "heating_video",
            label: "视频监控",
            icon: "",
            dataList: [],
          },
        ],
      },
      {
        id: "heating_monitor_alarm",
        label: "报警信息",
        children: [
          {
            id: "heating_alarm1",
            label: "一级报警",
            icon: "",
            dataList: [],
          },
          {
            id: "heating_alarm2",
            label: "二级报警",
            icon: "",
            dataList: [],
          },
          {
            id: "heating_alarm3",
            label: "三级报警",
            icon: "",
            dataList: [],
          }
        ],
      }
    ]
  },
  {
    id: "bridge_project",
    label: "桥梁专项",
    children:[
      {
        id: "bridge_infrastructure",
        label: "基础设施",
        children: [
          {
            id: "bridge_info",
            label: "桥梁",
            icon: "",
            dataList: [],
          },
          {
            id: "bridge_safety_rating",
            label: "安全评分",
            icon: "",
            dataList: [],
          },
        ],
      },
      {
        id: "bridge_monitor_device",
        label: "监测设备",
        children: [
          {
            id: "bridge_wind_speed",
            label: "风速仪",
            icon: "",
            dataList: [],
          },
          {
            id: "bridge_static_level",
            label: "静力水准仪",
            icon: "",
            dataList: [],
          },
          {
            id: "bridge_temperature",
            label: "温度传感器",
            icon: "",
            dataList: [],
          },
          {
            id: "bridge_humidity",
            label: "湿度传感器",
            icon: "",
            dataList: [],
          },
          {
            id: "bridge_vibration",
            label: "三向加速度传感器",
            icon: "",
            dataList: [],
          },
          {
            id: "bridge_dynamic_weight",
            label: "动态称重系统",
            icon: "",
            dataList: [],
          },
          {
            id: "bridge_displacement",
            label: "位移计",
            icon: "",
            dataList: [],
          },
          {
            id: "bridge_tilt",
            label: "倾角传感器",
            icon: "",
            dataList: [],
          },
          {
            id: "bridge_deflection",
            label: "挠度仪",
            icon: "",
            dataList: [],
          },
          {
            id: "bridge_strain",
            label: "应变传感器",
            icon: "",
            dataList: [],
          },
          {
            id: "bridge_load_cell",
            label: "测力支座",
            icon: "",
            dataList: [],
          },
          {
            id: "bridge_acceleration",
            label: "加速度传感器",
            icon: "",
            dataList: [],
          },
          {
            id: "bridge_crack",
            label: "车船撞击传感器",
            icon: "",
            dataList: [],
          },
          {
            id: "bridge_tilt_alarm",
            label: "梁体偏位报警器",
            icon: "",
            dataList: [],
          },
          {
            id: "bridge_crack_sensor",
            label: "裂缝传感器",
            icon: "",
            dataList: [],
          },
          {
            id: "bridge_vibration_sensor",
            label: "测振仪",
            icon: "",
            dataList: [],
          },
          {
            id: "bridge_video",
            label: "视频监控",
            icon: "",
            dataList: [],
          },
        ],
      },
      /*  {
          id: "bridge_diseases",
          label: "病害",
          children: [
            {
              id: "bridge_diseases1",
              label: "病害等级一",
              icon: "",
              dataList: [],
            },
            {
              id: "bridge_diseases2",
              label: "病害等级二",
              icon: "",
              dataList: [],
            },
            {
              id: "bridge_diseases3",
              label: "病害等级三",
              icon: "",
              dataList: [],
            },
          ],
        },*/
    ]
  }
];

//燃气专项
export const gasLayerTreeList = [
  {
    id: "gas_infrastructure",
    label: "基础设施",
    children: [
      {
        id: "gas_pipeline",
        label: "燃气管线",
        children: [
          {
            id: "high_pressure_gas_pipeline",
            label: "高压管线",
            icon: "",
            dataList: [],
          },
          {
            id: "mid_pressure_gas_pipeline",
            label: "中压管线",
            icon: "",
            dataList: [],
          },
          {
            id: "low_pressure_gas_pipeline",
            label: "低压管线",
            icon: "",
            dataList: [],
          }
        ],
      },
      {
        id: "gas_station",
        label: "场站",
        icon: "",
        dataList: [],
      },
      {
        id: "gas_pipeline_point",
        label: "管点",
        icon: "",
        dataList: [],
      },
      {
        id: "gas_well",
        label: "窨井",
        icon: "",
        dataList: [],
      },
      {
        id: "gas_dangerous_source",
        label: "危险源",
        icon: "",
        dataList: [],
      },
      {
        id: "gas_protection_target",
        label: "防护目标",
        icon: "",
        dataList: [],
      }
    ],
  },
  {
    id: "gas_pipeline_risk",
    label: "管线风险",
    children: [
      {
        id: "gas_pipeline_risk1",
        label: "重大风险",
        icon: "",
        dataList: [],
      },
      {
        id: "gas_pipeline_risk2",
        label: "较大风险",
        icon: "",
        dataList: [],
      },
      {
        id: "gas_pipeline_risk3",
        label: "一般风险",
        icon: "",
        dataList: [],
      },
      {
        id: "gas_pipeline_risk4",
        label: "低风险",
        icon: "",
        dataList: [],
      }
    ],
  },
  {
    id: "gas_station_risk",
    label: "场站风险",
    children: [
      {
        id: "gas_station_risk1",
        label: "重大风险",
        icon: "",
        dataList: [],
      },
      {
        id: "gas_station_risk2",
        label: "较大风险",
        icon: "",
        dataList: [],
      },
      {
        id: "gas_station_risk3",
        label: "一般风险",
        icon: "",
        dataList: [],
      },
      {
        id: "gas_station_risk4",
        label: "低风险",
        icon: "",
        dataList: [],
      }
    ],
  },
  {
    id: "gas_monitor_device",
    label: "监测设备",
    children: [
      {
        id: "gas_flowmeter",
        label: "流量监测",
        icon: "",
        dataList: [],
      },
      {
        id: "gas_manometer",
        label: "压力监测",
        icon: "",
        dataList: [],
      },
      {
        id: "gas_combustible",
        label: "可燃气体监测",
        icon: "",
        dataList: [],
      },
      {
        id: "gas_temperature",
        label: "温度监测",
        icon: "",
        dataList: [],
      },
      {
        id: "gas_manhole_cover",
        label: "井盖位移监测",
        icon: "",
        dataList: [],
      },
      {
        id: "gas_video",
        label: "视频监控",
        icon: "",
        dataList: [],
      },
    ],
  },
  {
    id: "gas_monitor_alarm",
    label: "报警信息",
    children: [
      {
        id: "gas_alarm1",
        label: "一级报警",
        icon: "",
        dataList: [],
      },
      {
        id: "gas_alarm2",
        label: "二级报警",
        icon: "",
        dataList: [],
      },
      {
        id: "gas_alarm3",
        label: "三级报警",
        icon: "",
        dataList: [],
      }
    ],
  }
];

//排水专项
export const drainageLayerTreeList = [
  {
    id: "drainage_infrastructure",
    label: "基础设施",
    children: [
      {
        id: "drainage_pipeline",
        label: "排水管线",
        children: [
          {
            id: "drainage_rain_pipeline",
            label: "雨水管线",
            icon: "",
            dataList: [],
          },
          {
            id: "drainage_sewage_pipeline",
            label: "污水管线",
            icon: "",
            dataList: [],
          },
          {
            id: "drainage_rainAndSewage_pipeline",
            label: "雨污合流管线",
            icon: "",
            dataList: [],
          },
        ],
      },
      {
        id: "drainage_pump_station",
        label: "泵站",
        icon: "",
        dataList: [],
      },
      {
        id: "drainage_sewage_works",
        label: "污水厂",
        icon: "",
        dataList: [],
      },
      {
        id: "drainage_water_outlet",
        label: "排水口",
        icon: "",
        dataList: [],
      },
      {
        id: "drainage_pipeline_point",
        label: "管点",
        icon: "",
        dataList: [],
      },
      {
        id: "drainage_well",
        label: "窨井",
        icon: "",
        dataList: [],
      },
      {
        id: "drainage_flooding_point",
        label: "易涝点",
        icon: "",
        dataList: [],
      },
      {
        id: "drainage_dangerous_source",
        label: "危险源",
        icon: "",
        dataList: [],
      },
      {
        id: "drainage_protection_target",
        label: "防护目标",
        icon: "",
        dataList: [],
      },
    ],
  },
  {
    id: "drainage_pipeline_risk",
    label: "管线风险",
    children: [
      {
        id: "drainage_pipeline_risk1",
        label: "重大风险",
        icon: "",
        dataList: [],
      },
      {
        id: "drainage_pipeline_risk2",
        label: "较大风险",
        icon: "",
        dataList: [],
      },
      {
        id: "drainage_pipeline_risk3",
        label: "一般风险",
        icon: "",
        dataList: [],
      },
      {
        id: "drainage_pipeline_risk4",
        label: "低风险",
        icon: "",
        dataList: [],
      }
    ],
  },
  {
    id: "drainage_sewage_risk",
    label: "污水厂风险",
    children: [
      {
        id: "drainage_sewage_risk1",
        label: "重大风险",
        icon: "",
        dataList: [],
      },
      {
        id: "drainage_sewage_risk2",
        label: "较大风险",
        icon: "",
        dataList: [],
      },
      {
        id: "drainage_sewage_risk3",
        label: "一般风险",
        icon: "",
        dataList: [],
      },
      {
        id: "drainage_sewage_risk4",
        label: "低风险",
        icon: "",
        dataList: [],
      }
    ],
  },
  {
    id: "drainage_pump_risk",
    label: "泵站风险",
    children: [
      {
        id: "drainage_pump_risk1",
        label: "重大风险",
        icon: "",
        dataList: [],
      },
      {
        id: "drainage_pump_risk2",
        label: "较大风险",
        icon: "",
        dataList: [],
      },
      {
        id: "drainage_pump_risk3",
        label: "一般风险",
        icon: "",
        dataList: [],
      },
      {
        id: "drainage_pump_risk4",
        label: "低风险",
        icon: "",
        dataList: [],
      }
    ],
  },
  {
    id: "drainage_hidden_risk",
    label: "隐患排查",
    children: [
      {
        id: "drainage_hidden_risk1",
        label: "重大隐患",
        icon: "",
        dataList: [],
      },
      {
        id: "drainage_hidden_risk2",
        label: "较大隐患",
        icon: "",
        dataList: [],
      },
      {
        id: "drainage_hidden_risk3",
        label: "一般隐患",
        icon: "",
        dataList: [],
      },
    ],
  },
  {
    id: "drainage_monitor_device",
    label: "监测设备",
    children: [
      {
        id: "drainage_level",
        label: "液位计",
        icon: "",
        dataList: [],
      },
      {
        id: "drainage_flowmeter",
        label: "流量计",
        icon: "",
        dataList: [],
      },
    /*  {
        id: "drainage_rain",
        label: "雨量计",
        icon: "",
        dataList: [],
      },*/
      {
        id: "drainage_water_quality",
        label: "水质监测仪",
        icon: "",
        dataList: [],
      },
      {
        id: "drainage_combustible",
        label: "可燃气体监测",
        icon: "",
        dataList: [],
      },
      {
        id: "drainage_manhole_cover",
        label: "井盖位移监测",
        icon: "",
        dataList: [],
      },
      {
        id: "drainage_video",
        label: "视频监控",
        icon: "",
        dataList: [],
      },
    ],
  },
  {
    id: "drainage_monitor_alarm",
    label: "报警信息",
    children: [
      {
        id: "drainage_alarm1",
        label: "一级报警",
        icon: "",
        dataList: [],
      },
      {
        id: "drainage_alarm2",
        label: "二级报警",
        icon: "",
        dataList: [],
      },
      {
        id: "drainage_alarm3",
        label: "三级报警",
        icon: "",
        dataList: [],
      }
    ],
  }
];

//供热专项
export const heatingLayerTreeList = [
  {
    id: "heating_infrastructure",
    label: "基础设施",
    children: [
      {
        id: "heating_pipeline",
        label: "供热管线",
        children: [
          {
            id: "heating_pipeline_1",
            label: "一次网",
            icon: "",
            dataList: [],
          },
          {
            id: "heating_pipeline_2",
            label: "二次网",
            icon: "",
            dataList: [],
          },
        ],
      },
      {
        id: "heating_pipeline_point",
        label: "管点",
        icon: "",
        dataList: [],
      },
      {
        id: "heating_well",
        label: "窨井",
        icon: "",
        dataList: [],
      },
      {
        id: "heating_enterprise",
        label: "供热企业",
        icon: "",
        dataList: [],
      },
      {
        id: "heating_source_works",
        label: "热源厂",
        icon: "",
        dataList: [],
      },
      {
        id: "heating_station",
        label: "换热站",
        icon: "",
        dataList: [],
      },
      {
        id: "heating_user",
        label: "供热用户",
        icon: "",
        dataList: [],
      },
      {
        id: "heating_dangerous_source",
        label: "危险源",
        icon: "",
        dataList: [],
      },
      {
        id: "heating_protection_target",
        label: "防护目标",
        icon: "",
        dataList: [],
      },
    ],
  },
  {
    id: "heating_pipeline_risk",
    label: "管线风险",
    children: [
      {
        id: "heating_pipeline_risk1",
        label: "重大风险",
        icon: "",
        dataList: [],
      },
      {
        id: "heating_pipeline_risk2",
        label: "较大风险",
        icon: "",
        dataList: [],
      },
      {
        id: "heating_pipeline_risk3",
        label: "一般风险",
        icon: "",
        dataList: [],
      },
      {
        id: "heating_pipeline_risk4",
        label: "低风险",
        icon: "",
        dataList: [],
      }
    ],
  },
  {
    id: "heating_source_risk",
    label: "热源风险",
    children: [
      {
        id: "heating_source_risk1",
        label: "重大风险",
        icon: "",
        dataList: [],
      },
      {
        id: "heating_source_risk2",
        label: "较大风险",
        icon: "",
        dataList: [],
      },
      {
        id: "heating_source_risk3",
        label: "一般风险",
        icon: "",
        dataList: [],
      },
      {
        id: "heating_source_risk4",
        label: "低风险",
        icon: "",
        dataList: [],
      }
    ],
  },
  {
    id: "heating_station_risk",
    label: "换热站风险",
    children: [
      {
        id: "heating_station_risk1",
        label: "重大风险",
        icon: "",
        dataList: [],
      },
      {
        id: "heating_station_risk2",
        label: "较大风险",
        icon: "",
        dataList: [],
      },
      {
        id: "heating_station_risk3",
        label: "一般风险",
        icon: "",
        dataList: [],
      },
      {
        id: "heating_station_risk4",
        label: "低风险",
        icon: "",
        dataList: [],
      }
    ],
  },
  {
    id: "heating_hidden_risk",
    label: "隐患排查",
    children: [
      {
        id: "heating_hidden_risk1",
        label: "重大隐患",
        icon: "",
        dataList: [],
      },
      {
        id: "heating_hidden_risk2",
        label: "较大隐患",
        icon: "",
        dataList: [],
      },
      {
        id: "heating_hidden_risk3",
        label: "一般隐患",
        icon: "",
        dataList: [],
      },
    ],
  },
  {
    id: "heating_monitor_device",
    label: "监测设备",
    children: [
    /*  {
        id: "heating_level",
        label: "液位计",
        icon: "",
        dataList: [],
      },*/
      {
        id: "heating_flowmeter",
        label: "流量计",
        icon: "",
        dataList: [],
      },
    /*  {
        id: "heating_rain",
        label: "雨量计",
        icon: "",
        dataList: [],
      },*/
     /* {
        id: "heating_water_quality",
        label: "水质监测仪",
        icon: "",
        dataList: [],
      },*/
      {
        id: "heating_combustible",
        label: "可燃气体监测",
        icon: "",
        dataList: [],
      },
      {
        id: "heating_temperature",
        label: "温度监测",
        icon: "",
        dataList: [],
      },
      {
        id: "heating_manhole_cover",
        label: "井盖位移监测",
        icon: "",
        dataList: [],
      },
      {
        id: "heating_video",
        label: "视频监控",
        icon: "",
        dataList: [],
      },
    ],
  },
  {
    id: "heating_monitor_alarm",
    label: "报警信息",
    children: [
      {
        id: "heating_alarm1",
        label: "一级报警",
        icon: "",
        dataList: [],
      },
      {
        id: "heating_alarm2",
        label: "二级报警",
        icon: "",
        dataList: [],
      },
      {
        id: "heating_alarm3",
        label: "三级报警",
        icon: "",
        dataList: [],
      }
    ],
  }
];

//桥梁专项
export const bridgeLayerTreeList = [
  {
    id: "bridge_infrastructure",
    label: "基础设施",
    children: [
      {
        id: "bridge_info",
        label: "桥梁",
        icon: "",
        dataList: [],
      },
      {
        id: "bridge_safety_rating",
        label: "安全评分",
        icon: "",
        dataList: [],
      },
    ],
  },
  {
    id: "bridge_monitor_device",
    label: "监测设备",
    children: [
      {
        id: "bridge_wind_speed",
        label: "风速仪",
        icon: "",
        dataList: [],
      },
      {
        id: "bridge_static_level",
        label: "静力水准仪",
        icon: "",
        dataList: [],
      },
      {
        id: "bridge_temperature",
        label: "温度传感器",
        icon: "",
        dataList: [],
      },
      {
        id: "bridge_humidity",
        label: "湿度传感器",
        icon: "",
        dataList: [],
      },
      {
        id: "bridge_vibration",
        label: "三向加速度传感器",
        icon: "",
        dataList: [],
      },
      {
        id: "bridge_dynamic_weight",
        label: "动态称重系统",
        icon: "",
        dataList: [],
      },
      {
        id: "bridge_displacement",
        label: "位移计",
        icon: "",
        dataList: [],
      },
      {
        id: "bridge_tilt",
        label: "倾角传感器",
        icon: "",
        dataList: [],
      },
      {
        id: "bridge_deflection",
        label: "挠度仪",
        icon: "",
        dataList: [],
      },
      {
        id: "bridge_strain",
        label: "应变传感器",
        icon: "",
        dataList: [],
      },
      {
        id: "bridge_load_cell",
        label: "测力支座",
        icon: "",
        dataList: [],
      },
      {
        id: "bridge_acceleration",
        label: "加速度传感器",
        icon: "",
        dataList: [],
      },
      {
        id: "bridge_crack",
        label: "车船撞击传感器",
        icon: "",
        dataList: [],
      },
      {
        id: "bridge_tilt_alarm",
        label: "梁体偏位报警器",
        icon: "",
        dataList: [],
      },
      {
        id: "bridge_crack_sensor",
        label: "裂缝传感器",
        icon: "",
        dataList: [],
      },
      {
        id: "bridge_vibration_sensor",
        label: "测振仪",
        icon: "",
        dataList: [],
      },
      {
        id: "bridge_video",
        label: "视频监控",
        icon: "",
        dataList: [],
      },
    ],
  },
  {
    id: "bridge_monitor_alarm",
    label: "报警信息",
    children: [
      {
        id: "bridge_alarm1",
        label: "一级报警",
        icon: "",
        dataList: [],
      },
      {
        id: "bridge_alarm2",
        label: "二级报警",
        icon: "",
        dataList: [],
      },
      {
        id: "bridge_alarm3",
        label: "三级报警",
        icon: "",
        dataList: [],
      }
    ],
  }
/*  {
    id: "bridge_diseases",
    label: "病害",
    children: [
      {
        id: "bridge_diseases1",
        label: "病害等级一",
        icon: "",
        dataList: [],
      },
      {
        id: "bridge_diseases2",
        label: "病害等级二",
        icon: "",
        dataList: [],
      },
      {
        id: "bridge_diseases3",
        label: "病害等级三",
        icon: "",
        dataList: [],
      },
    ],
  },*/
];

export const layerTreeListMap= {
  "/comprehensive/overview": comprehensiveLayerTreeList, // 综合管理-总览
  "/comprehensive/risk": comprehensiveLayerTreeList, // 综合管理-风险隐患
  "/comprehensive/monitoring": comprehensiveLayerTreeList, // 综合管理-运行监测
  "/comprehensive/coordination": comprehensiveLayerTreeList, // 综合管理-协同指挥
  "/comprehensive/emergency": comprehensiveLayerTreeList, // 综合管理-应急辅助决策
  "/gas/overview": gasLayerTreeList, // 燃气管理-总览
  "/gas/network-risk": gasLayerTreeList, // 燃气管理-管线风险
  "/gas/monitoring": gasLayerTreeList, // 燃气管理-运行监测
  "/gas/decision-screen": gasLayerTreeList, // 燃气管理-辅助决策
  "/drainage/overview": drainageLayerTreeList, // 排水管理-总览
  "/drainage/risk": drainageLayerTreeList, // 排水管理-风险隐患
  "/drainage/flooding-risk": drainageLayerTreeList, // 排水管理-易涝点风险
  "/drainage/monitoring": drainageLayerTreeList, // 排水管理-运行监测
  "/drainage/decision": drainageLayerTreeList, // 排水管理-辅助决策
  "/heating/overview": heatingLayerTreeList, // 供热管理-总览
  "/heating/risk": heatingLayerTreeList, // 供热管理-风险隐患
  "/heating/monitoring": heatingLayerTreeList, // 供热管理-运行监测
  "/heating/decision": heatingLayerTreeList, // 供热管理-辅助决策
  "/bridge": bridgeLayerTreeList, // 桥梁管理-总览
  "/bridge/overview": bridgeLayerTreeList, // 桥梁管理-总览
  "/bridge/risk": bridgeLayerTreeList, // 桥梁管理-风险隐患
  "/bridge/monitoring": bridgeLayerTreeList, // 桥梁管理-运行监测
  "/bridge/decision": bridgeLayerTreeList, // 桥梁管理-辅助决策
};
