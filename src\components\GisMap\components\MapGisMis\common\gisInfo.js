import {
    postHeatUsmRiskHiddenDangerList,
    postHeatUsmRiskPipelineList,
    postHeatUsmRiskProtectList,
    postHeatUsmRiskStationList,
    postUsmVideoStreamPointList,
    postUsmZyGasRiskPipelineList,
    postUsmZyGasRiskStationList
} from "@/api/layerData.js";

export const layerQueryInfo = {
    mis_gas_station_risk: [
        "gas_station_risk1",
        "gas_station_risk2",
        "gas_station_risk3",
        "gas_station_risk4",
    ],
    mis_gas_pipeline_risk: [
        "gas_pipeline_risk1",
        "gas_pipeline_risk2",
        "gas_pipeline_risk3",
        "gas_pipeline_risk4",
    ],
    mis_gas_monitor_video: [
        "gas_video1",
        "gas_video0",
    ],
    mis_drainage_support_flood: [
        "drainage_rain_info",
        "drainage_flood_info",
        "drainage_flood_video",
        "drainage_people_location",
        "drainage_flood_warning",
        "drainage_flood_materials",
    ],
    mis_heating_pipeline_risk: [
        "heating_pipeline_risk1",
        "heating_pipeline_risk2",
        "heating_pipeline_risk3",
        "heating_pipeline_risk4",
    ],
    mis_heating_station_risk: [
        "heating_station_risk1",
        "heating_station_risk2",
        "heating_station_risk3",
        "heating_station_risk4",
    ],
    mis_heating_hidden_risk: [
        "heating_hidden_risk1",
        "heating_hidden_risk2",
        "heating_hidden_risk3",
    ],
    mis_heating_protection:[
        "heating_protection1",
        "heating_protection2",
    ],
    mis_com_risk: [
        "mis_com_risk1",
        "mis_com_risk2",
        "mis_com_risk3",
        "mis_com_risk4",
    ],
};

export const requestMisDataMap = {
    gas_pipeline_risk1: {
        api: postUsmZyGasRiskPipelineList,
        params: {
            riskLevel: 7001, //重大风险
        },
    },
    gas_pipeline_risk2: {
        api: postUsmZyGasRiskPipelineList,
        params: {
            riskLevel: 7002, //较大风险
        },
    },
    gas_pipeline_risk3: {
        api: postUsmZyGasRiskPipelineList,
        params: {
            riskLevel: 7003, //一般风险
        },
    },
    gas_pipeline_risk4: {
        api: postUsmZyGasRiskPipelineList,
        params: {
            riskLevel: 7004, //低风险
        },
    },
    gas_station_risk1: {
        api: postUsmZyGasRiskStationList,
        params: {
            riskLevel: 7001, //重大风险
        },
    },
    gas_station_risk2: {
        api: postUsmZyGasRiskStationList,
        params: {
            riskLevel: 7002, //较大风险
        },
    },
    gas_station_risk3: {
        api: postUsmZyGasRiskStationList,
        params: {
            riskLevel: 7003, //一般风险
        },
    },
    gas_station_risk4: {
        api: postUsmZyGasRiskStationList,
        params: {
            riskLevel: 7004, //低风险
        },
    },
    gas_video1: {
        api: postUsmVideoStreamPointList,
        params: {
            online: 1, // 在线视频
        },
    },
    gas_video0: {
        api: postUsmVideoStreamPointList,
        params: {
            online: 0, // 离线视频
        },
    },
    heating_pipeline_risk1: {
        api: postHeatUsmRiskPipelineList,
        params: {
            riskLevel: 2002801, //重大风险
        },
    },
    heating_pipeline_risk2: {
        api: postHeatUsmRiskPipelineList,
        params: {
            riskLevel: 2002802, //较大风险
        },
    },
    heating_pipeline_risk3: {
        api: postHeatUsmRiskPipelineList,
        params: {
            riskLevel: 2002803, //一般风险
        },
    },
    heating_pipeline_risk4: {
        api: postHeatUsmRiskPipelineList,
        params: {
            riskLevel: 2002804, //低风险
        },
    },
    heating_station_risk1: {
        api: postHeatUsmRiskStationList,
        params: {
            riskLevel: 2002801, //重大风险
        },
    },
    heating_station_risk2: {
        api: postHeatUsmRiskStationList,
        params: {
            riskLevel: 2002802, //较大风险
        },
    },
    heating_station_risk3: {
        api: postHeatUsmRiskStationList,
        params: {
            riskLevel: 2002803, //一般风险
        },
    },
    heating_station_risk4: {
        api: postHeatUsmRiskStationList,
        params: {
            riskLevel: 2002804, //低风险
        },
    },
    heating_hidden_risk1: {
        api: postHeatUsmRiskHiddenDangerList,
        params: {
            dangerLevel: 2002401, //重大隐患
        },
    },
    heating_hidden_risk2: {
        api: postHeatUsmRiskHiddenDangerList,
        params: {
            dangerLevel: 2002402, //较大隐患
        },
    },
    heating_hidden_risk3: {
        api: postHeatUsmRiskHiddenDangerList,
        params: {
            dangerLevel: 2002403, //一般隐患
        },
    },
    heating_protection1: {
        api: postHeatUsmRiskProtectList,
        params: {
            isMajor: 0, // 非重点防护目标
        },
    },
    heating_protection2: {
        api: postHeatUsmRiskProtectList,
        params: {
            isMajor: 1, // 重点防护目标
        },
    },
};
