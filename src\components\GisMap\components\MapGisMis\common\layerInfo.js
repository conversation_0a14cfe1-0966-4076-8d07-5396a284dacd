
export const treeInfo = {
  mis_gas_station_risk: [
    {
      id: "gas_station_risk1",
      label: "重大风险",
      icon: "",
      dataList: [],
    },
    {
      id: "gas_station_risk2",
      label: "较大风险",
      icon: "",
      dataList: [],
    },
    {
      id: "gas_station_risk3",
      label: "一般风险",
      icon: "",
      dataList: [],
    },
    {
      id: "gas_station_risk4",
      label: "低风险",
      icon: "",
      dataList: [],
    }
  ],
  mis_gas_pipeline_risk: [
    {
      id: "gas_pipeline_risk1",
      label: "重大风险",
      icon: "",
      dataList: [],
    },
    {
      id: "gas_pipeline_risk2",
      label: "较大风险",
      icon: "",
      dataList: [],
    },
    {
      id: "gas_pipeline_risk3",
      label: "一般风险",
      icon: "",
      dataList: [],
    },
    {
      id: "gas_pipeline_risk4",
      label: "低风险",
      icon: "",
      dataList: [],
    }
  ],
  mis_gas_monitor_video: [
    {
      id: "gas_video1",
      label: "在线视频",
      icon: "",
      dataList: [],
    },
    {
      id: "gas_video0",
      label: "离线视频",
      icon: "",
      dataList: [],
    },
  ],
  mis_drainage_support_flood: [
    {
      id: "drainage_rain_info",
      label: "雨情信息",
      icon: "",
      dataList: [],
    },
    {
      id: "drainage_flood_info",
      label: "水情信息",
      icon: "",
      dataList: [],
    },
    {
      id: "drainage_flood_video",
      label: "视频监控",
      icon: "",
      dataList: [],
    },
    {
      id: "drainage_people_location",
      label: "人员定位",
      icon: "",
      dataList: [],
    },
    {
      id: "drainage_flood_warning",
      label: "报警信息",
      icon: "",
      dataList: [],
    },
    {
      id: "drainage_flood_materials",
      label: "防汛物资",
      icon: "",
      dataList: [],
    },
  ],
  mis_heating_pipeline_risk: [
    {
      id: "heating_pipeline_risk1",
      label: "重大风险",
      icon: "",
      dataList: [],
    },
    {
      id: "heating_pipeline_risk2",
      label: "较大风险",
      icon: "",
      dataList: [],
    },
    {
      id: "heating_pipeline_risk3",
      label: "一般风险",
      icon: "",
      dataList: [],
    },
    {
      id: "heating_pipeline_risk4",
      label: "低风险",
      icon: "",
      dataList: [],
    }
  ],
  mis_heating_station_risk: [
    {
      id: "heating_station_risk1",
      label: "重大风险",
      icon: "",
      dataList: [],
    },
    {
      id: "heating_station_risk2",
      label: "较大风险",
      icon: "",
      dataList: [],
    },
    {
      id: "heating_station_risk3",
      label: "一般风险",
      icon: "",
      dataList: [],
    },
    {
      id: "heating_station_risk4",
      label: "低风险",
      icon: "",
      dataList: [],
    }
  ],
  mis_heating_hidden_risk: [
    {
      id: "heating_hidden_risk1",
      label: "重大隐患",
      icon: "",
      dataList: [],
    },
    {
      id: "heating_hidden_risk2",
      label: "较大隐患",
      icon: "",
      dataList: [],
    },
    {
      id: "heating_hidden_risk3",
      label: "一般隐患",
      icon: "",
      dataList: [],
    },
  ],
  mis_heating_protection: [
    {
      id: "heating_protection1",
      label: "非重点防护目标",
      icon: "",
      dataList: [],
    },
    {
      id: "heating_protection2",
      label: "重点防护目标",
      icon: "",
      dataList: [],
    },
  ],
  mis_com_risk: [
    {
      id: "mis_com_risk1",
      label: "重大风险",
      icon: "",
      dataList: [],
    },
    {
      id: "mis_com_risk2",
      label: "较大风险",
      icon: "",
      dataList: [],
    },
    {
      id: "mis_com_risk3",
      label: "一般风险",
      icon: "",
      dataList: [],
    },
    {
      id: "mis_com_risk4",
      label: "低风险",
      icon: "",
      dataList: [],
    }
  ],
};
