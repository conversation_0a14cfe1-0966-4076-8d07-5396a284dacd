<template>
  <PanelBox title="视频监控" class="gas-monitoring-left-middle-panel">
    <div class="panel-content">
      <div class="video-grid">
        <div class="video-item" v-for="(video, index) in videoList" :key="index">
          <div class="video-title">{{ video.title }}</div>
          <VideoPlayer 
            :src="video.src" 
            :muted="true" 
            :autoplay="true"
            :showControls="true"
            :ref="el => { if (el) playerRefs[index] = el }"
            class="video-player"
          />
        </div>
      </div>
    </div>
  </PanelBox>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import PanelBox from '@/components/screen/PanelBox.vue';
import VideoPlayer from '@/components/screen/common/VideoPlayer.vue';

// 播放器引用集合
const playerRefs = ref([]);

// 视频列表数据
const videoList = ref([
{
    title: 'A_富强路桥东北角_V_1',
    src: 'http://**************/video/rtp/34020000002001300023_41010500001320000047/hls.m3u8?originTypeStr=rtp_push'
  },
  {
    title: 'A_工业路桥西北角_V_1',
    src: 'http://**************/video/rtp/34020000002001300023_41010500001320000045/hls.m3u8?originTypeStr=rtp_push'
  },
  {
    title: '黄河路桥西南角',
    src: 'http://**************/video/rtp/34020000002001300023_41010500001320000006/hls.m3u8?originTypeStr=rtp_push'
  },
  {
    title: 'A_木桥桥西南角_V_1',
    src: 'http://**************/video/rtp/34020000002001300023_41010500001320000037/hls.m3u8?originTypeStr=rtp_push'
  }
]);

// 组件卸载时处理
onUnmounted(() => {
  // 销毁播放器，释放资源
  playerRefs.value.forEach(player => {
    if (player && player.player) {
      player.player.dispose();
    }
  });
});
</script>

<style scoped>
.gas-monitoring-left-middle-panel {
  height: 330px; /* 默认高度为330px */
}

.panel-container {
  width: 100%;
  height: 100%;
  background: rgba(0, 35, 80, 0.5);
  border: 1px solid rgba(0, 242, 241, 0.2);
  border-radius: 8px;
  box-shadow: 0 0 10px rgba(0, 242, 241, 0.1);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.panel-header {
  background: linear-gradient(90deg, 
    rgba(0, 242, 241, 0.05) 0%, 
    rgba(0, 242, 241, 0.2) 50%, 
    rgba(0, 242, 241, 0.05) 100%);
  padding: 10px 15px;
  border-bottom: 1px solid rgba(0, 242, 241, 0.2);
}

.panel-title {
  color: #00f2f1;
  font-size: 16px;
  margin: 0;
}

.panel-content {
  padding: 10px;
  height: 100%;
  box-sizing: border-box;
}

.video-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: repeat(2, 1fr);
  gap: 10px;
  height: 100%;
  width: 100%;
}

.video-item {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 4px;
  overflow: hidden;
  background-color: rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(0, 242, 241, 0.2);
}

.video-title {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 220px;
  height: 32px;
  background: #000000;
  opacity: 0.4;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 10px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 10px;
  color: #FFFFFF;
}

/* 媒体查询适配不同分辨率 */
@media screen and (min-width: 1920px) and (max-width: 2560px) {
  .gas-monitoring-left-middle-panel {
    height: 330px;
  }
}

@media screen and (max-width: 1919px) {
  .gas-monitoring-left-middle-panel {
    height: 310px;
  }
  
  .video-title {
    height: 28px;
    font-size: 12px;
  }
}

@media screen and (min-width: 2561px) {
  .gas-monitoring-left-middle-panel {
    height: 360px;
  }
  
  .video-title {
    height: 36px;
    font-size: 12px;
  }
}

/* 视频播放器响应式调整 */
.video-player {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 低高度屏幕适配 */
@media screen and (max-height: 940px) {
  .gas-monitoring-left-middle-panel {
    height: 280px;
  }
  
  .video-grid {
    gap: 8px;
  }
  
  .video-title {
    height: 24px;
    font-size: 12px;
    width: 180px;
  }
  
  .video-item {
    transform: scale(0.95);
    transform-origin: center;
  }
}

@media screen and (max-height: 900px) {
  .gas-monitoring-left-middle-panel {
    height: 260px;
  }
  
  .video-grid {
    gap: 6px;
  }
  
  .video-title {
    height: 20px;
    width: 160px;
    font-size: 11px;
  }
}
</style> 