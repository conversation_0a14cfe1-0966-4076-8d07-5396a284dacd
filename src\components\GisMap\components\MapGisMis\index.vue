<template>
    <InitMap/>
    <LayerTreeMis
            class="layer-tree"
            ref="layerTreeRef"
            :treeData="layerTreeData"
            @check-layer="handleCheckLayer"
    />
    <ZoomMis v-if="showZoom"/>
  <!-- 图例 -->
    <div class="normal-legend">
        <LegendMis :legendData="legendData"/>
    </div>
    <div class="gas-model-container">
        <GasModelMis v-if="showGasModel"/>
        <DrainageModelMis v-if="showDrainageModel"/>
        <HeatingModelMis v-if="showHeatingModel"/>
    </div>
</template>

<script setup>
import {nextTick, onMounted, onUnmounted, ref, watch} from "vue";
import InitMap from "@/components/GisMap/InitMap.vue";
import LayerTreeMis from "@/components/GisMap/ToolMis/LayerTreeMis/index.vue";
import ZoomMis from "@/components/GisMap/ToolMis/ZoomMis/index.vue";
import LegendMis from "@/components/GisMap/ToolMis/LegendMis/index.vue";
import GasModelMis from "@/components/GisMap/ToolMis/GasModelMis/index.vue";
import DrainageModelMis from "@/components/GisMap/ToolMis/DrainageModelMis/index.vue";
import HeatingModelMis from "@/components/GisMap/ToolMis/HeatingModelMis/index.vue";
import {mapStates} from "@/components/GisMap/mapStates";
import {mapLoaded} from "@/hooks/gishooks";
import {
    layerQueryInfo,
    requestMisDataMap,
} from "./common/gisInfo.js";
import {treeInfo} from "./common/layerInfo.js";
import {legendInfo} from "./common/legendInfo.js";
import {
    formatDataByField,
    formatPolylineDataByFieldAndStyle,
    getIntersectionArray
} from "@/components/GisMap/common/gisUtil";
import {defaultCheckedLayers} from "@/hooks/gishooks";
import {useRoute} from "vue-router";
import bus from "@/utils/mitt.js";
import {gisMisPopups} from "@/components/GisMap/popup/gisPopup.js";
import gisDialog from "@/components/GisMap/common/gisDialog.js";
import {
    alarmLayerMap,
    alarmStatusMap,
    AllPipelineMap,
    associationLayerMap,
    devicesAlarmAll,
    offsetPopupList
} from "@/components/GisMap/common/gisInfo.js";

const route = useRoute();
//图层start
const layerTreeData = ref([]);
const dialogs = ref();
//图例
const legendData = ref([]);
let layerAPIRes = {}; //标记调用接口情况
let layerData = {}; //存储地图容器数据

const showGasModel = ref(false);
const showDrainageModel = ref(false);
const showHeatingModel = ref(false);
const showZoom = ref(true);

//点击事件：弹窗1信息展示
//点击事件：弹窗1信息展示
const handlepickBillboard = async (entity) => {
    if (dialogs.value) {
        // 只允许一个弹窗出现
        dialogs.value.windowClose();
    }
    //清除高亮
    mapStates.earth.entity.clearHighlight();
    //查找entity信息是否存在
    const item = layerData[entity.name].find(
        (im) => im.id === entity.id
    );
    if (item) {
        let position = [];
        if (AllPipelineMap.includes(entity.name)) {
            const center = mapStates.earth.entity.getCenterFromPolylineGeometry(item?.geometry);
            position = [center.longitude, center.latitude];
        } else {
            position = [parseFloat(item.longitude), parseFloat(item.latitude)];
        }
        mapStates.earth.entity.highlightEntity(entity);

        mapStates.earth.camera.flyTo({
            lon: position[0],
            lat: position[1] - 0.0068,
            height: 1200,
            orientation: {
                heading: 0,
                pitch: -45,
                roll: 0,
            },
        });
        // 获取地形高程
        const cartographic = Cesium.Cartographic.fromDegrees(position[0], position[1]);
        let height = mapStates.viewer.scene.globe.getHeight(cartographic) || 0;
        // 添加一个小的偏移量确保线条显示在地面上
        height += 4;
        const popupPosition = Cesium.Cartesian3.fromDegrees(position[0], position[1],  height);
        const opts = Object.assign({
            viewer: mapStates.viewer,
            position: popupPosition, //弹框位置--笛卡尔坐标；
            gisPopup: gisMisPopups[entity.name],
            offset: offsetPopupList.includes(entity.name) ? [0, 0.1] : [0, 0.08], // 使用相对值，表示canvas高度的百分比
            useElement: true, //如果增加了el按钮，放开element渲染
            useEcharts: true, //如果增加了echarts图表，放开echarts渲染
            data: {
                ...item,
                layerId: entity.name,
            },
        });
        window.setTimeout(() => {
            dialogs.value = new gisDialog(opts);
        }, 500);
    }
};

/**
 * 清理数据源
 * @param layers
 */
const clearDataSourcesByLayers = (layers) => {
    for (let i = 0; i < layers.length; i++) {
        mapStates.earth.entity.clearDataSourcesEntitiesByLayerId(layers[i]);
    }
};

const addPointToMap = (data, layerId) => {
    mapStates.earth.entity.clearDataSourcesEntitiesByLayerId(layerId);
    if (layerId === 'bridge_safety_rating') {
        mapStates.earth.entity.addPointGeometryCustomRender({
            layerId: layerId,
            data: data,
            width: 60, //屏幕分辨率适配
            height: 60, //屏幕分辨率适配
            show: true,
        });
    } else {
        mapStates.earth.entity.addPointGeometryFromDegrees({
            layerId: layerId,
            data: data,
            width: 26, //屏幕分辨率适配
            height: 49, //屏幕分辨率适配
            show: true,
            isCluster: layerId === 'bridge_info' || layerId === 'bridge_safety_rating'? false : true, //是否聚合
            showLabel: layerId === 'bridge_info'? true : false // 是否显示标注
        });
    }
};

const addPolylineToMap = (data, layerId) => {
    mapStates.earth.entity.addPolylineGeometryFromDegrees({
        layerId: layerId,
        data: data,
        // material: materialRoadH1,
        width: 4,
        show: true,
    });
};

// 页面切换：综合预览，监测报警，应急处置……
const onPageIndex = (path, query) => {
    //清除高亮
    mapStates.earth.entity.clearHighlight();
    // 关闭弹窗
    if (dialogs.value) {
        dialogs.value.windowClose();
    }
    const layerIds = [...Object.keys(layerData), ...Object.keys(layerAPIRes)];
    clearDataSourcesByLayers([...new Set(layerIds)]);
    layerData = {};
    layerAPIRes = {};
    showGasModel.value = false;
    showDrainageModel.value = false;
    showHeatingModel.value = false;
    showZoom.value = true;
    switch (path) {
        case "/gas/risk/explosion/heatmap": //燃气管网风险热力图
            layerTreeData.value = treeInfo["mis_gas_pipeline_risk"];
            legendData.value = legendInfo["mis_gas_pipeline_risk"];
            defaultCheckedLayers.value = layerQueryInfo["mis_gas_pipeline_risk"];
            break;
        case "/gas/risk/explosion/station-heatmap": //燃气场站风险热力图
            layerTreeData.value = treeInfo["mis_gas_station_risk"];
            legendData.value = legendInfo["mis_gas_station_risk"];
            defaultCheckedLayers.value = layerQueryInfo["mis_gas_station_risk"];
            break;
        case "/gas/leak/monitor/video": //视频监控--四个专项通用
            layerTreeData.value = treeInfo["mis_gas_monitor_video"];
            legendData.value = legendInfo["mis_gas_monitor_video"];
            defaultCheckedLayers.value = layerQueryInfo["mis_gas_monitor_video"];
            break;
        case "/gas/predict/warning/damage": //可燃气爆炸损伤范围分析
            layerTreeData.value = [];
            legendData.value = [];
            defaultCheckedLayers.value = [];
            showGasModel.value = true;
            break;
        case "/gas/home": //燃气首页
            layerTreeData.value = [];
            legendData.value = [];
            defaultCheckedLayers.value = [];
            showZoom.value = false;
            break;
        case "/drainage/monitoringMis/alarm/video": //视频监控--四个专项通用
            layerTreeData.value = treeInfo["mis_gas_monitor_video"];
            legendData.value = legendInfo["mis_gas_monitor_video"];
            defaultCheckedLayers.value = layerQueryInfo["mis_gas_monitor_video"];
            break;
        case "/drainage/decisionMis/support/flood": //排水防汛调度辅助决策支持
            if (query?.floodTab === 'materials') {
                layerTreeData.value = treeInfo["mis_drainage_support_flood"];
                defaultCheckedLayers.value = layerQueryInfo["mis_drainage_support_flood"];
            } else {
                layerTreeData.value = [];
                defaultCheckedLayers.value = [];
            }
            legendData.value = [];
            break;
        case "/drainage/predict/warning/network": //排水管网模型预测预警
            layerTreeData.value = [];
            legendData.value = [];
            defaultCheckedLayers.value = [];
            showDrainageModel.value = true;
            break;
        case "/heating/riskMis/management/network-distribution": //供热管网风险分布图
            layerTreeData.value = treeInfo["mis_heating_pipeline_risk"];
            legendData.value = legendInfo["mis_heating_pipeline_risk"];
            defaultCheckedLayers.value = layerQueryInfo["mis_heating_pipeline_risk"];
            break;
        case "/heating/riskMis/management/station-distribution": //供热场站风险分布图
            layerTreeData.value = treeInfo["mis_heating_station_risk"];
            legendData.value = legendInfo["mis_heating_station_risk"];
            defaultCheckedLayers.value = layerQueryInfo["mis_heating_station_risk"];
            break;
        case "/heating/riskMis/management/hidden-distribution": //供热隐患风险分布图
            layerTreeData.value = treeInfo["mis_heating_hidden_risk"];
            legendData.value = legendInfo["mis_heating_hidden_risk"];
            defaultCheckedLayers.value = layerQueryInfo["mis_heating_hidden_risk"];
            break;
        case "/heating/riskMis/management/protection-distribution": //供热防护分布图
            layerTreeData.value = treeInfo["mis_heating_protection"];
            legendData.value = legendInfo["mis_heating_protection"];
            defaultCheckedLayers.value = layerQueryInfo["mis_heating_protection"];
            break;
        case "/heating/monitoringMis/warning/leak": //供热管网泄漏研判分析
            layerTreeData.value = [];
            legendData.value = [];
            defaultCheckedLayers.value = [];
            showHeatingModel.value = true;
            break;
        case "/comprehensive/riskMis/control/map": //综合风险四色图
            layerTreeData.value = treeInfo["mis_com_risk"];
            legendData.value = legendInfo["mis_com_risk"];
            defaultCheckedLayers.value = layerQueryInfo["mis_com_risk"];
            break;
        default:
            layerTreeData.value = [];
            legendData.value = [];
            defaultCheckedLayers.value = [];
            break;
    }
    getGisData(defaultCheckedLayers.value);
    resetPostion();
};

//图层树点击事件
const handleCheckLayer = (layerId, isChecked) => {
    if (layerId) {
        mapStates.earth.entity.toggleLayerVisibleById(layerId, isChecked);
    }
};

const showPipeline = async (v) => {
    console.log("showPipeline-->>", v);
    if (layerAPIRes[v] === v) {
        handleCheckLayer(v, true);
    } else {
        layerAPIRes[v] = v;
        try {
            const params = requestMisDataMap[v]['params']; // 使用默认参数
            // 调用接口获取数据
            const res = await requestMisDataMap[v]['api'](params);
            if (res?.data) {
                let gisData;
                // 如果有过滤条件，过滤数据
                if (requestMisDataMap[v]['filterList']) {
                    const devices = res.data.filter((item) =>
                        requestMisDataMap[v]['filterList'].includes(item[requestMisDataMap[v]['filterColumn']])
                    ); // 根据过滤列过滤数据
                    gisData = formatPolylineDataByFieldAndStyle(devices, "gisType", v); // 格式化数据
                } else {
                    gisData = formatPolylineDataByFieldAndStyle(res.data, "gisType", v); // 格式化数据
                }
                layerData[v] = gisData; // 存储图层数据
                addPolylineToMap(gisData, v); // 添加点到地图
                // bus.emit("searchDataChanged", layerData); // 触发数据变化事件
            } else {
                layerData[v] = []; // 存储图层数据
                // bus.emit("searchDataChanged", layerData); // 触发数据变化事件
                console.warn(`图层 ${v} 返回的数据为空`);
            }
        } catch (error) {
            console.error(`加载管线 ${v} 时发生错误:`, error);
        }
    }
}

const getGisData = async (typeList) => {
    // 创建typeList的副本，避免修改原始参数
    let processTypeList = [...typeList];
    //  判断类型列表是否包含 "_alarm"
    const hasAlarmItems = typeList.some(item => item.includes('_alarm'));
    // 报警参数，初始化为空数组
    const alarms = {
        gas_alarm: [], // 燃气报警状态
        drainage_alarm: [], // 排水报警状态
        heating_alarm: [], // 供暖报警状态
        bridge_alarm: [] // 桥梁报警状态
    };
    // 获取已勾选的设备图层
    const associations = {
        gas_alarm: getIntersectionArray(processTypeList, associationLayerMap['gas_alarm']), // 燃气相关图层
        drainage_alarm: getIntersectionArray(processTypeList, associationLayerMap['drainage_alarm']), // 排水相关图层
        heating_alarm: getIntersectionArray(processTypeList, associationLayerMap['heating_alarm']), // 供暖相关图层
        bridge_alarm: getIntersectionArray(processTypeList, associationLayerMap['bridge_alarm']),  // 桥梁相关图层
    };
    // 如果类型列表包含 "_alarm"，处理报警相关逻辑
    if (hasAlarmItems) {
        // 遍历报警图层映射，处理报警状态
        Object.entries(alarmLayerMap).forEach(([key, layers]) => {
            layers.forEach((layer) => {
                // 如果报警状态映射中存在该图层，并且该图层在类型列表中
                if (alarmStatusMap[layer] && processTypeList.includes(layer)) {
                    alarms[key].push(alarmStatusMap[layer]); // 添加报警状态
                }
            });

            // 如果有报警状态,但没有勾选关联图层，要强制勾选报警对应的所有设备图层；
            if (alarms[key].length > 0 && associations[key].length === 0) {
                processTypeList = processTypeList.concat(associationLayerMap[key]);
            } else if (alarms[key].length > 0 && associations[key].length > 0) {
                // 如果有报警状态且勾选了关联图层，清除没有勾选的关联图层；
                const unAssociationLayer = associationLayerMap[key].filter(layer => !associations[key].includes(layer));
                clearDataSourcesByLayers(unAssociationLayer); // 清除未勾选的关联图层
                // 如果有报警状态且勾选了关联图层，将关联图层添加到类型列表中
                processTypeList = processTypeList.concat(associations[key]);
            }
        });
    } else {
        // todo 如果没有报警图层，获取已勾选的设备图层不包含监测设备的关联图层，则清除监测设备图层
        Object.entries(associations).forEach(([key, layers]) => {
            if (layers.length === 0) {
                clearDataSourcesByLayers(associationLayerMap[key])
            }
        });
    }
    // 遍历类型列表，处理每种类型
    for (const v of processTypeList) {
        //  如果是管线，处理管线相关逻辑
        if (AllPipelineMap.includes(v)) {
            await showPipeline(v);
            continue;
        }
        if (!requestMisDataMap[v]) {
            // console.warn(`未找到图层 ${v} 的配置信息`);
            continue;
        }
        try {
            // 检查某类型是否已调用过接口（监测设备除外）
            if (layerAPIRes[v] === v && !devicesAlarmAll.includes(v)) {
                handleCheckLayer(v, true); // 显示图层
                continue;
            }
            layerAPIRes[v] = v; // 标记接口已调用
            let params = {};
            // 如果是监测设备，设置接口参数
            if (devicesAlarmAll.includes(v)) {
                params = {
                    ...requestMisDataMap[v]['params'], // 合并默认参数
                    alarmStatuses: v.includes('gas') ? alarms.gas_alarm :
                        v.includes('drainage') ? alarms.drainage_alarm :
                            v.includes('heating') ? alarms.heating_alarm :
                                v.includes('bridge') ? alarms.bridge_alarm : [], // 根据类型设置报警状态
                };
            } else {
                params = requestMisDataMap[v]['params']; // 非监测设备使用默认参数
            }
            // 调用接口获取数据
            const res = await requestMisDataMap[v]['api'](params);
            if (res?.data) {
                let gisData;
                // 如果有过滤条件，过滤数据
                if (requestMisDataMap[v]['filterList']) {
                    const devices = res.data.filter((item) =>
                        requestMisDataMap[v]['filterList'].includes(item[requestMisDataMap[v]['filterColumn']])
                    ); // 根据过滤列过滤数据
                    gisData = formatDataByField(devices, "gisType", v); // 格式化数据
                } else {
                    gisData = formatDataByField(res.data, "gisType", v); // 格式化数据
                }
                layerData[v] = gisData; // 存储图层数据
                addPointToMap(gisData, v); // 添加点到地图
                bus.emit("searchDataChanged", layerData); // 触发数据变化事件
            } else {
                layerData[v] = []; // 存储图层数据
                bus.emit("searchDataChanged", layerData); // 触发数据变化事件
                console.warn(`图层 ${v} 返回的数据为空`);
            }
        } catch (error) {
            console.error(`加载图层 ${v} 数据时发生错误:`, error);
        }
    }
};

const optimizeImageryLayer = () => {
    // 获取当前的图层
    let layer = mapStates.viewer.scene.imageryLayers.get(0);
    // 改变当前地图的组织结构
    layer.minificationFilter = Cesium.TextureMinificationFilter.NEAREST;
    layer.magnificationFilter = Cesium.TextureMagnificationFilter.NEAREST;
};

const resetPostion = () => {
    mapStates.earth.camera.flyTo({
        lon: 115.097,
        lat: 35.288,
        // lon: 116.94527734181389,
        // lat: 33.644170804185244,
        height: 8000,
        orientation: {
            heading: 0,
            pitch: -90, //-45
            roll: 0,
        },
    });
    // handleDialog1Close(); // 关闭弹窗
};

const modelMap = () => {
    if (import.meta.env.VITE_GIS_MAP_ID === "pro") {
        console.log("生产环境");
        /* mapStates.earth.basemap.addOsgbModel(gisSource.osgb.dongMing);
         mapStates.earth.basemap.addBridgeModel(gisSource.maxModel.dongMingBridgeModel);*/
    } else if (import.meta.env.VITE_GIS_MAP_ID === "dev") {
        console.log("开发环境");
        /* mapStates.earth.basemap.addOsgbModel(gisSource.osgb.suZhou);
         mapStates.earth.basemap.addBridgeModel(gisSource.maxModel.suZhouModelPartA);*/
    }
};

const InitScreen = () => {
    // 添加默认底图
    mapStates.earth.basemap.add("img");
    mapStates.earth.basemap.add("cia");
    mapStates.earth.basemap.add("tdt_terrain");
    modelMap();
    // 优化影像图层
    optimizeImageryLayer();
    // 重置位置
    resetPostion();
    //地图要素点击事件，[点击事件]，[悬停事件]
    mapStates.earth.event.activatePickHandler(
        [
            handlepickBillboard, //点-billboard
            () => {
            }, //线-polyline
            () => {
            }, //文本-label
            // handlepickAnything, // handlepickAnything, //所有情况,打印坐标
            () => {
            }, //点击空白处
            // handlePickPipe // 3dtiles 管道模型
        ],
        [() => {
        }] //handleHoverPolyline]
    );
};

bus.on("resetGisPopup", () => {
    mapStates.earth.entity.clearHighlight();
    if (dialogs.value) {
        dialogs.value.windowClose();
    }
});

watch(
    () => [mapLoaded.value, route],
    ([val]) => {
        if (val) {
            nextTick(() => {
               onPageIndex(route.path, route.query);
            });
        }
    },
    {
        deep: true,
    }
);

onMounted(() => {
    // 初始化地图
    InitScreen();
});
onUnmounted(() => {
});
</script>

<style lang="scss" scoped>
.layer-tree,
.gas-model-container {
  position: absolute;
  top: 18px;
  left: 18px;
  z-index: 999;
}

.normal-legend {
  position: absolute;
  top: auto;
  bottom: 18px;
  left: 18px;
  z-index: 999;
}
</style>
