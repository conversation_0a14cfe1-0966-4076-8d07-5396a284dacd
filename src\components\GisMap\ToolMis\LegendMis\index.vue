<template>
  <div class="legend" v-show="legendData.length > 0">
    <div class="title-header">
      <div class="title-text">图例</div>
    </div>
    <div class="content">
      <div v-for="(item, index) in legendData" :key="index" class="row">
        <div
          class="icon-color"
          v-if="item.icon === 'default'"
          :style="{ background: item.color }"
        ></div>
        <div
          class="icon-line"
          v-if="item.icon === 'line'"
          :style="{ background: item.color }"
        ></div>
        <div
          class="icon-polygon"
          v-if="item.icon === 'polygon'"
          :style="{ background: item.color }"
        ></div>
        <div class="text-label">
          {{ item.label }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>

const props = defineProps({
    legendData: {
        type: Array,
        default: () => []
    }
});
</script>

<style lang="scss" scoped>
.legend {
  background: #ffffff;
  box-shadow: 0px 3px 4px 0px rgba(0, 0, 0, 0.2);
  border-radius: 2px;
  padding: 18px 22px;

  .title-header {
    .title-text {
      font-size: 14px;
      font-weight: 600;
      text-align: center;
      color: #000;
    }
  }
  .content {
    .row {
      display: flex;
      align-items: center;
      justify-content: left; //每行内容居中
      margin-top: 10px;
      .icon-color {
        width: 14px;
        height: 14px;
        border-radius: 10px;
        margin-right: 10px;
      }
      .icon-line{
        width: 18px;
        height: 4px;
        border-radius: 10px;
        margin-right: 10px;
      }
      .icon-polygon {
        width: 18px;
        height: 14px;
        border-radius: 2px;
        margin-right: 10px;
      }
      .text-label {
        height: 18px;
        line-height: 18px;
        font-size: 14px;
        font-weight: 500;
        color: #333333;
      }
      // 这个是小标题字体样式，
      .text-default {
        height: 18px;
        line-height: 18px;
        font-size: 14px;
        font-weight: 400;
        color: #ffffff;
      }
    }
  }
}
</style>
