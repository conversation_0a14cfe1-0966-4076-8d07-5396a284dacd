<template>
    <PopupFrame :title="'查看详情'"
                :hasArrow="true"
                :showTitle="showTitle"
                :data="data"
                @close="handleClose"
    >
        <component :is="currentCom" :data="data" :baseInfo="baseInfo" />
    </PopupFrame>
</template>

<script setup>
import {computed, ref, watch} from "vue";
import PopupFrame from "../PopupFrame.vue";
import EmergencyEventInfo from "./EmergencyEventInfo.vue";
import {popupApiInfo} from "@/components/GisMap/popup/popupApi.js";
import {mapStates} from "@/components/GisMap/mapStates.js";
import {ElMessage} from "element-plus";

const props = defineProps({
    showTitle: true,
    data: {
        type: Object,
        default: () => ({}),
    },
    closeEvent: {
        type: Function,
        required: false
    }
});

const currentCom = computed(() => {
    return EmergencyEventInfo;
});

const baseInfo = ref({});

// 获取基础信息
const getBaseInfo = async () => {
    try {
        const { data }  = await popupApiInfo[props.data?.layerId](props.data?.id);
        baseInfo.value = data;
    } catch (error) {
        console.error('获取应急事件信息失败:', error);
    }
};

const handleClose = () => {
    props.closeEvent?.();
    //清除高亮
    mapStates.earth.entity.clearHighlight();
};

watch(
    () => props.data,
    () => {
        getBaseInfo();
    },
    {
        deep: true,
        immediate: true,
    }
);
</script>

<style lang="scss" scoped>

</style>