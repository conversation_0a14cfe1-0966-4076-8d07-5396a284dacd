<template>
  <div class="bridge-page-container">
    <!-- 筛选区域 -->
    <div class="filter-section">
      <div class="filter-card">
        <el-form :model="filterForm" ref="filterFormRef" class="filter-form">
          <!-- 桥梁和设备选择区域 -->
          <div class="device-selection-area">
            <div class="selection-row" v-for="(item, index) in filterForm.bridgeInfos" :key="index">
              <el-form-item :label="`桥梁名称${index + 1}`" :prop="`bridgeInfos.${index}.bridgeId`">
                <el-select v-model="item.bridgeId" placeholder="请选择桥梁" style="width: 200px;" clearable
                  @change="handleBridgeChange(index)">
                  <el-option v-for="bridge in bridgeList" :key="bridge.id" :label="bridge.bridgeName" :value="bridge.id" />
                </el-select>
              </el-form-item>

              <el-form-item :label="`设备类型${index + 1}`" :prop="`bridgeInfos.${index}.deviceType`">
                <el-select v-model="item.deviceType" placeholder="请选择设备类型" style="width: 200px;" clearable
                  @change="handleDeviceTypeChange(index)">
                  <el-option v-for="device in deviceTypeOptions" :key="device.value" :label="device.label"
                    :value="device.value" />
                </el-select>
              </el-form-item>

              <el-form-item :label="`设备名称${index + 1}`" :prop="`bridgeInfos.${index}.deviceId`">
                <el-select v-model="item.deviceId" placeholder="请选择设备" style="width: 200px;" clearable>
                  <el-option v-for="device in item.deviceList" :key="device.id" :label="device.deviceName"
                    :value="device.id" />
                </el-select>
              </el-form-item>

              <div class="action-buttons">
                <el-button v-if="filterForm.bridgeInfos.length > 1" type="danger" size="small" text
                  @click="removeDevice(index)">
                  <el-icon>
                    <Delete />
                  </el-icon>
                </el-button>
                <el-button v-if="index === filterForm.bridgeInfos.length - 1 && filterForm.bridgeInfos.length < 3"
                  type="primary" size="small" text @click="addDevice">
                  <el-icon>
                    <Plus />
                  </el-icon>
                </el-button>
              </div>
            </div>
          </div>

          <!-- 统计时段 -->
          <div class="time-selection-area">
            <el-form-item label="统计时段" prop="dateRange">
              <el-date-picker v-model="filterForm.dateRange" type="datetimerange" range-separator="至"
                start-placeholder="开始时间" end-placeholder="结束时间" format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss" style="width: 350px;" />
            </el-form-item>
          </div>

          <!-- 操作按钮 -->
          <div class="action-area">
            <el-button type="primary" @click="handleSearch" :loading="searchLoading">
              <el-icon>
                <Search />
              </el-icon>
              查询
            </el-button>
            <!-- <el-button type="success" @click="exportImage" :disabled="chartData.length === 0">
              <el-icon>
                <Picture />
              </el-icon>
              导出图片
            </el-button>
            <el-button type="warning" @click="exportData" :disabled="chartData.length === 0">
              <el-icon>
                <Download />
              </el-icon>
              导出数据
            </el-button> -->
          </div>
        </el-form>
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="chart-section" v-loading="loading" element-loading-text="数据加载中...">
      <div class="chart-container">
        <div class="chart-header">
          <h3 class="chart-title">关联分析</h3>
          <div class="chart-legend" v-if="chartData.length > 0">
            <div class="legend-item" v-for="(item, index) in chartData" :key="item.deviceName">
              <span class="legend-dot" :style="{ backgroundColor: chartColors[index % chartColors.length] }"></span>
              <span class="legend-text">{{ item.deviceName }}</span>
              <span class="legend-unit">({{ item.unitName }})</span>
            </div>
          </div>
        </div>
        <div class="chart-content">
          <div ref="chartRef" class="chart-canvas"></div>
        </div>
      </div>
    </div>

    <!-- 数据概览 -->
    <div class="data-overview" v-if="chartData.length > 0">
      <div class="overview-title">
        <h4>数据概览</h4>
      </div>
      <div class="overview-cards">
        <div class="overview-card" v-for="(item, index) in chartData" :key="item.deviceName">
          <div class="card-header">
            <h4 class="device-name">{{ item.deviceName }}</h4>
            <span class="device-unit">{{ item.unitName }}</span>
          </div>
          <div class="card-content">
            <div class="value-item max">
              <span class="value-label">最大值</span>
              <span class="value-number">{{ getMaxValue(item) }}</span>
            </div>
            <div class="value-item min">
              <span class="value-label">最小值</span>
              <span class="value-number">{{ getMinValue(item) }}</span>
            </div>
            <div class="value-item avg">
              <span class="value-label">平均值</span>
              <span class="value-number">{{ getAvgValue(item) }}</span>
            </div>
            <div class="value-item count">
              <span class="value-label">数据点数</span>
              <span class="value-number">{{ getDataCount(item) }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <!-- <div class="empty-state" v-if="!loading && chartData.length === 0">
      <el-empty description="暂无数据，请选择查询条件后点击查询" />
    </div> -->
  </div>
</template>

<script setup>
import { ref, onMounted, reactive, nextTick, onBeforeUnmount } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Delete, Plus, Picture, Download } from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import moment from 'moment'
import {
  getBridgeBasicInfoList,
  getPipelineInfoList,
  getDataAssociationAnalysis
} from '@/api/bridge'
import { DEVICE_TYPE_OPTIONS } from '@/constants/bridge'

// 响应式数据
const loading = ref(false)
const searchLoading = ref(false)
const filterFormRef = ref(null)

// 表单数据
const filterForm = reactive({
  bridgeInfos: [
    {
      bridgeId: '',
      deviceType: '',
      deviceId: '',
      deviceList: []
    }
  ],
  dateRange: []
})

// 下拉选项数据
const bridgeList = ref([])
const deviceTypeOptions = ref(DEVICE_TYPE_OPTIONS)

// 图表相关
const chartRef = ref(null)
let chartInstance = null
const chartData = ref([])
const chartColors = ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4']

// 初始化
onMounted(async () => {
  await loadBridgeList()
  await loadDeviceList(0)
  // 设置默认时间范围为最近7天
  setDefaultDateRange()
})

// 组件卸载时销毁图表
onBeforeUnmount(() => {
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
})

// 设置默认时间范围
const setDefaultDateRange = () => {
  const endTime = moment().format('YYYY-MM-DD HH:mm:ss')
  const startTime = moment().subtract(7, 'days').format('YYYY-MM-DD HH:mm:ss')
  filterForm.dateRange = [startTime, endTime]
}

// 加载桥梁列表
const loadBridgeList = async () => {
  try {
    const response = await getBridgeBasicInfoList()
    if (response.code === 200 && response.data) {
      bridgeList.value = response.data.map(item => ({
        id: item.id,
        bridgeName: item.bridgeName
      }))
    }
  } catch (error) {
    console.error('加载桥梁列表失败:', error)
    ElMessage.error('加载桥梁列表失败')
  }
}

// 加载设备列表
const loadDeviceList = async (index, bridgeId = '', deviceType = '') => {
  try {
    const params = {}
    if (bridgeId) {
      params.bridgeId = bridgeId
    }
    if (deviceType) {
      params.deviceType = deviceType
    }
    
    const response = await getPipelineInfoList(params)
    if (response.code === 200 && response.data) {
      filterForm.bridgeInfos[index].deviceList = response.data.map(item => ({
        id: item.id,
        deviceName: item.deviceName
      }))
    }
  } catch (error) {
    console.error('加载设备列表失败:', error)
    ElMessage.error('加载设备列表失败')
  }
}

// 桥梁选择变化
const handleBridgeChange = (index) => {
  filterForm.bridgeInfos[index].deviceId = ''
  const bridgeId = filterForm.bridgeInfos[index].bridgeId
  const deviceType = filterForm.bridgeInfos[index].deviceType
  if (bridgeId || deviceType) {
    loadDeviceList(index, bridgeId, deviceType)
  }
}

// 设备类型变化
const handleDeviceTypeChange = (index) => {
  filterForm.bridgeInfos[index].deviceId = ''
  const bridgeId = filterForm.bridgeInfos[index].bridgeId
  const deviceType = filterForm.bridgeInfos[index].deviceType
  if (bridgeId || deviceType) {
    loadDeviceList(index, bridgeId, deviceType)
  }
}

// 添加设备
const addDevice = () => {
  if (filterForm.bridgeInfos.length < 3) {
    filterForm.bridgeInfos.push({
      bridgeId: '',
      deviceType: '',
      deviceId: '',
      deviceList: []
    })
  }
}

// 删除设备
const removeDevice = (index) => {
  if (filterForm.bridgeInfos.length > 1) {
    filterForm.bridgeInfos.splice(index, 1)
  }
}

// 查询数据
const handleSearch = async () => {
  // 验证表单
  if (!filterForm.dateRange || filterForm.dateRange.length !== 2) {
    ElMessage.warning('请选择统计时段')
    return
  }

  // 验证至少有一个完整的设备配置
  const validDevices = filterForm.bridgeInfos.filter(item => 
    item.bridgeId && item.deviceType && item.deviceId
  )

  if (validDevices.length === 0) {
    ElMessage.warning('请至少配置一个完整的桥梁设备信息')
    return
  }

  searchLoading.value = true
  loading.value = true

  try {
    // 构建请求参数
    const params = {
      bridgeInfos: validDevices.map(item => ({
        bridgeName: getBridgeName(item.bridgeId),
        deviceName: getDeviceName(item.deviceId, item.deviceList),
        deviceType: item.deviceType
      })),
      startDate: filterForm.dateRange[0],
      endDate: filterForm.dateRange[1]
    }

    const response = await getDataAssociationAnalysis(params)

    if (response.code === 200) {
      if (!response.data || !response.data.associationData) {
        ElMessage.info('查询结果为空')
        chartData.value = []
        return
      }
      
      chartData.value = response.data.associationData || []

      // 渲染图表
      await nextTick()
      renderChart()

      if (chartData.value.length === 0) {
        ElMessage.info('查询结果为空')
      }
    } else {
      ElMessage.error(response.message || '查询失败')
    }
  } catch (error) {
    console.error('查询数据失败:', error)
    ElMessage.error('查询数据失败')
  } finally {
    searchLoading.value = false
    loading.value = false
  }
}

// 获取桥梁名称
const getBridgeName = (bridgeId) => {
  const bridge = bridgeList.value.find(item => item.id === bridgeId)
  return bridge ? bridge.bridgeName : ''
}

// 获取设备名称
const getDeviceName = (deviceId, deviceList) => {
  const device = deviceList.find(item => item.id === deviceId)
  return device ? device.deviceName : ''
}

// 获取最大值
const getMaxValue = (item) => {
  if (!item.dataValue || item.dataValue.length === 0) return 0
  const values = item.dataValue.flatMap(d => d.timeValues?.map(t => t.value) || [])
  return values.length > 0 ? Math.max(...values).toFixed(2) : 0
}

// 获取最小值
const getMinValue = (item) => {
  if (!item.dataValue || item.dataValue.length === 0) return 0
  const values = item.dataValue.flatMap(d => d.timeValues?.map(t => t.value) || [])
  return values.length > 0 ? Math.min(...values).toFixed(2) : 0
}

// 获取平均值
const getAvgValue = (item) => {
  if (!item.dataValue || item.dataValue.length === 0) return 0
  const values = item.dataValue.flatMap(d => d.timeValues?.map(t => t.value) || [])
  if (values.length === 0) return 0
  const sum = values.reduce((acc, val) => acc + val, 0)
  return (sum / values.length).toFixed(2)
}

// 获取数据点数
const getDataCount = (item) => {
  if (!item.dataValue || item.dataValue.length === 0) return 0
  return item.dataValue.reduce((acc, d) => acc + (d.timeValues?.length || 0), 0)
}

// 渲染图表
const renderChart = () => {
  if (!chartRef.value || chartData.value.length === 0) return

  // 销毁已存在的图表实例
  if (chartInstance) {
    chartInstance.dispose()
  }

  chartInstance = echarts.init(chartRef.value)

  // 准备图表数据
  const series = []
  const yAxisData = []
  let hasMultipleUnits = false
  const units = [...new Set(chartData.value.map(item => item.unitName))]
  
  if (units.length > 1) {
    hasMultipleUnits = true
  }

  chartData.value.forEach((item, index) => {
    if (item.dataValue && item.dataValue.length > 0) {
      const dataValue = item.dataValue[0]
      if (dataValue.timeValues && dataValue.timeValues.length > 0) {
        const data = dataValue.timeValues.map(point => [
          moment(point.time).valueOf(),
          point.value || 0
        ])

        const yAxisIndex = hasMultipleUnits ? Math.min(index, 1) : 0

        series.push({
          name: item.deviceName,
          type: 'line',
          yAxisIndex: yAxisIndex,
          data: data,
          smooth: true,
          symbol: 'circle',
          symbolSize: 4,
          lineStyle: {
            width: 2,
            color: chartColors[index % chartColors.length]
          },
          itemStyle: {
            color: chartColors[index % chartColors.length]
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [{
                offset: 0,
                color: chartColors[index % chartColors.length] + '40'
              }, {
                offset: 1,
                color: chartColors[index % chartColors.length] + '10'
              }]
            }
          }
        })
      }
    }
  })

  // 配置Y轴
  if (hasMultipleUnits) {
    yAxisData.push(
      {
        type: 'value',
        name: chartData.value[0]?.unitName || '数值',
        position: 'left',
        axisLine: {
          lineStyle: {
            color: chartColors[0]
          }
        },
        axisLabel: {
          color: '#666'
        },
        splitLine: {
          lineStyle: {
            color: '#f5f5f5'
          }
        }
      },
      {
        type: 'value',
        name: chartData.value[1]?.unitName || '数值',
        position: 'right',
        axisLine: {
          lineStyle: {
            color: chartColors[1]
          }
        },
        axisLabel: {
          color: '#666'
        },
        splitLine: {
          show: false
        }
      }
    )
  } else {
    yAxisData.push({
      type: 'value',
      name: chartData.value[0]?.unitName || '数值',
      axisLine: {
        lineStyle: {
          color: '#e6e6e6'
        }
      },
      axisLabel: {
        color: '#666'
      },
      splitLine: {
        lineStyle: {
          color: '#f5f5f5'
        }
      }
    })
  }

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#999'
        }
      },
      formatter: function (params) {
        let tooltip = `<div style="margin-bottom: 5px;">${moment(params[0].axisValue).format('YYYY-MM-DD HH:mm:ss')}</div>`
        params.forEach(param => {
          const deviceData = chartData.value.find(item => item.deviceName === param.seriesName)
          const unit = deviceData?.unitName || ''
          tooltip += `<div>
            <span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${param.color};"></span>
            ${param.seriesName}: ${param.value[1]} ${unit}
          </div>`
        })
        return tooltip
      }
    },
    legend: {
      data: chartData.value.map(item => item.deviceName),
      top: 10,
      textStyle: {
        color: '#666'
      }
    },
    grid: {
      left: '3%',
      right: hasMultipleUnits ? '8%' : '4%',
      bottom: '3%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'time',
      axisLine: {
        lineStyle: {
          color: '#e6e6e6'
        }
      },
      axisLabel: {
        color: '#666',
        formatter: function (value) {
          return moment(value).format('HH:mm')
        }
      },
      splitLine: {
        show: false
      }
    },
    yAxis: yAxisData,
    series: series,
    animationEasing: 'elasticOut',
    animationDelayUpdate: function (idx) {
      return idx * 100
    }
  }

  chartInstance.setOption(option)

  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    chartInstance && chartInstance.resize()
  })
}

// 导出图片
const exportImage = () => {
  if (!chartInstance) {
    ElMessage.warning('暂无图表数据')
    return
  }

  const url = chartInstance.getDataURL({
    type: 'png',
    pixelRatio: 2,
    backgroundColor: '#fff'
  })

  const link = document.createElement('a')
  link.download = `关联分析_${moment().format('YYYY-MM-DD_HH-mm-ss')}.png`
  link.href = url
  link.click()

  ElMessage.success('图片导出成功')
}

// 导出数据
const exportData = () => {
  if (chartData.value.length === 0) {
    ElMessage.warning('暂无数据')
    return
  }

  try {
    let csvContent = '\uFEFF' // BOM for UTF-8
    csvContent += '设备名称,时间,数值,单位\n'

    chartData.value.forEach(item => {
      if (item.dataValue && item.dataValue.length > 0) {
        const dataValue = item.dataValue[0]
        if (dataValue.timeValues && dataValue.timeValues.length > 0) {
          dataValue.timeValues.forEach(point => {
            csvContent += `${item.deviceName},${point.time},${point.value || 0},${item.unitName}\n`
          })
        }
      }
    })

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    link.href = URL.createObjectURL(blob)
    link.download = `关联分析数据_${moment().format('YYYY-MM-DD_HH-mm-ss')}.csv`
    link.click()

    ElMessage.success('数据导出成功')
  } catch (error) {
    console.error('导出数据失败:', error)
    ElMessage.error('导出数据失败')
  }
}

// 清理事件监听
onBeforeUnmount(() => {
  window.removeEventListener('resize', () => {
    chartInstance && chartInstance.resize()
  })
})
</script>

<style scoped>
.bridge-page-container {
  height: calc(100vh - 200px);
  overflow-y: auto;
}

/* 筛选区域 */
.filter-section {
  margin-bottom: 20px;
}

.filter-card {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.filter-form {
  margin: 0;
}

/* 设备选择区域 */
.device-selection-area {
  margin-bottom: 20px;
}

.selection-row {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.selection-row .el-form-item {
  margin-bottom: 0;
}

.action-buttons {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 时间选择区域 */
.time-selection-area {
  margin-bottom: 20px;
  padding: 16px;
  background: #f0f2f5;
  border-radius: 8px;
}

.time-selection-area .el-form-item {
  margin-bottom: 0;
}

/* 操作区域 */
.action-area {
  display: flex;
  justify-content: center;
  gap: 16px;
  padding-top: 16px;
  border-top: 1px solid #e4e7ed;
}

/* 图表区域 */
.chart-section {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.chart-container {
  padding: 20px;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #f0f0f0;
}

.chart-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.chart-legend {
  display: flex;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 6px;
}

.legend-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.legend-text {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.legend-unit {
  font-size: 12px;
  color: #909399;
}

.chart-content {
  height: 400px;
}

.chart-canvas {
  width: 100%;
  height: 100%;
}

/* 数据概览区域 */
.data-overview {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
}

.overview-title {
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.overview-title h4 {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.overview-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.overview-card {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #e9ecef;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.device-name {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.device-unit {
  font-size: 12px;
  color: #909399;
  background: #e7f3ff;
  padding: 2px 8px;
  border-radius: 4px;
}

.card-content {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.value-item {
  text-align: center;
  padding: 8px;
  background: white;
  border-radius: 6px;
  border: 1px solid #f0f0f0;
}

.value-label {
  display: block;
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
}

.value-number {
  display: block;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.value-item.max .value-number {
  color: #ff4757;
}

.value-item.min .value-number {
  color: #3742fa;
}

.value-item.avg .value-number {
  color: #2ed573;
}

.value-item.count .value-number {
  color: #ffa502;
}

/* 空状态 */
.empty-state {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 40px;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .selection-row {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .selection-row .el-form-item {
    width: 100%;
  }

  .action-buttons {
    justify-content: center;
  }

  .chart-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .chart-legend {
    align-self: flex-end;
  }
}

@media (max-width: 768px) {
  .bridge-page-container {
    padding: 10px;
  }

  .filter-card {
    padding: 15px;
  }

  .chart-container {
    padding: 15px;
  }

  .chart-content {
    height: 300px;
  }

  .overview-cards {
    grid-template-columns: 1fr;
  }

  .card-content {
    grid-template-columns: repeat(2, 1fr);
  }

  .action-area {
    flex-direction: column;
  }

  .action-area .el-button {
    width: 100%;
  }

  .chart-legend {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}
</style>