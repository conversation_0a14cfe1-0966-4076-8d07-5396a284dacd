<template>
    <div class="emergency-event-info">
        <div class="event-content">
            <!-- 信息区域 - 两列布局 -->
            <div class="info-section">
                <!-- 第一行 -->
                <div class="info-row">
                    <div class="info-item">
                        <span class="info-label">事件来源：</span>
                        <span class="info-value">{{ baseInfo?.eventSourceName || '--' }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">事件标题：</span>
                        <span class="info-value">{{ baseInfo?.eventTitle || '--' }}</span>
                    </div>
                </div>

                <!-- 第二行 -->
                <div class="info-row">
                    <div class="info-item">
                        <span class="info-label">事件分类：</span>
                        <span class="info-value">{{ baseInfo?.eventTypeName || '--' }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">事件分级：</span>
                        <span class="info-value">{{ baseInfo?.eventLevelName || '--' }}</span>
                    </div>
                </div>

                <!-- 第三行 -->
                <div class="info-row">
                    <div class="info-item">
                        <span class="info-label">发生时间：</span>
                        <span class="info-value">{{ baseInfo?.eventTime || '--' }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">事件位置：</span>
                        <span class="info-value">{{ baseInfo?.address || '--' }}</span>
                    </div>
                </div>

                <!-- 第四行 -->
                <div class="info-row">
                    <div class="info-item">
                        <span class="info-label">位置坐标：</span>
                        <span class="info-value">{{ baseInfo?.longitude || '--' }}，{{ baseInfo?.latitude || '--' }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">是否人员伤亡：</span>
                        <span class="info-value">{{ baseInfo?.isCasualty? '是': '否' || '--' }}</span>
                    </div>
                </div>

                <!-- 第五行 -->
                <div class="info-row">
                    <div class="info-item">
                        <span class="info-label">死亡人数：</span>
                        <span class="casualty-value">{{ baseInfo?.deathNum || '0' }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">受伤人数：</span>
                        <span class="casualty-value">{{ baseInfo?.injuredNum || '0' }}</span>
                    </div>
                </div>

                <!-- 第六行 -->
                <div class="info-row">
                    <div class="info-item">
                        <span class="info-label">上报人员联系方式：</span>
                        <span class="info-value">{{ baseInfo?.contactInfo || '--' }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">责任单位：</span>
                        <span class="info-value">{{ baseInfo?.ownershipUnitName || '--' }}</span>
                    </div>
                </div>

                <!-- 第七行 -->
                <div class="info-row">
                    <div class="info-item">
                        <span class="info-label">报收时间：</span>
                        <span class="info-value">{{ baseInfo?.receiveTime || '--' }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">处置完成时间：</span>
                        <span class="info-value">{{ baseInfo?.handleTime || '--' }}</span>
                    </div>
                </div>

                <!-- 第八行 -->
                <div class="info-row">
                    <div class="info-item">
                        <span class="info-label">事件处置状态：</span>
                        <span class="status-value" :class="getStatusClass(baseInfo?.eventStatusName)">
                            {{baseInfo?.eventStatusName || '--' }}
                        </span>
                    </div>
                    <div class="info-item">
                        <!-- 空白占位 -->
                    </div>
                </div>

                <!-- 第八行 -->
                <div class="info-row">
                    <div class="info-item">
                        <span class="info-label">事件描述：</span>
                        <span class="status-value">
                            {{baseInfo?.eventDesc || '--' }}
                        </span>
                    </div>
                </div>
            </div>
            <!-- 周边应急资源分析按钮 -->
        </div>
        <div class="analysis-button-section">
            <button class="analysis-button" @click="showEmergencyAnalysis">
                周边应急资源分析
            </button>
        </div>
    </div>
</template>

<script setup>
import {watch} from "vue";
import bus from "@/utils/mitt.js";

const props = defineProps({
    baseInfo: {
        type: Object,
        default: () => ({}),
    },
    data: {
        type: Object,
        default: () => ({}),
    }
});

const showEmergencyAnalysis = () => {
    bus.emit('showEmergencyAnalysis', props.data);
    // 可以在这里添加周边应急资源分析的逻辑
};

// 获取状态样式类
const getStatusClass = (status) => {
    switch(status) {
        case '未处理':
            return 'status-pending';
        case '处理中':
            return 'status-processing';
        case '已处理':
            return 'status-completed';
        default:
            return '';
    }
};

// 监听数据变化
watch(() => props.baseInfo, () => {
    // console.log('baseInfo---emergency-event', props.baseInfo);
}, {deep: true});
</script>

<style lang="scss" scoped>
.emergency-event-info {
    height: 360px;
    overflow-y: auto;

    .event-content {
        display: flex;
        height: 100%;
    }

    .info-section {
        flex: 1;
        padding: 16px;
        color: #ffffff;
        border-radius: 4px;
        display: flex;
        flex-direction: column;
        gap: 12px;
    }

    .info-row {
        display: flex;
        gap: 20px;

        .info-item {
            flex: 1;
            display: flex;
            align-items: center;
            font-size: 14px;

            .info-label {
                color: #8fa4b8;
                min-width: 120px;
                white-space: nowrap;
                text-align: right;
            }

            .info-value {
                color: #ffffff;
                flex: 1;
            }

            .casualty-value {
                color: #ff6b6b;
                font-weight: bold;
                flex: 1;
            }
        }
    }

    .status-value {
        font-weight: bold;

        &.status-pending {
            color: #ff9800;
        }

        &.status-processing {
            color: #2196f3;
        }

        &.status-completed {
            color: #4caf50;
        }
    }

    .description-section {
        margin-top: 8px;

        .description-label {
            color: #8fa4b8;
            font-size: 14px;
            margin-bottom: 8px;
        }

        .description-content {
            color: #ffffff;
            font-size: 12px;
            line-height: 1.6;
            text-align: justify;
            background: rgba(255, 255, 255, 0.05);
            padding: 8px;
            border-radius: 4px;
            min-height: 60px;
        }
    }

    .analysis-button-section {
        margin-top: auto;
        display: flex;
        justify-content: center;
        padding-top: 16px;

        .analysis-button {
            width: auto;
            padding: 8px 16px;
            height: 36px;
            background: #00a8ff;
            color: #ffffff;
            border: none;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
            transition: background-color 0.3s;

            &:hover {
                background: #0088cc;
            }
        }
    }
}
</style>