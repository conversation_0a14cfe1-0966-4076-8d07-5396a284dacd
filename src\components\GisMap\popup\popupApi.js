import {
  getBridgeUsmBridgeBasicInfo,
  getBridgeUsmMonitorAlarmInfo,
  getBridgeUsmMonitorDeviceInfo,
  getBridgeUsmMonitorIndicatorsInfo,
  getBridgeUsmMonitorRecordMonitorCurve,
  getComUsmEmergencyEventInfo,
  getComUsmEmergencyHospitalInfo,
  getComUsmEmergencyRespondersInfo,
  getComUsmEmergencyShelterInfo,
  getComUsmEmergencyStoreInfo,
  getComUsmEmergencySuppliesInfo,
  getComUsmEmergencyTeamInfo,
  getComUsmRiskHiddenDangerInfo, getComUsmWarningInfo,
  getDrainUsmBasicFloodPointInfo,
  getDrainUsmBasicPipelineInfo,
  getDrainUsmBasicPointInfo,
  getDrainUsmBasicWellInfo,
  getDrainUsmMonitorAlarmInfo,
  getDrainUsmMonitorDeviceInfo,
  getDrainUsmMonitorIndicatorsInfo,
  getDrainUsmMonitorRecordMonitorCurve,
  getDrainUsmRiskDangerInfo,
  getDrainUsmRiskFactoryInfo,
  getDrainUsmRiskHiddenDangerInfo,
  getDrainUsmRiskPipelineInfo,
  getDrainUsmRiskProtectInfo,
  getDrainUsmRiskStationInfo,
  getGasUsmMonitorAlarmInfo,
  getGasUsmMonitorIndicatorsInfo,
  getGasUsmMonitorRecordMonitorCurve,
  getHeatUsmBasicEnterpriseInfo,
  getHeatUsmBasicHeatFactoryInfo,
  getHeatUsmBasicHeatStationInfo,
  getHeatUsmBasicPipelineInfo,
  getHeatUsmBasicPointInfo,
  getHeatUsmBasicUserInfo,
  getHeatUsmBasicWellInfo,
  getHeatUsmMonitorAlarmInfo,
  getHeatUsmMonitorDeviceInfo,
  getHeatUsmMonitorIndicatorsInfo,
  getHeatUsmMonitorRecordMonitorCurve,
  getHeatUsmRiskDangerInfo,
  getHeatUsmRiskFactoryInfo,
  getHeatUsmRiskHiddenDangerInfo,
  getHeatUsmRiskPipelineInfo,
  getHeatUsmRiskProtectInfo,
  getHeatUsmRiskStationInfo,
  getUsmBasicDrainOutletInfo,
  getUsmBasicPumpStationInfo,
  getUsmBasicSewageFactoryInfo,
  getUsmMonitorDeviceInfo,
  getUsmZyGasDangerInfo,
  getUsmZyGasPipelineInfo,
  getUsmZyGasPointInfo,
  getUsmZyGasProtectInfo,
  getUsmZyGasRiskPipelineInfo,
  getUsmZyGasRiskStationInfo,
  getUsmZyGasStationInfo,
  getUsmZyGasWellInfo,
  postBridgeAlarmStatusList,
  postDrainAlarmStatusList,
  postGasAlarmStatusList,
  postHeatAlarmStatusList,
  queryUsmVideoStreamHls,
} from "@/api/layerData";

// popupApiInfo 存放所有popup组件的api, key为popup组件的name，value为api函数
export const popupApiInfo = {
  high_pressure_gas_pipeline: getUsmZyGasPipelineInfo, // 燃气管线详情
  mid_pressure_gas_pipeline: getUsmZyGasPipelineInfo, // 燃气管线详情
  low_pressure_gas_pipeline: getUsmZyGasPipelineInfo, // 燃气管线详情
  gas_pipeline_point: getUsmZyGasPointInfo, // 燃气管点详情
  gas_station: getUsmZyGasStationInfo, // 燃气场站详情
  gas_well: getUsmZyGasWellInfo, // 燃气窨井详情
  gas_dangerous_source: getUsmZyGasDangerInfo, // 燃气危险源详情
  gas_protection_target: getUsmZyGasProtectInfo, // 燃气防护目标详情
  gas_combustible: getUsmMonitorDeviceInfo, // 燃气可燃气体监测仪详情
  gas_manhole_cover: getUsmMonitorDeviceInfo, // 井盖监测详情
  gas_video: queryUsmVideoStreamHls, // 视频详情
  gas_pipeline_risk1: getUsmZyGasRiskPipelineInfo, // 燃气管线风险详情
  gas_pipeline_risk2: getUsmZyGasRiskPipelineInfo, // 燃气管线风险详情
  gas_pipeline_risk3: getUsmZyGasRiskPipelineInfo, // 燃气管线风险详情
  gas_pipeline_risk4: getUsmZyGasRiskPipelineInfo, // 燃气管线风险详情
  gas_station_risk1: getUsmZyGasRiskStationInfo, // 燃气场站风险评估详情
  gas_station_risk2: getUsmZyGasRiskStationInfo, // 燃气场站风险评估详情
  gas_station_risk3: getUsmZyGasRiskStationInfo, // 燃气场站风险评估详情
  gas_station_risk4: getUsmZyGasRiskStationInfo, // 燃气场站风险评估详情
  drainage_rain_pipeline: getDrainUsmBasicPipelineInfo, // 排水雨水管线详情
  drainage_sewage_pipeline: getDrainUsmBasicPipelineInfo, // 排水污水管线详情
  drainage_rainAndSewage_pipeline: getDrainUsmBasicPipelineInfo, // 排水雨污合流管线详情
  drainage_pump_station: getUsmBasicPumpStationInfo, // 抽水泵站详情
  drainage_sewage_works: getUsmBasicSewageFactoryInfo, // 污水处理厂详情
  drainage_water_outlet: getUsmBasicDrainOutletInfo, // 排水口详情
  drainage_pipeline_point: getDrainUsmBasicPointInfo, // 排水管点详情
  drainage_well: getDrainUsmBasicWellInfo, // 排水窨井详情
  drainage_flooding_point: getDrainUsmBasicFloodPointInfo, // 排水易涝点详情
  drainage_dangerous_source: getDrainUsmRiskDangerInfo, // 排水危险源详情
  drainage_protection_target: getDrainUsmRiskProtectInfo, // 排水防护目标详情
  drainage_combustible: getDrainUsmMonitorDeviceInfo, // 排水可燃气体监测仪详情
  drainage_manhole_cover: getDrainUsmMonitorDeviceInfo, // 排水井盖监测详情
  drainage_level: getDrainUsmMonitorDeviceInfo, // 排水液位计详情
  drainage_water_quality: getDrainUsmMonitorDeviceInfo, // 排水水质监测仪详情
  drainage_video: queryUsmVideoStreamHls, // 视频详情
  drainage_pipeline_risk1: getDrainUsmRiskPipelineInfo, // 排水管线风险详情
  drainage_pipeline_risk2: getDrainUsmRiskPipelineInfo, // 排水管线风险详情
  drainage_pipeline_risk3: getDrainUsmRiskPipelineInfo, // 排水管线风险详情
  drainage_pipeline_risk4: getDrainUsmRiskPipelineInfo, // 排水管线风险详情
  drainage_sewage_risk1: getDrainUsmRiskFactoryInfo, // 排水污水处理厂风险评估详情
  drainage_sewage_risk2: getDrainUsmRiskFactoryInfo, // 排水污水处理厂风险评估详情
  drainage_sewage_risk3: getDrainUsmRiskFactoryInfo, // 排水污水处理厂风险评估详情
  drainage_sewage_risk4: getDrainUsmRiskFactoryInfo, // 排水污水处理厂风险评估详情
  drainage_pump_risk1: getDrainUsmRiskStationInfo, // 排水泵站风险评估详情
  drainage_pump_risk2: getDrainUsmRiskStationInfo, // 排水泵站风险评估详情
  drainage_pump_risk3: getDrainUsmRiskStationInfo, // 排水泵站风险评估详情
  drainage_pump_risk4: getDrainUsmRiskStationInfo, // 排水泵站风险评估详情
  drainage_hidden_risk1: getDrainUsmRiskHiddenDangerInfo, // 排水隐患点风险评估详情
  drainage_hidden_risk2: getDrainUsmRiskHiddenDangerInfo, // 排水隐患点风险评估详情
  drainage_hidden_risk3: getDrainUsmRiskHiddenDangerInfo, // 排水隐患点风险评估详情
  heating_pipeline_1: getHeatUsmBasicPipelineInfo, // 供热管线详情
  heating_pipeline_2: getHeatUsmBasicPipelineInfo, // 供热管线详情
  heating_pipeline_point: getHeatUsmBasicPointInfo, // 供热管点详情
  heating_well: getHeatUsmBasicWellInfo, // 供热窨井详情
  heating_enterprise: getHeatUsmBasicEnterpriseInfo, // 供热企业详情
  heating_source_works: getHeatUsmBasicHeatFactoryInfo, // 供热源厂详情
  heating_station: getHeatUsmBasicHeatStationInfo, // 供热场站详情
  heating_user: getHeatUsmBasicUserInfo, // 供热用户详情
  heating_dangerous_source: getHeatUsmRiskDangerInfo, // 供热危险源详情
  heating_protection_target: getHeatUsmRiskProtectInfo, // 供热防护目标详情
  heating_combustible: getHeatUsmMonitorDeviceInfo, // 供热可燃气体监测仪详情
  heating_manhole_cover: getHeatUsmMonitorDeviceInfo, // 供热井盖监测详情
  heating_temperature: getHeatUsmMonitorDeviceInfo, // 供热温度监测仪详情
  heating_video: queryUsmVideoStreamHls, // 视频详情
  heating_pipeline_risk1: getHeatUsmRiskPipelineInfo, // 供热管线风险详情
  heating_pipeline_risk2: getHeatUsmRiskPipelineInfo, // 供热管线风险详情
  heating_pipeline_risk3: getHeatUsmRiskPipelineInfo, // 供热管线风险详情
  heating_pipeline_risk4: getHeatUsmRiskPipelineInfo, // 供热管线风险详情
  heating_source_risk1: getHeatUsmRiskFactoryInfo, // 供热源厂风险评估详情
  heating_source_risk2: getHeatUsmRiskFactoryInfo, // 供热源厂风险评估详情
  heating_source_risk3: getHeatUsmRiskFactoryInfo, // 供热源厂风险评估详情
  heating_source_risk4: getHeatUsmRiskFactoryInfo, // 供热源厂风险评估详情
  heating_station_risk1: getHeatUsmRiskStationInfo, // 供热场站风险评估详情
  heating_station_risk2: getHeatUsmRiskStationInfo, // 供热场站风险评估详情
  heating_station_risk3: getHeatUsmRiskStationInfo, // 供热场站风险评估详情
  heating_station_risk4: getHeatUsmRiskStationInfo, // 供热场站风险评估详情
  heating_hidden_risk1: getHeatUsmRiskHiddenDangerInfo, // 供热隐患点风险评估详情
  heating_hidden_risk2: getHeatUsmRiskHiddenDangerInfo, // 供热隐患点风险评估详情
  heating_hidden_risk3: getHeatUsmRiskHiddenDangerInfo, // 供热隐患点风险评估详情
  bridge_info: getBridgeUsmBridgeBasicInfo, // 桥梁信息详情
  bridge_safety_rating: getBridgeUsmBridgeBasicInfo, // 桥梁安全等级详情
  bridge_temperature: getBridgeUsmMonitorDeviceInfo, // 桥梁温度监测仪
  bridge_static_level: getBridgeUsmMonitorDeviceInfo, // 桥梁静力水准仪
  bridge_displacement: getBridgeUsmMonitorDeviceInfo, // 桥梁位移监测仪
  bridge_strain: getBridgeUsmMonitorDeviceInfo, // 桥梁应变监测仪
  bridge_crack_sensor: getBridgeUsmMonitorDeviceInfo, // 桥梁裂缝监测仪
  bridge_video: queryUsmVideoStreamHls, // 视频详情
  gas_video0: queryUsmVideoStreamHls, // 燃气视频详情
  gas_video1: queryUsmVideoStreamHls, // 燃气视频详情
  heating_protection1: getHeatUsmRiskProtectInfo, // 供热防护目标详情
  heating_protection2: getHeatUsmRiskProtectInfo, // 供热防护目标详情
  com_warning_level1: getComUsmWarningInfo, //  综合预警级别1
  com_warning_level2: getComUsmWarningInfo, //  综合预警级别2
  com_warning_level3: getComUsmWarningInfo, //  综合预警级别3
  com_gas_event: getComUsmEmergencyEventInfo, // 燃气事件
  com_drainage_event: getComUsmEmergencyEventInfo, // 排水事件
  com_heating_event: getComUsmEmergencyEventInfo, // 供热事件
  com_bridge_event: getComUsmEmergencyEventInfo, // 桥梁事件
  com_shelter: getComUsmEmergencyShelterInfo, // 避难场所
  com_emergency_team: getComUsmEmergencyTeamInfo, // 应急队伍
  com_emergency_material: getComUsmEmergencySuppliesInfo, // 应急物资
  com_rescue_personnel: getComUsmEmergencyRespondersInfo, // 救援人员
  com_medical_institution: getComUsmEmergencyHospitalInfo, // 医疗机构
  com_emergency_warehouse: getComUsmEmergencyStoreInfo, // 应急仓库
  com_gas_hidden: getComUsmRiskHiddenDangerInfo, // 燃气隐患
  com_drainage_hidden: getComUsmRiskHiddenDangerInfo, // 排水隐患
  com_heating_hidden: getComUsmRiskHiddenDangerInfo, // 供热隐患
  com_bridge_hidden: getComUsmRiskHiddenDangerInfo, // 桥梁隐患
};

//监测设备曲线api
export const popupDeviceCurveApiInfo = {
  gas_combustible: getGasUsmMonitorRecordMonitorCurve, // 燃气可燃气体监测仪详情
  gas_manhole_cover: getGasUsmMonitorRecordMonitorCurve, // 井盖监测详情
  drainage_combustible: getDrainUsmMonitorRecordMonitorCurve, // 排水可燃气体监测仪详情
  drainage_manhole_cover: getDrainUsmMonitorRecordMonitorCurve, // 排水井盖监测详情
  drainage_level: getDrainUsmMonitorRecordMonitorCurve, // 排水液位计详情
  drainage_water_quality: getDrainUsmMonitorRecordMonitorCurve, // 排水水质监测仪详情
  heating_combustible: getHeatUsmMonitorRecordMonitorCurve, // 供热可燃气体监测仪
  heating_manhole_cover: getHeatUsmMonitorRecordMonitorCurve, // 供热井盖监测
  heating_temperature: getHeatUsmMonitorRecordMonitorCurve, // 供热温度监测仪
  bridge_temperature: getBridgeUsmMonitorRecordMonitorCurve, // 桥梁温度监测仪
  bridge_static_level: getBridgeUsmMonitorRecordMonitorCurve, // 桥梁静力水准仪
  bridge_displacement: getBridgeUsmMonitorRecordMonitorCurve, // 桥梁位移监测仪
  bridge_strain: getBridgeUsmMonitorRecordMonitorCurve, // 桥梁应变监测仪
  bridge_crack_sensor: getBridgeUsmMonitorRecordMonitorCurve, // 桥梁裂缝监测仪
}

//监测设备指标api
export const popupMonitorIndicatorsApiInfo = {
  gas_combustible: getGasUsmMonitorIndicatorsInfo, // 燃气可燃气体监测指标详情
  gas_manhole_cover: getGasUsmMonitorIndicatorsInfo, // 井盖监测指标详情
  drainage_combustible: getDrainUsmMonitorIndicatorsInfo, // 排水可燃气体监测仪详情
  drainage_manhole_cover: getDrainUsmMonitorIndicatorsInfo, // 排水井盖监测详情
  drainage_level: getDrainUsmMonitorIndicatorsInfo, // 排水液位计详情
  drainage_water_quality: getDrainUsmMonitorIndicatorsInfo, // 排水水质监测仪详情
  heating_combustible: getHeatUsmMonitorIndicatorsInfo, // 供热可燃气体监测仪
  heating_manhole_cover: getHeatUsmMonitorIndicatorsInfo, // 供热井盖监测
  heating_temperature: getHeatUsmMonitorIndicatorsInfo, // 供热温度监测仪
  bridge_temperature: getBridgeUsmMonitorIndicatorsInfo, // 桥梁温度监测仪
  bridge_static_level: getBridgeUsmMonitorIndicatorsInfo, // 桥梁静力水准仪
  bridge_displacement: getBridgeUsmMonitorIndicatorsInfo, // 桥梁位移监测仪
  bridge_strain: getBridgeUsmMonitorIndicatorsInfo, // 桥梁应变监测仪
  bridge_crack_sensor: getBridgeUsmMonitorIndicatorsInfo, // 桥梁裂缝监测仪
}

//监测设备报警详情api
export const popupMonitorAlarmApiInfo = {
  gas_combustible: getGasUsmMonitorAlarmInfo, // 燃气可燃气体监测指标详情
  gas_manhole_cover: getGasUsmMonitorAlarmInfo, // 井盖监测指标详情
  drainage_combustible: getDrainUsmMonitorAlarmInfo, // 排水可燃气体监测仪详情
  drainage_manhole_cover: getDrainUsmMonitorAlarmInfo, // 排水井盖监测详情
  drainage_level: getDrainUsmMonitorAlarmInfo, // 排水液位计详情
  drainage_water_quality: getDrainUsmMonitorAlarmInfo, // 排水水质监测仪详情
  heating_combustible: getHeatUsmMonitorAlarmInfo, // 供热可燃气体监测仪
  heating_manhole_cover: getHeatUsmMonitorAlarmInfo, // 供热井盖监测
  heating_temperature: getHeatUsmMonitorAlarmInfo, // 供热温度监测仪
  bridge_temperature: getBridgeUsmMonitorAlarmInfo, // 桥梁温度监测仪
  bridge_static_level: getBridgeUsmMonitorAlarmInfo, // 桥梁静力水准仪
  bridge_displacement: getBridgeUsmMonitorAlarmInfo, // 桥梁位移监测仪
  bridge_strain: getBridgeUsmMonitorAlarmInfo, // 桥梁应变监测仪
  bridge_crack_sensor: getBridgeUsmMonitorAlarmInfo, // 桥梁裂缝监测仪
}

export const popupMonitorAlarmStatusListApiInfo = {
  gas_combustible: postGasAlarmStatusList, // 燃气可燃气体监测指标详情
  gas_manhole_cover: postGasAlarmStatusList, // 井盖监测指标详情
  drainage_combustible: postDrainAlarmStatusList, // 排水可燃气体监测仪详情
  drainage_manhole_cover: postDrainAlarmStatusList, // 排水井盖监测详情
  drainage_level: postDrainAlarmStatusList, // 排水液位计详情
  drainage_water_quality: postDrainAlarmStatusList, // 排水水质监测仪详情
  heating_combustible: postHeatAlarmStatusList, // 供热可燃气体监测仪
  heating_manhole_cover: postHeatAlarmStatusList, // 供热井盖监测
  heating_temperature: postHeatAlarmStatusList, // 供热温度监测仪
  bridge_temperature: postBridgeAlarmStatusList, // 桥梁温度监测仪
  bridge_static_level: postBridgeAlarmStatusList, // 桥梁静力水准仪
  bridge_displacement: postBridgeAlarmStatusList, // 桥梁位移监测仪
  bridge_strain: postBridgeAlarmStatusList, // 桥梁应变监测仪
  bridge_crack_sensor: postBridgeAlarmStatusList, // 桥梁裂缝监测仪
}
