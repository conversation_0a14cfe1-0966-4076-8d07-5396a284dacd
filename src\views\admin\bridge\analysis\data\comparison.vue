<template>
  <div class="bridge-page-container">
    <!-- 筛选区域 -->
    <div class="filter-section">
      <div class="filter-card">
        <el-form :model="filterForm" ref="filterFormRef" class="filter-form">
          <!-- 第一行：基础筛选条件 -->
          <div class="form-row">
            <el-form-item label="桥梁名称" prop="bridgeId">
              <el-select 
                v-model="filterForm.bridgeId" 
                placeholder="请选择桥梁" 
                style="width: 200px;" 
                clearable
                @change="handleBridgeChange"
              >
                <el-option
                  v-for="bridge in bridgeList"
                  :key="bridge.id"
                  :label="bridge.bridgeName"
                  :value="bridge.id"
                />
              </el-select>
            </el-form-item>
            
            <el-form-item label="设备类型" prop="deviceType">
              <el-select 
                v-model="filterForm.deviceType" 
                placeholder="请选择设备类型" 
                style="width: 200px;" 
                clearable
                @change="handleDeviceTypeChange"
              >
                <el-option
                  v-for="device in deviceTypeOptions"
                  :key="device.value"
                  :label="device.label"
                  :value="device.value"
                />
              </el-select>
            </el-form-item>
            
            <el-form-item label="设备名称" prop="deviceId">
              <el-select 
                v-model="filterForm.deviceId" 
                placeholder="请选择设备" 
                style="width: 200px;" 
                clearable
              >
                <el-option
                  v-for="device in deviceNameList"
                  :key="device.id"
                  :label="device.deviceName"
                  :value="device.id"
                />
              </el-select>
            </el-form-item>

            <!-- 操作按钮 -->
            <div class="action-buttons">
              <el-button type="primary" @click="handleSearch" :loading="searchLoading">
                <el-icon><Search /></el-icon>
                查询
              </el-button>
              <!-- <el-button @click="handleExportChart" :disabled="chartData.length === 0">
                <el-icon><Picture /></el-icon>
                导出图片
              </el-button>
              <el-button @click="handleExportData" :disabled="chartData.length === 0">
                <el-icon><Download /></el-icon>
                导出数据
              </el-button> -->
            </div>
          </div>

          <!-- 第二行：时间段设置 -->
          <div class="time-slots-section">
            <div class="time-slot-group">
              <div class="time-slot-header">
                <label class="time-slot-label">A时间段:</label>
              </div>
              <div class="time-slot-content">
                <el-date-picker
                  v-model="filterForm.timeSlotA.dateRange"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  format="YYYY-MM-DD HH:mm:ss"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  style="width: 350px;"
                />
                <el-button 
                  type="text" 
                  size="small" 
                  @click="addTimeSlot" 
                  :icon="Plus"
                  class="add-time-slot-btn"
                >
                  新增时间段
                </el-button>
              </div>
            </div>

            <div 
              class="time-slot-group" 
              v-for="(slot, index) in additionalTimeSlots" 
              :key="index"
            >
              <div class="time-slot-header">
                <label class="time-slot-label">{{ slot.name }}:</label>
              </div>
              <div class="time-slot-content">
                <el-date-picker
                  v-model="slot.dateRange"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  format="YYYY-MM-DD HH:mm:ss"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  style="width: 350px;"
                />
                <el-button 
                  type="text" 
                  size="small" 
                  @click="removeTimeSlot(index)" 
                  :icon="Minus"
                  class="remove-time-slot-btn"
                >
                  删除
                </el-button>
              </div>
            </div>
          </div>
        </el-form>
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="chart-section" v-loading="loading" element-loading-text="数据加载中...">
      <div class="chart-container">
        <div class="chart-header">
          <h3 class="chart-title">对比分析</h3>
          <div class="chart-legend">
            <div 
              class="legend-item" 
              v-for="(item, index) in legendData" 
              :key="index"
            >
              <span 
                class="legend-dot" 
                :style="{ background: item.color }"
              ></span>
              <span class="legend-text">{{ item.name }}</span>
            </div>
          </div>
        </div>
        <div class="chart-content">
          <div ref="chartRef" class="chart-canvas"></div>
        </div>
      </div>
    </div>

    <!-- 数据概览 -->
    <div class="data-overview" v-if="chartData.length > 0">
      <div class="overview-cards">
        <div class="overview-card" v-for="(item, index) in chartData" :key="index">
          <div class="card-header">
            <h4 class="time-slot-name">{{ item.timeSlotName }}</h4>
            <span class="device-unit">{{ unitName }}</span>
          </div>
          <div class="card-content">
            <div class="value-item avg">
              <span class="value-label">平均值</span>
              <span class="value-number">{{ calculateAverage(item.dataValues) }}</span>
            </div>
            <div class="value-item max">
              <span class="value-label">最大值</span>
              <span class="value-number">{{ getMaxValue(item.dataValues) }}</span>
            </div>
            <div class="value-item min">
              <span class="value-label">最小值</span>
              <span class="value-number">{{ getMinValue(item.dataValues) }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <!-- <div class="empty-state" v-if="!loading && chartData.length === 0">
      <el-empty description="暂无数据，请设置查询条件后点击查询" />
    </div> -->
  </div>
</template>

<script setup>
import { ref, onMounted, reactive, nextTick, onBeforeUnmount, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Picture, Download, Plus, Minus } from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import moment from 'moment'
import { 
  getBridgeBasicInfoList, 
  getPipelineInfoList, 
  getDataContrastAnalysis 
} from '@/api/bridge'
import { DEVICE_TYPE_OPTIONS } from '@/constants/bridge'

// 响应式数据
const loading = ref(false)
const searchLoading = ref(false)
const filterFormRef = ref(null)

// 表单数据
const filterForm = reactive({
  bridgeId: '',
  deviceType: '',
  deviceId: '',
  timeSlotA: {
    name: 'A时间段',
    dateRange: []
  }
})

// 额外时间段
const additionalTimeSlots = ref([])

// 下拉选项数据
const bridgeList = ref([])
const deviceNameList = ref([])
const deviceTypeOptions = ref(DEVICE_TYPE_OPTIONS)

// 图表相关
const chartRef = ref(null)
let chartInstance = null
const chartData = ref([])
const unitName = ref('')

// 计算图例数据
const legendData = computed(() => {
  const colors = ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272']
  const data = []
  
  // A时间段
  data.push({
    name: 'A时间段 [N]',
    color: colors[0]
  })
  
  // 额外时间段
  additionalTimeSlots.value.forEach((slot, index) => {
    data.push({
      name: `${slot.name} [N]`,
      color: colors[(index + 1) % colors.length]
    })
  })
  
  return data
})

// 初始化
onMounted(async () => {
  await loadBridgeList()
  await loadDeviceList()
  // 设置默认时间范围
  setDefaultDateRange()
})

// 组件卸载时销毁图表
onBeforeUnmount(() => {
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
})

// 设置默认时间范围
const setDefaultDateRange = () => {
  const endTime = moment().format('YYYY-MM-DD HH:mm:ss')
  const startTime = moment().subtract(7, 'days').format('YYYY-MM-DD HH:mm:ss')
  filterForm.timeSlotA.dateRange = [startTime, endTime]
}

// 加载桥梁列表
const loadBridgeList = async () => {
  try {
    const response = await getBridgeBasicInfoList()
    if (response.code === 200 && response.data) {
      bridgeList.value = response.data.map(item => ({
        id: item.id,
        bridgeName: item.bridgeName
      }))
    }
  } catch (error) {
    console.error('加载桥梁列表失败:', error)
    ElMessage.error('加载桥梁列表失败')
  }
}

// 加载设备列表
const loadDeviceList = async (bridgeId = '') => {
  try {
    const params = bridgeId ? { bridgeId } : {}
    const response = await getPipelineInfoList(params)
    if (response.code === 200 && response.data) {
      deviceNameList.value = response.data.map(item => ({
        id: item.id,
        deviceName: item.deviceName
      }))
    }
  } catch (error) {
    console.error('加载设备列表失败:', error)
    ElMessage.error('加载设备列表失败')
  }
}

// 桥梁选择变化
const handleBridgeChange = (bridgeId) => {
  filterForm.deviceId = ''
  if (bridgeId) {
    loadDeviceList(bridgeId)
  } else {
    loadDeviceList()
  }
}

// 设备类型变化
const handleDeviceTypeChange = () => {
  filterForm.deviceId = ''
}

// 新增时间段
const addTimeSlot = () => {
  const slotLetter = String.fromCharCode(66 + additionalTimeSlots.value.length) // B, C, D...
  const endTime = moment().format('YYYY-MM-DD HH:mm:ss')
  const startTime = moment().subtract(7, 'days').format('YYYY-MM-DD HH:mm:ss')
  
  additionalTimeSlots.value.push({
    name: `${slotLetter}时间段`,
    dateRange: [startTime, endTime]
  })
}

// 删除时间段
const removeTimeSlot = (index) => {
  additionalTimeSlots.value.splice(index, 1)
  // 重新计算时间段名称
  additionalTimeSlots.value.forEach((slot, idx) => {
    const slotLetter = String.fromCharCode(66 + idx)
    slot.name = `${slotLetter}时间段`
  })
}

// 查询数据
const handleSearch = async () => {
  // 验证基础条件
  if (!filterForm.timeSlotA.dateRange || filterForm.timeSlotA.dateRange.length !== 2) {
    ElMessage.warning('请选择A时间段')
    return
  }

  // 验证额外时间段
  for (let i = 0; i < additionalTimeSlots.value.length; i++) {
    const slot = additionalTimeSlots.value[i]
    if (!slot.dateRange || slot.dateRange.length !== 2) {
      ElMessage.warning(`请选择${slot.name}`)
      return
    }
  }

  searchLoading.value = true
  loading.value = true
  
  try {
    // 构建请求参数
    const timeSlots = []
    
    // 添加A时间段
    timeSlots.push({
      timeSlotName: filterForm.timeSlotA.name,
      startDate: filterForm.timeSlotA.dateRange[0],
      endDate: filterForm.timeSlotA.dateRange[1]
    })
    
    // 添加额外时间段
    additionalTimeSlots.value.forEach(slot => {
      timeSlots.push({
        timeSlotName: slot.name,
        startDate: slot.dateRange[0],
        endDate: slot.dateRange[1]
      })
    })

    const params = {
      bridgeName: getBridgeName(filterForm.bridgeId),
      deviceName: getDeviceName(filterForm.deviceId),
      deviceType: filterForm.deviceType,
      timeSlots: timeSlots
    }

    const response = await getDataContrastAnalysis(params)
    
    if (response.code === 200) {
      if (!response.data) {
        ElMessage.info('查询结果为空')
        return
      }
      
      chartData.value = response.data.contrastData || []
      unitName.value = response.data.unitName || ''
      
      // 渲染图表
      await nextTick()
      renderChart()
      
      if (chartData.value.length === 0) {
        ElMessage.info('查询结果为空')
      }
    } else {
      ElMessage.error(response.message || '查询失败')
    }
  } catch (error) {
    console.error('查询数据失败:', error)
    ElMessage.error('查询数据失败')
  } finally {
    searchLoading.value = false
    loading.value = false
  }
}

// 获取桥梁名称
const getBridgeName = (bridgeId) => {
  const bridge = bridgeList.value.find(item => item.id === bridgeId)
  return bridge ? bridge.bridgeName : ''
}

// 获取设备名称
const getDeviceName = (deviceId) => {
  const device = deviceNameList.value.find(item => item.id === deviceId)
  return device ? device.deviceName : ''
}

// 导出图片
const handleExportChart = () => {
  if (!chartInstance) {
    ElMessage.warning('暂无图表数据')
    return
  }
  
  const base64 = chartInstance.getDataURL({
    type: 'png',
    pixelRatio: 2,
    backgroundColor: '#fff'
  })
  
  const link = document.createElement('a')
  link.download = `对比分析_${moment().format('YYYY-MM-DD_HH-mm-ss')}.png`
  link.href = base64
  link.click()
  
  ElMessage.success('图片导出成功')
}

// 导出数据
const handleExportData = () => {
  if (chartData.value.length === 0) {
    ElMessage.warning('暂无数据')
    return
  }
  
  // 构建CSV数据
  let csvContent = '时间段,时间,数值\n'
  
  chartData.value.forEach(item => {
    if (item.dataValues && item.dataValues.length > 0) {
      item.dataValues[0].timeValues?.forEach(timeValue => {
        csvContent += `${item.timeSlotName},${timeValue.time},${timeValue.value}\n`
      })
    }
  })
  
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')
  link.download = `对比分析数据_${moment().format('YYYY-MM-DD_HH-mm-ss')}.csv`
  link.href = URL.createObjectURL(blob)
  link.click()
  
  ElMessage.success('数据导出成功')
}

// 计算平均值
const calculateAverage = (dataValues) => {
  if (!dataValues || dataValues.length === 0) return 0
  const values = dataValues[0].timeValues || []
  if (values.length === 0) return 0
  
  const sum = values.reduce((acc, item) => acc + (item.value || 0), 0)
  return (sum / values.length).toFixed(2)
}

// 获取最大值
const getMaxValue = (dataValues) => {
  if (!dataValues || dataValues.length === 0) return 0
  return dataValues[0].maxvalue || 0
}

// 获取最小值
const getMinValue = (dataValues) => {
  if (!dataValues || dataValues.length === 0) return 0
  return dataValues[0].minvalue || 0
}

// 渲染图表
const renderChart = () => {
  if (!chartRef.value || chartData.value.length === 0) return

  // 销毁已存在的图表实例
  if (chartInstance) {
    chartInstance.dispose()
  }

  chartInstance = echarts.init(chartRef.value)

  // 生成24小时时间轴
  const hours = Array.from({ length: 24 }, (_, i) => `${i}时`)
  const colors = ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272']

  const series = []

  chartData.value.forEach((item, index) => {
    if (item.dataValues && item.dataValues.length > 0) {
      const timeValues = item.dataValues[0].timeValues || []
      
      // 创建24小时的数据数组，默认值为0
      const hourlyData = new Array(24).fill(0)
      
      // 填充实际数据
      timeValues.forEach(timeValue => {
        const time = timeValue.time
        if (time) {
          // 解析时间，提取小时
          const hour = parseInt(time.split(':')[0]) || 0
          if (hour >= 0 && hour < 24) {
            hourlyData[hour] = timeValue.value || 0
          }
        }
      })

      series.push({
        name: `${item.timeSlotName} [N]`,
        type: 'line',
        data: hourlyData,
        smooth: true,
        lineStyle: {
          color: colors[index % colors.length],
          width: 2
        },
        itemStyle: {
          color: colors[index % colors.length]
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: colors[index % colors.length] + '60' },
              { offset: 1, color: colors[index % colors.length] + '10' }
            ]
          }
        },
        symbol: 'circle',
        symbolSize: 4
      })
    }
  })

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#999'
        }
      },
      formatter: function(params) {
        let tooltip = `<div style="margin-bottom: 5px;">${params[0].axisValue}</div>`
        params.forEach(param => {
          tooltip += `<div>
            <span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${param.color};"></span>
            ${param.seriesName}: ${param.value} ${unitName.value}
          </div>`
        })
        return tooltip
      }
    },
    legend: {
      data: series.map(s => s.name),
      top: 10,
      textStyle: {
        color: '#666'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: hours,
      axisLine: {
        lineStyle: {
          color: '#e6e6e6'
        }
      },
      axisLabel: {
        color: '#666'
      }
    },
    yAxis: {
      type: 'value',
      name: `数值 (${unitName.value})`,
      nameTextStyle: {
        color: '#666'
      },
      axisLine: {
        lineStyle: {
          color: '#e6e6e6'
        }
      },
      axisLabel: {
        color: '#666'
      },
      splitLine: {
        lineStyle: {
          color: '#f5f5f5'
        }
      }
    },
    series: series,
    animationEasing: 'elasticOut',
    animationDelayUpdate: function (idx) {
      return idx * 50
    }
  }

  chartInstance.setOption(option)

  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    chartInstance && chartInstance.resize()
  })
}

// 清理事件监听
onBeforeUnmount(() => {
  window.removeEventListener('resize', () => {
    chartInstance && chartInstance.resize()
  })
})
</script>

<style scoped>
.bridge-page-container {
  height: calc(100vh - 200px);
  overflow-y: auto;
}

/* 筛选区域 */
.filter-section {
  margin-bottom: 20px;
}

.filter-card {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.filter-form {
  margin: 0;
}

.form-row {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.form-row .el-form-item {
  margin-bottom: 0;
}

.action-buttons {
  display: flex;
  gap: 10px;
  margin-left: auto;
}

/* 时间段设置区域 */
.time-slots-section {
  border-top: 1px solid #f0f0f0;
  padding-top: 20px;
}

.time-slot-group {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  gap: 15px;
}

.time-slot-group:last-child {
  margin-bottom: 0;
}

.time-slot-header {
  min-width: 80px;
}

.time-slot-label {
  font-weight: 500;
  color: #606266;
  font-size: 14px;
}

.time-slot-content {
  display: flex;
  align-items: center;
  gap: 10px;
}

.add-time-slot-btn,
.remove-time-slot-btn {
  color: #409eff;
  padding: 4px 8px;
}

.remove-time-slot-btn {
  color: #f56c6c;
}

/* 图表区域 */
.chart-section {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.chart-container {
  padding: 20px;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #f0f0f0;
}

.chart-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.chart-legend {
  display: flex;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 6px;
}

.legend-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.legend-text {
  font-size: 14px;
  color: #666;
}

.chart-content {
  height: 450px;
}

.chart-canvas {
  width: 100%;
  height: 100%;
}

/* 数据概览区域 */
.data-overview {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
}

.overview-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.overview-card {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #e9ecef;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.time-slot-name {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.device-unit {
  font-size: 12px;
  color: #909399;
  background: #e7f3ff;
  padding: 2px 8px;
  border-radius: 4px;
}

.card-content {
  display: flex;
  justify-content: space-between;
  gap: 16px;
}

.value-item {
  text-align: center;
  flex: 1;
}

.value-label {
  display: block;
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
}

.value-number {
  display: block;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.value-item.max .value-number {
  color: #f56c6c;
}

.value-item.min .value-number {
  color: #67c23a;
}

.value-item.avg .value-number {
  color: #409eff;
}

/* 空状态 */
.empty-state {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 40px;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .form-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }
  
  .action-buttons {
    margin-left: 0;
    width: 100%;
    justify-content: flex-start;
  }
  
  .chart-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .chart-legend {
    align-self: flex-end;
  }
}

@media (max-width: 768px) {
  .bridge-page-container {
    padding: 10px;
  }
  
  .filter-card {
    padding: 15px;
  }
  
  .chart-container {
    padding: 15px;
  }
  
  .chart-content {
    height: 350px;
  }
  
  .time-slot-group {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .time-slot-content {
    width: 100%;
  }
  
  .overview-cards {
    grid-template-columns: 1fr;
  }
  
  .action-buttons {
    flex-direction: column;
    width: 100%;
  }
}
</style>