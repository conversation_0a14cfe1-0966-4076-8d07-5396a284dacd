<template>
  <div class="bridge-page-container">
    <!-- 筛选区域 -->
    <div class="filter-section">
      <div class="filter-card">
        <el-form :model="filterForm" ref="filterFormRef" inline class="filter-form">
          <el-form-item label="桥梁名称" prop="bridgeId">
            <el-select v-model="filterForm.bridgeId" placeholder="请选择桥梁" style="width: 200px;" clearable
              @change="handleBridgeChange">
              <el-option v-for="bridge in bridgeList" :key="bridge.id" :label="bridge.bridgeName" :value="bridge.id" />
            </el-select>
          </el-form-item>

          <el-form-item label="加速度传感器" prop="sensorId">
            <el-select v-model="filterForm.sensorId" placeholder="请选择加速度传感器" style="width: 200px;" clearable>
              <el-option v-for="sensor in sensorList" :key="sensor.id" :label="sensor.deviceName" :value="sensor.id" />
            </el-select>
          </el-form-item>

          <el-form-item label="统计时段" prop="timePeriods">
            <div class="time-periods-container">
              <div v-for="(period, index) in filterForm.timePeriods" :key="index" class="time-period-item">
                <div class="period-header">
                  <span class="period-label">{{ period.timePeriodName }}</span>
                  <el-button v-if="filterForm.timePeriods.length > 1" type="danger" size="small" text
                    @click="removePeriod(index)">
                    <el-icon>
                      <Delete />
                    </el-icon>
                  </el-button>
                </div>
                <el-date-picker v-model="period.dateRange" type="datetimerange" range-separator="至"
                  start-placeholder="开始时间" end-placeholder="结束时间" format="YYYY-MM-DD HH:mm:ss"
                  value-format="YYYY-MM-DD HH:mm:ss" style="width: 350px;" />
              </div>
              <el-button v-if="filterForm.timePeriods.length < 3" type="primary" size="small" text @click="addPeriod">
                <el-icon>
                  <Plus />
                </el-icon>
                添加时段
              </el-button>
            </div>
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="handleSearch" :loading="searchLoading">
              <el-icon>
                <Search />
              </el-icon>
              查询
            </el-button>
            <el-button @click="handleReset">
              <el-icon>
                <Refresh />
              </el-icon>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <!-- 导出按钮区域 -->
    <div class="export-section" v-if="chartData.length > 0">
      <div class="export-buttons">
        <el-button type="success" @click="exportImage">
          <el-icon>
            <Picture />
          </el-icon>
          导出图片
        </el-button>
        <el-button type="warning" @click="exportData">
          <el-icon>
            <Download />
          </el-icon>
          导出数据
        </el-button>
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="chart-section" v-loading="loading" element-loading-text="数据加载中...">
      <div class="chart-container">
        <div class="chart-header">
          <h3 class="chart-title">多时段频谱分析</h3>
          <div class="chart-info" v-if="chartData.length > 0">
            <span class="device-info">设备：{{ chartData[0]?.deviceName || '-' }}</span>
            <span class="unit-info">单位：{{ responseData.unitName || '-' }}</span>
          </div>
        </div>
        <div class="chart-content">
          <div ref="chartRef" class="chart-canvas"></div>
        </div>
      </div>
    </div>

    <!-- 数据概览 -->
    <div class="data-overview" v-if="chartData.length > 0">
      <div class="overview-title">
        <h4>数据概览</h4>
      </div>
      <div class="overview-cards">
        <div class="overview-card" v-for="item in chartData" :key="item.timePeriodName">
          <div class="card-header">
            <h4 class="period-name">{{ item.timePeriodName }}</h4>
            <span class="period-time">{{ formatPeriodTime(item.time) }}</span>
          </div>
          <div class="card-content">
            <div class="value-item max">
              <span class="value-label">最大值</span>
              <span class="value-number">{{ item.maxvalue || 0 }}</span>
            </div>
            <div class="value-item min">
              <span class="value-label">最小值</span>
              <span class="value-number">{{ item.minvalue || 0 }}</span>
            </div>
            <div class="value-item avg">
              <span class="value-label">平均值</span>
              <span class="value-number">{{ calculateAverage(item.timeValues) }}</span>
            </div>
            <div class="value-item count">
              <span class="value-label">数据点数</span>
              <span class="value-number">{{ item.timeValues?.length || 0 }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <!-- <div class="empty-state" v-if="!loading && chartData.length === 0">
      <el-empty description="暂无数据，请选择查询条件后点击查询" />
    </div> -->
  </div>
</template>

<script setup>
import { ref, onMounted, reactive, nextTick, onBeforeUnmount } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Refresh, Plus, Delete, Picture, Download } from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import moment from 'moment'
import {
  getBridgeBasicInfoList,
  getPipelineInfoList,
  getMultiPeriodAnalysis
} from '@/api/bridge'

// 响应式数据
const loading = ref(false)
const searchLoading = ref(false)
const filterFormRef = ref(null)

// 表单数据
const filterForm = reactive({
  bridgeId: '',
  sensorId: '',
  timePeriods: [
    {
      timePeriodName: '时段A',
      dateRange: []
    }
  ]
})

// 下拉选项数据
const bridgeList = ref([])
const sensorList = ref([])

// 图表相关
const chartRef = ref(null)
let chartInstance = null
const chartData = ref([])
const responseData = ref({})

// 初始化
onMounted(async () => {
  await loadBridgeList()
  await loadSensorList()
  // 设置默认时间范围
  setDefaultDateRange()
})

// 组件卸载时销毁图表
onBeforeUnmount(() => {
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
})

// 设置默认时间范围
const setDefaultDateRange = () => {
  const endTime = moment().format('YYYY-MM-DD HH:mm:ss')
  const startTime = moment().subtract(7, 'days').format('YYYY-MM-DD HH:mm:ss')
  filterForm.timePeriods[0].dateRange = [startTime, endTime]
}

// 加载桥梁列表
const loadBridgeList = async () => {
  try {
    const response = await getBridgeBasicInfoList()
    if (response.code === 200 && response.data) {
      bridgeList.value = response.data.map(item => ({
        id: item.id,
        bridgeName: item.bridgeName
      }))
    }
  } catch (error) {
    console.error('加载桥梁列表失败:', error)
    ElMessage.error('加载桥梁列表失败')
  }
}

// 加载加速度传感器列表
const loadSensorList = async (bridgeId = '') => {
  try {
    const params = {
      deviceType: '5000104' // 加速度传感器类型
    }
    if (bridgeId) {
      params.bridgeId = bridgeId
    }
    
    const response = await getPipelineInfoList(params)
    if (response.code === 200 && response.data) {
      sensorList.value = response.data.map(item => ({
        id: item.id,
        deviceName: item.deviceName
      }))
    }
  } catch (error) {
    console.error('加载传感器列表失败:', error)
    ElMessage.error('加载传感器列表失败')
  }
}

// 桥梁选择变化
const handleBridgeChange = (bridgeId) => {
  filterForm.sensorId = ''
  if (bridgeId) {
    loadSensorList(bridgeId)
  } else {
    loadSensorList()
  }
}

// 添加时段
const addPeriod = () => {
  const periodNames = ['时段A', '时段B', '时段C']
  const nextIndex = filterForm.timePeriods.length
  if (nextIndex < 3) {
    const endTime = moment().format('YYYY-MM-DD HH:mm:ss')
    const startTime = moment().subtract(7, 'days').format('YYYY-MM-DD HH:mm:ss')
    
    filterForm.timePeriods.push({
      timePeriodName: periodNames[nextIndex],
      dateRange: [startTime, endTime]
    })
  }
}

// 删除时段
const removePeriod = (index) => {
  if (filterForm.timePeriods.length > 1) {
    filterForm.timePeriods.splice(index, 1)
  }
}

// 查询数据
const handleSearch = async () => {
  // 验证表单
  if (!filterForm.bridgeId) {
    ElMessage.warning('请选择桥梁名称')
    return
  }
  
  if (!filterForm.sensorId) {
    ElMessage.warning('请选择加速度传感器')
    return
  }

  // 验证时段
  for (let i = 0; i < filterForm.timePeriods.length; i++) {
    const period = filterForm.timePeriods[i]
    if (!period.dateRange || period.dateRange.length !== 2) {
      ElMessage.warning(`请选择${period.timePeriodName}的时间范围`)
      return
    }
  }

  searchLoading.value = true
  loading.value = true

  try {
    // 构建请求参数
    const bridgeName = getBridgeName(filterForm.bridgeId)
    const sensorName = getSensorName(filterForm.sensorId)
    
    const params = {
      bridgeName: bridgeName,
      sensor: sensorName,
      startTime: filterForm.timePeriods.map(period => ({
        startDate: period.dateRange[0],
        endDate: period.dateRange[1],
        timePeriodName: period.timePeriodName
      }))
    }

    const response = await getMultiPeriodAnalysis(params)

    if (response.code === 200) {
      if (!response.data) {
        ElMessage.info('查询结果为空')
        chartData.value = []
        return
      }
      
      responseData.value = response.data
      chartData.value = response.data.dataValues || []

      // 渲染图表
      await nextTick()
      renderChart()

      if (chartData.value.length === 0) {
        ElMessage.info('查询结果为空')
      }
    } else {
      ElMessage.error(response.message || '查询失败')
    }
  } catch (error) {
    console.error('查询数据失败:', error)
    ElMessage.error('查询数据失败')
  } finally {
    searchLoading.value = false
    loading.value = false
  }
}

// 重置表单
const handleReset = () => {
  filterForm.bridgeId = ''
  filterForm.sensorId = ''
  filterForm.timePeriods = [
    {
      timePeriodName: '时段A',
      dateRange: []
    }
  ]
  setDefaultDateRange()
  chartData.value = []
  responseData.value = {}

  // 清空图表
  if (chartInstance) {
    chartInstance.clear()
  }
}

// 获取桥梁名称
const getBridgeName = (bridgeId) => {
  const bridge = bridgeList.value.find(item => item.id === bridgeId)
  return bridge ? bridge.bridgeName : ''
}

// 获取传感器名称
const getSensorName = (sensorId) => {
  const sensor = sensorList.value.find(item => item.id === sensorId)
  return sensor ? sensor.deviceName : ''
}

// 格式化时段时间
const formatPeriodTime = (time) => {
  return time ? moment(time).format('YYYY-MM-DD HH:mm:ss') : '-'
}

// 计算平均值
const calculateAverage = (timeValues) => {
  if (!timeValues || timeValues.length === 0) return 0
  const sum = timeValues.reduce((acc, item) => acc + (item.value || 0), 0)
  return (sum / timeValues.length).toFixed(2)
}

// 渲染图表
const renderChart = () => {
  if (!chartRef.value || chartData.value.length === 0) return

  // 销毁已存在的图表实例
  if (chartInstance) {
    chartInstance.dispose()
  }

  chartInstance = echarts.init(chartRef.value)

  // 准备图表数据
  const series = []
  const colors = ['#ff4757', '#3742fa', '#2ed573', '#ffa502', '#ff6348']

  chartData.value.forEach((item, index) => {
    if (item.timeValues && item.timeValues.length > 0) {
      const data = item.timeValues.map(point => [
        moment(point.time).valueOf(),
        point.value || 0
      ])

      series.push({
        name: item.timePeriodName,
        type: 'line',
        data: data,
        smooth: true,
        symbol: 'circle',
        symbolSize: 4,
        lineStyle: {
          width: 2,
          color: colors[index % colors.length]
        },
        itemStyle: {
          color: colors[index % colors.length]
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [{
              offset: 0,
              color: colors[index % colors.length] + '40'
            }, {
              offset: 1,
              color: colors[index % colors.length] + '10'
            }]
          }
        }
      })
    }
  })

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#999'
        }
      },
      formatter: function (params) {
        let tooltip = `<div style="margin-bottom: 5px;">${moment(params[0].axisValue).format('YYYY-MM-DD HH:mm:ss')}</div>`
        params.forEach(param => {
          const unit = responseData.value.unitName || ''
          tooltip += `<div>
            <span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${param.color};"></span>
            ${param.seriesName}: ${param.value[1]} ${unit}
          </div>`
        })
        return tooltip
      }
    },
    legend: {
      data: chartData.value.map(item => item.timePeriodName),
      top: 10,
      textStyle: {
        color: '#666'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'time',
      axisLine: {
        lineStyle: {
          color: '#e6e6e6'
        }
      },
      axisLabel: {
        color: '#666',
        formatter: function (value) {
          return moment(value).format('HH:mm')
        }
      },
      splitLine: {
        show: false
      }
    },
    yAxis: {
      type: 'value',
      name: responseData.value.unitName || '数值',
      axisLine: {
        lineStyle: {
          color: '#e6e6e6'
        }
      },
      axisLabel: {
        color: '#666'
      },
      splitLine: {
        lineStyle: {
          color: '#f5f5f5'
        }
      }
    },
    series: series,
    animationEasing: 'elasticOut',
    animationDelayUpdate: function (idx) {
      return idx * 100
    }
  }

  chartInstance.setOption(option)

  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    chartInstance && chartInstance.resize()
  })
}

// 导出图片
const exportImage = () => {
  if (!chartInstance) {
    ElMessage.warning('暂无图表数据')
    return
  }

  const url = chartInstance.getDataURL({
    type: 'png',
    pixelRatio: 2,
    backgroundColor: '#fff'
  })

  const link = document.createElement('a')
  link.download = `多时段频谱分析_${moment().format('YYYY-MM-DD_HH-mm-ss')}.png`
  link.href = url
  link.click()

  ElMessage.success('图片导出成功')
}

// 导出数据
const exportData = () => {
  if (chartData.value.length === 0) {
    ElMessage.warning('暂无数据')
    return
  }

  try {
    let csvContent = '\uFEFF' // BOM for UTF-8
    csvContent += '时段名称,设备名称,时间,数值,最大值,最小值\n'

    chartData.value.forEach(item => {
      if (item.timeValues && item.timeValues.length > 0) {
        item.timeValues.forEach(point => {
          csvContent += `${item.timePeriodName},${item.deviceName},${point.time},${point.value || 0},${item.maxvalue || 0},${item.minvalue || 0}\n`
        })
      }
    })

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    link.href = URL.createObjectURL(blob)
    link.download = `多时段频谱分析数据_${moment().format('YYYY-MM-DD_HH-mm-ss')}.csv`
    link.click()

    ElMessage.success('数据导出成功')
  } catch (error) {
    console.error('导出数据失败:', error)
    ElMessage.error('导出数据失败')
  }
}

// 清理事件监听
onBeforeUnmount(() => {
  window.removeEventListener('resize', () => {
    chartInstance && chartInstance.resize()
  })
})
</script>

<style scoped>
.bridge-page-container {
  background-color: #f5f7fa;
  height: calc(100vh - 200px);
  overflow-y: auto;
}

/* 筛选区域 */
.filter-section {
  margin-bottom: 20px;
}

.filter-card {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.filter-form {
  margin: 0;
}

.filter-form .el-form-item {
  margin-bottom: 16px;
  margin-right: 20px;
}

.filter-form .el-form-item:last-child {
  margin-right: 0;
}

/* 时段管理 */
.time-periods-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
  min-width: 400px;
}

.time-period-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 12px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  background: #fafafa;
}

.period-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.period-label {
  font-weight: 600;
  color: #303133;
  font-size: 14px;
}

/* 导出按钮区域 */
.export-section {
  margin-bottom: 20px;
  display: flex;
  justify-content: flex-end;
}

.export-buttons {
  display: flex;
  gap: 12px;
}

/* 图表区域 */
.chart-section {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.chart-container {
  padding: 20px;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #f0f0f0;
}

.chart-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.chart-info {
  display: flex;
  align-items: center;
  gap: 20px;
}

.device-info,
.unit-info {
  font-size: 14px;
  color: #666;
  background: #f0f2f5;
  padding: 4px 12px;
  border-radius: 4px;
}

.chart-content {
  height: 400px;
}

.chart-canvas {
  width: 100%;
  height: 100%;
}

/* 数据概览区域 */
.data-overview {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
}

.overview-title {
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.overview-title h4 {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.overview-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.overview-card {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #e9ecef;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.period-name {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.period-time {
  font-size: 12px;
  color: #909399;
  background: #e7f3ff;
  padding: 2px 8px;
  border-radius: 4px;
}

.card-content {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.value-item {
  text-align: center;
  padding: 8px;
  background: white;
  border-radius: 6px;
  border: 1px solid #f0f0f0;
}

.value-label {
  display: block;
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
}

.value-number {
  display: block;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.value-item.max .value-number {
  color: #ff4757;
}

.value-item.min .value-number {
  color: #3742fa;
}

.value-item.avg .value-number {
  color: #2ed573;
}

.value-item.count .value-number {
  color: #ffa502;
}

/* 空状态 */
.empty-state {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 40px;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .filter-form {
    display: block;
  }

  .filter-form .el-form-item {
    display: block;
    margin-bottom: 16px;
    margin-right: 0;
  }

  .chart-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .chart-info {
    align-self: flex-end;
  }

  .time-periods-container {
    min-width: auto;
  }
}

@media (max-width: 768px) {
  .bridge-page-container {
    padding: 10px;
  }

  .filter-card {
    padding: 15px;
  }

  .chart-container {
    padding: 15px;
  }

  .chart-content {
    height: 300px;
  }

  .overview-cards {
    grid-template-columns: 1fr;
  }

  .card-content {
    grid-template-columns: repeat(2, 1fr);
  }

  .export-section {
    justify-content: center;
  }

  .export-buttons {
    flex-direction: column;
    width: 100%;
  }

  .export-buttons .el-button {
    width: 100%;
  }
}
</style>