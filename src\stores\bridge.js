import { defineStore } from 'pinia'

export const useBridgeStore = defineStore('bridge', {
  state: () => ({
    // 是否处于详情模式
    isDetailMode: false,
    // 当前选中的桥梁ID和名称
    selectedBridgeId: null,
    selectedBridgeName: '桥梁详情'
  }),

  actions: {
    // 进入详情模式
    enterDetailMode(bridgeId, bridgeName) {
      this.isDetailMode = true
      this.selectedBridgeId = bridgeId
      this.selectedBridgeName = bridgeName
    },

    // 退出详情模式
    exitDetailMode() {
      this.isDetailMode = false
      this.selectedBridgeId = null
      this.selectedBridgeName = '桥梁详情'
    },

    // 切换详情模式
    toggleDetailMode(bridgeId = null, bridgeName = '桥梁详情') {
      if (this.isDetailMode) {
        this.exitDetailMode()
      } else {
        this.enterDetailMode(bridgeId, bridgeName)
      }
    }
  }
}) 