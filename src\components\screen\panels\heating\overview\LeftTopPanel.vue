<template>
  <PanelBox title="基础设施">
    <div class="panel-content">
      <!-- 上面部分：6个数据项展示区域 -->
      <div class="data-items-container">
        <div class="data-item" v-for="(item, index) in dataItems" :key="index">
          <div class="icon-container">
            <img :src="item.icon" :alt="item.label" />
          </div>
          <div class="data-info">
            <div class="data-label">{{ item.label }}</div>
            <div class="data-value">
              {{ item.value }}<span class="data-unit">{{ item.unit }}</span>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 下面部分：两个圆环图表 -->
      <div class="charts-container">
        <div class="chart-container" v-for="(chart, index) in chartData" :key="index">
          <CircleChart 
            :title="chart.title" 
            :data="chart.data" 
            :color-map="chart.colorMap" 
            :show-percentage="chart.showPercentage" 
            class="circle-chart"
          />
        </div>
      </div>
    </div>
  </PanelBox>
</template>

<script setup>
import PanelBox from '@/components/screen/PanelBox.vue'
import CircleChart from '@/components/screen/charts/CircleChart.vue'
import { ref, onMounted } from 'vue'
// 导入API
import { getInfrastructureStatisticsScreen, getPipelineStatistics } from '@/api/heating'
// 导入图片
import yonghuIcon from '@/assets/images/screen/heating/yonghu.png'
import reyuanIcon from '@/assets/images/screen/heating/reyuan.png'
import qiyeIcon from '@/assets/images/screen/heating/qiye.png'
import huanrezhanIcon from '@/assets/images/screen/heating/huanrezhan.png'
import guanwangIcon from '@/assets/images/screen/heating/guanwang.png'
import gongremianjiIcon from '@/assets/images/screen/heating/gongremianji.png'
// 综合态势总览左上面板组件

// 上部分6个数据项
const dataItems = ref([
  {
    icon: qiyeIcon,
    label: '企业',
    value: 0,
    unit: '个'
  },
  {
    icon: guanwangIcon,
    label: '管网',
    value: 0,
    unit: '公里'
  },
  {
    icon: reyuanIcon,
    label: '热源',
    value: 0,
    unit: '个'
  },
  {
    icon: huanrezhanIcon,
    label: '换热站',
    value: 0,
    unit: '座'
  },
  {
    icon: yonghuIcon,
    label: '用户',
    value: 0,
    unit: '户'
  },
  {
    icon: gongremianjiIcon,
    label: '供热面积',
    value: 0,
    unit: '万m²'
  }
])

// 下部分图表数据
const chartData = ref([
  {
    title: '材质',
    data: [],
    showPercentage: false
  },
  {
    title: '管龄',
    data: [],
    showPercentage: false
  }
])

// 获取基础设施统计数据
const fetchInfrastructureData = async () => {
  try {
    const res = await getInfrastructureStatisticsScreen()
    if (res.code === 200 && res.data) {
      // 更新基础设施数据
      dataItems.value[0].value = res.data.enterpriseTotalCount || 0 // 企业
      dataItems.value[1].value = (res.data.pipelineTotalLength / 1000).toFixed(2) || 0 // 管网（米转公里）
      dataItems.value[2].value = res.data.factoryTotalCount || 0 // 热源
      dataItems.value[3].value = res.data.stationTotalCount || 0 // 换热站
      dataItems.value[4].value = res.data.userTotalCount || 0 // 用户
      dataItems.value[5].value = res.data.heatTotalArea || 0 // 供热面积
    }
  } catch (error) {
    console.error('获取基础设施统计数据失败:', error)
  }
}

// 获取管线统计数据
const fetchPipelineData = async () => {
  try {
    const res = await getPipelineStatistics()
    if (res.code === 200 && res.data) {
      // 更新材质统计数据
      if (res.data.materialStatistics && res.data.materialStatistics.length > 0) {
        chartData.value[0].data = res.data.materialStatistics.map(item => ({
          name: item.name,
          value: item.length ? Number(item.length).toFixed(2) : 0,
          unit: 'km'
        }))
      }
      
      // 更新管龄统计数据
      if (res.data.ageStatistics && res.data.ageStatistics.length > 0) {
        chartData.value[1].data = res.data.ageStatistics.map(item => ({
          name: item.name,
          value: item.length ? Number(item.length).toFixed(2) : 0,
          unit: 'km'
        }))
      }
    }
  } catch (error) {
    console.error('获取管线统计数据失败:', error)
  }
}

onMounted(async () => {
  // 获取数据
  await fetchInfrastructureData()
  await fetchPipelineData()
})
</script>

<style scoped>
.panel-content {
  flex: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 5px;
}

/* 上部分：数据项容器样式 */
.data-items-container {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(2, 1fr);
  gap: 15px;
  margin-bottom: 5px;
}

.data-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.icon-container {
  width: 3rem;
  height: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-container img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.data-info {
  display: flex;
  flex-direction: column;
}

.data-label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 16px;
  color: #FFFFFF;
  line-height: 1.2;
}

.data-value {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 16px;
  color: #FFFFFF;
  line-height: 1.5;
}

.data-unit {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
  margin-left: 4px;
}

/* 下部分：图表容器样式 */
.charts-container {
  display: flex;
  justify-content: space-between;
  height: 200px;
  margin-top: 10px;
}

.chart-container {
  width: 48%;
  height: 100%;
  position: relative;
}

.circle-chart {
  width: 100%;
  height: 100%;
}

/* 响应式适配 */
@media (max-height: 940px) {
  .data-label, .data-value {
    font-size: 14px;
  }
  
  .data-unit {
    font-size: 12px;
  }
  
  .charts-container {
    height: 180px;
  }
  .charts-container{
    margin-top: -2rem;
  }
}

@media (max-height: 800px) {
  .panel-content {
    padding: 10px;
    gap: 10px;
  }
  
  .data-items-container {
    gap: 10px;
    margin-bottom: 10px;
  }
  
  .data-label, .data-value {
    font-size: 12px;
  }
  
  .data-unit {
    font-size: 10px;
  }
  
  .charts-container {
    height: 160px;
  }
  
  .icon-container {
    width: 30px;
    height: 30px;
  }
}
</style>