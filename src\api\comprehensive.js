import request from '@/utils/request'
import moment from 'moment'

/**
 * 分页查询通知公告
 * @param {number} page 页码
 * @param {number} size 每页大小
 * @param {object} params 查询参数
 * @returns {Promise}
 */
export function getNoticePage(page, size, params = {}) {
  return request({
    url: `/comprehensive/usmComprehensiveNotice/page/${page}/${size}`,
    method: 'post',
    data: params
  })
}

/**
 * 保存通知公告
 * @param {object} data 通知公告数据
 * @returns {Promise}
 */
export function saveNotice(data) {
  // 处理时间字段格式
  const submitData = { ...data }
  if (submitData.createTime) {
    submitData.createTime = moment(submitData.createTime).format('YYYY-MM-DD HH:mm:ss')
  }
  if (submitData.updateTime) {
    submitData.updateTime = moment(submitData.updateTime).format('YYYY-MM-DD HH:mm:ss')
  }
  
  return request({
    url: `/comprehensive/usmComprehensiveNotice/save`,
    method: 'post',
    data: submitData
  })
}

/**
 * 更新通知公告
 * @param {object} data 通知公告数据
 * @returns {Promise}
 */
export function updateNotice(data) {
  // 处理时间字段格式
  const submitData = { ...data }
  if (submitData.createTime) {
    submitData.createTime = moment(submitData.createTime).format('YYYY-MM-DD HH:mm:ss')
  }
  if (submitData.updateTime) {
    submitData.updateTime = moment(submitData.updateTime).format('YYYY-MM-DD HH:mm:ss')
  }
  
  return request({
    url: `/comprehensive/usmComprehensiveNotice/update`,
    method: 'post',
    data: submitData
  })
}

/**
 * 获取通知公告详情
 * @param {string} id 通知公告ID
 * @returns {Promise}
 */
export function getNoticeDetail(id) {
  return request({
    url: `/comprehensive/usmComprehensiveNotice/${id}`,
    method: 'get'
  })
}

/**
 * 删除通知公告
 * @param {string} id 通知公告ID
 * @returns {Promise}
 */
export function deleteNotice(id) {
  return request({
    url: `/comprehensive/usmComprehensiveNotice/${id}`,
    method: 'delete'
  })
}

// 规划建设项目相关接口

/**
 * 分页查询项目列表
 * @param {number} pageNum 页码
 * @param {number} pageSize 每页大小
 * @param {object} params 查询参数
 * @returns {Promise}
 */
export function getProjectPage(pageNum, pageSize, params = {}) {
  return request({
    url: `/comprehensive/usmComprehensiveProject/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  })
}

/**
 * 保存项目
 * @param {object} data 项目数据
 * @returns {Promise}
 */
export function saveProject(data) {
  // 处理时间字段格式
  const submitData = { ...data }
  if (submitData.startTime) {
    submitData.startTime = moment(submitData.startTime).format('YYYY-MM-DD HH:mm:ss')
  }
  
  return request({
    url: `/comprehensive/usmComprehensiveProject/save`,
    method: 'post',
    data: submitData
  })
}

/**
 * 更新项目
 * @param {object} data 项目数据
 * @returns {Promise}
 */
export function updateProject(data) {
  // 处理时间字段格式
  const submitData = { ...data }
  if (submitData.startTime) {
    submitData.startTime = moment(submitData.startTime).format('YYYY-MM-DD HH:mm:ss')
  }
  
  return request({
    url: `/comprehensive/usmComprehensiveProject/update`,
    method: 'post',
    data: submitData
  })
}

/**
 * 获取项目详情
 * @param {string} id 项目ID
 * @returns {Promise}
 */
export function getProjectDetail(id) {
  return request({
    url: `/comprehensive/usmComprehensiveProject/${id}`,
    method: 'get'
  })
}

/**
 * 删除项目
 * @param {string} id 项目ID
 * @returns {Promise}
 */
export function deleteProject(id) {
  return request({
    url: `/comprehensive/usmComprehensiveProject/${id}`,
    method: 'delete'
  })
}

// 催办相关接口

/**
 * 更新催办
 * @param {object} data 催办数据
 * @returns {Promise}
 */
export function updateUrging(data) {
  // 处理时间字段格式
  const submitData = { ...data }
  if (submitData.startTime) {
    submitData.startTime = moment(submitData.startTime).format('YYYY-MM-DD HH:mm:ss')
  }
  if (submitData.endTime) {
    submitData.endTime = moment(submitData.endTime).format('YYYY-MM-DD HH:mm:ss')
  }
  
  return request({
    url: `/comprehensive/usmComprehensiveUrging/save`,
    method: 'post',
    data: submitData
  })
}

/**
 * 催办记录分页查询
 * @param {number} pageNum 页码
 * @param {number} pageSize 每页大小
 * @param {object} params 查询参数
 * @returns {Promise}
 */
export function getUrgingPage(pageNum, pageSize, params = {}) {
  params.startTime = params.startTime ? moment(params.startTime).format('YYYY-MM-DD HH:mm:ss') : ''
  params.endTime = params.endTime ? moment(params.endTime).format('YYYY-MM-DD HH:mm:ss') : ''
  return request({
    url: `/comprehensive/usmComprehensiveUrging/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  })
}

// 统计分析接口

/**
 * 项目进度统计
 * @param {object} params 查询参数
 * @returns {Promise}
 */
export function getProjectProgressStatistics(params = {}) {
  return request({
    url: `/comprehensive/usmComprehensiveProject/statistics/progress`,
    method: 'post',
    data: params
  })
}

/**
 * 所属行业统计
 * @param {object} params 查询参数
 * @returns {Promise}
 */
export function getProjectRelatedBusinessStatistics(params = {}) {
  return request({
    url: `/comprehensive/usmComprehensiveProject/statistics/relatedBusiness`,
    method: 'post',
    data: params
  })
}

/**
 * 项目规模统计
 * @param {object} params 查询参数
 * @returns {Promise}
 */
export function getProjectScaleStatistics(params = {}) {
  return request({
    url: `/comprehensive/usmComprehensiveProject/statistics/scale`,
    method: 'post',
    data: params
  })
}

/**
 * 在建项目统计
 * @param {object} params 查询参数
 * @returns {Promise}
 */
export function getProjectConstructionStatistics(params = {}) {
  return request({
    url: `/comprehensive/usmComprehensiveProject/statistics/construction`,
    method: 'post',
    data: params
  })
}

// 督查督办相关接口

/**
 * 分页查询督查督办列表
 * @param {number} pageNum 页码
 * @param {number} pageSize 每页大小
 * @param {object} params 查询参数
 * @returns {Promise}
 */
export function getSupervisePage(pageNum, pageSize, params = {}) {
  return request({
    url: `/comprehensive/usmComprehensiveSupervise/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  })
}

/**
 * 保存督查督办
 * @param {object} data 督查督办数据
 * @returns {Promise}
 */
export function saveSupervise(data) {
  // 处理时间字段格式
  const submitData = { ...data }
  if (submitData.issuedTime) {
    submitData.issuedTime = moment(submitData.issuedTime).format('YYYY-MM-DD HH:mm:ss')
  }
  if (submitData.requiredCompletionTime) {
    submitData.requiredCompletionTime = moment(submitData.requiredCompletionTime).format('YYYY-MM-DD HH:mm:ss')
  }
  
  return request({
    url: `/comprehensive/usmComprehensiveSupervise/save`,
    method: 'post',
    data: submitData
  })
}

/**
 * 更新督查督办
 * @param {object} data 督查督办数据
 * @returns {Promise}
 */
export function updateSupervise(data) {
  // 处理时间字段格式
  const submitData = { ...data }
  if (submitData.issuedTime) {
    submitData.issuedTime = moment(submitData.issuedTime).format('YYYY-MM-DD HH:mm:ss')
  }
  if (submitData.requiredCompletionTime) {
    submitData.requiredCompletionTime = moment(submitData.requiredCompletionTime).format('YYYY-MM-DD HH:mm:ss')
  }
  
  return request({
    url: `/comprehensive/usmComprehensiveSupervise/update`,
    method: 'post',
    data: submitData
  })
}

/**
 * 获取督查督办详情
 * @param {string} id 督查督办ID
 * @returns {Promise}
 */
export function getSuperviseDetail(id) {
  return request({
    url: `/comprehensive/usmComprehensiveSupervise/${id}`,
    method: 'get'
  })
}

/**
 * 删除督查督办
 * @param {string} id 督查督办ID
 * @returns {Promise}
 */
export function deleteSupervise(id) {
  return request({
    url: `/comprehensive/usmComprehensiveSupervise/${id}`,
    method: 'delete'
  })
}

/**
 * 督查督办任务下发
 * @param {object} data 下发数据
 * @returns {Promise}
 */
export function issueSupervise(data) {
  return request({
    url: `/comprehensive/usmComprehensiveSupervise/issue`,
    method: 'post',
    data
  })
}

/**
 * 督查督办处置新增/编辑
 * @param {object} data 处置数据
 * @returns {Promise}
 */
export function addSuperviseHandle(data) {
  return request({
    url: `/comprehensive/usmComprehensiveSupervise/addHandle`,
    method: 'post',
    data
  })
}

/**
 * 获取督查督办处置列表
 * @param {string} id 督查督办ID
 * @returns {Promise}
 */
export function getSuperviseHandleList(id) {
  return request({
    url: `/comprehensive/usmComprehensiveSupervise/handleList/${id}`,
    method: 'get'
  })
}

/**
 * 获取督查督办反馈记录时间线
 * @param {string} superviseId 督查督办ID
 * @returns {Promise}
 */
export function getSuperviseStatusList(superviseId) {
  return request({
    url: `/comprehensive/usmComprehensiveSuperviseStatus/list`,
    method: 'post',
    data: { superviseId }
  })
}

// 考核评价相关接口

/**
 * 企业评价排名
 * @param {number} type 类型 1：月度，2：季度，3：年度
 * @returns {Promise}
 */
export function getEvaluationRank(type = 1) {
  return request({
    url: `/comprehensive/usmComprehensiveEvaluation/rank`,
    method: 'get',
    params: { type }
  })
}

/**
 * 企业评价分布统计
 * @returns {Promise}
 */
export function getEvaluationDistribution() {
  return request({
    url: `/comprehensive/usmComprehensiveEvaluation/distribution`,
    method: 'get'
  })
}

/**
 * 分页查询考核评价列表
 * @param {number} pageNum 页码
 * @param {number} pageSize 每页大小
 * @param {object} params 查询参数
 * @returns {Promise}
 */
export function getEvaluationPage(pageNum, pageSize, params = {}) {
  return request({
    url: `/comprehensive/usmComprehensiveEvaluation/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  })
}

/**
 * 保存考核评价
 * @param {object} data 考核评价数据
 * @returns {Promise}
 */
export function saveEvaluation(data) {
  // 处理时间字段格式
  const submitData = { ...data }
  if (submitData.createTime) {
    submitData.createTime = moment(submitData.createTime).format('YYYY-MM-DD HH:mm:ss')
  }
  if (submitData.updateTime) {
    submitData.updateTime = moment(submitData.updateTime).format('YYYY-MM-DD HH:mm:ss')
  }
  
  return request({
    url: `/comprehensive/usmComprehensiveEvaluation/save`,
    method: 'post',
    data: submitData
  })
}

/**
 * 更新考核评价
 * @param {object} data 考核评价数据
 * @returns {Promise}
 */
export function updateEvaluation(data) {
  // 处理时间字段格式
  const submitData = { ...data }
  if (submitData.createTime) {
    submitData.createTime = moment(submitData.createTime).format('YYYY-MM-DD HH:mm:ss')
  }
  if (submitData.updateTime) {
    submitData.updateTime = moment(submitData.updateTime).format('YYYY-MM-DD HH:mm:ss')
  }
  
  return request({
    url: `/comprehensive/usmComprehensiveEvaluation/update`,
    method: 'post',
    data: submitData
  })
}

/**
 * 获取考核评价详情
 * @param {string} id 考核评价ID
 * @returns {Promise}
 */
export function getEvaluationDetail(id) {
  return request({
    url: `/comprehensive/usmComprehensiveEvaluation/${id}`,
    method: 'get'
  })
}

/**
 * 删除考核评价
 * @param {string} id 考核评价ID  
 * @returns {Promise}
 */
export function deleteEvaluation(id) {
  return request({
    url: `/comprehensive/usmComprehensiveEvaluation/${id}`,
    method: 'delete'
  })
}

// 考核评价常量定义

// 考核周期选项
export const EVALUATION_PERIOD_OPTIONS = [
  { label: '月度', value: 7000901 },
  { label: '季度', value: 7000902 },
  { label: '年度', value: 7000903 }
]

// 所属行业选项
export const RELATED_BUSINESS_OPTIONS = [
  { label: '燃气', value: 7000501 },
  { label: '排水', value: 7000502 },
  { label: '供热', value: 7000503 },
  { label: '桥梁', value: 7000504 }
]

// 资料中心相关接口

/**
 * 分页查询资料中心列表
 * @param {number} pageNum 页码
 * @param {number} pageSize 每页大小
 * @param {object} params 查询参数
 * @returns {Promise}
 */
export function getDocumentPage(pageNum, pageSize, params = {}) {
  return request({
    url: `/comprehensive/usmComprehensiveDocument/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  })
}

/**
 * 保存资料文档
 * @param {object} data 资料文档数据
 * @returns {Promise}
 */
export function saveDocument(data) {
  // 处理时间字段格式
  const submitData = { ...data }
  if (submitData.issuedTime) {
    submitData.issuedTime = moment(submitData.issuedTime).format('YYYY-MM-DD HH:mm:ss')
  }
  if (submitData.startTime) {
    submitData.startTime = moment(submitData.startTime).format('YYYY-MM-DD HH:mm:ss')
  }
  if (submitData.endTime) {
    submitData.endTime = moment(submitData.endTime).format('YYYY-MM-DD HH:mm:ss')
  }
  
  return request({
    url: `/comprehensive/usmComprehensiveDocument/save`,
    method: 'post',
    data: submitData
  })
}

/**
 * 更新资料文档
 * @param {object} data 资料文档数据
 * @returns {Promise}
 */
export function updateDocument(data) {
  // 处理时间字段格式
  const submitData = { ...data }
  if (submitData.issuedTime) {
    submitData.issuedTime = moment(submitData.issuedTime).format('YYYY-MM-DD HH:mm:ss')
  }
  if (submitData.startTime) {
    submitData.startTime = moment(submitData.startTime).format('YYYY-MM-DD HH:mm:ss')
  }
  if (submitData.endTime) {
    submitData.endTime = moment(submitData.endTime).format('YYYY-MM-DD HH:mm:ss')
  }
  
  return request({
    url: `/comprehensive/usmComprehensiveDocument/update`,
    method: 'post',
    data: submitData
  })
}

/**
 * 获取资料文档详情
 * @param {string} id 资料文档ID
 * @returns {Promise}
 */
export function getDocumentDetail(id) {
  return request({
    url: `/comprehensive/usmComprehensiveDocument/${id}`,
    method: 'get'
  })
}

/**
 * 删除资料文档
 * @param {string} id 资料文档ID
 * @returns {Promise}
 */
export function deleteDocument(id) {
  return request({
    url: `/comprehensive/usmComprehensiveDocument/${id}`,
    method: 'delete'
  })
}

// 资料中心常量定义

// 资料类型选项
export const DOCUMENT_TYPE_OPTIONS = [
  { label: '法律法规', value: 7001201 },
  { label: '政策文件', value: 7001202 },
  { label: '标准规范', value: 7001203 }
]

// 发布日期选项
export const PUBLISH_DATE_OPTIONS = [
  { label: '今年', value: 'thisYear' },
  { label: '去年', value: 'lastYear' },
  { label: '近三年', value: 'threeYears' }
]

// 报警通报相关接口

/**
 * 分页查询报警通报列表
 * @param {number} pageNum 页码
 * @param {number} pageSize 每页大小
 * @param {object} params 查询参数
 * @returns {Promise}
 */
export function getAlarmNotifyPage(pageNum, pageSize, params = {}) {
  return request({
    url: `/comprehensive/usmAlarmNotify/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  })
}

/**
 * 保存报警通报
 * @param {object} data 报警通报数据
 * @returns {Promise}
 */
export function saveAlarmNotify(data) {
  // 处理时间字段格式
  const submitData = { ...data }
  if (submitData.createTime) {
    submitData.createTime = moment(submitData.createTime).format('YYYY-MM-DD HH:mm:ss')
  }
  if (submitData.updateTime) {
    submitData.updateTime = moment(submitData.updateTime).format('YYYY-MM-DD HH:mm:ss')
  }
  
  return request({
    url: `/comprehensive/usmAlarmNotify/save`,
    method: 'post',
    data: submitData
  })
}

/**
 * 更新报警通报
 * @param {object} data 报警通报数据
 * @returns {Promise}
 */
export function updateAlarmNotify(data) {
  // 处理时间字段格式
  const submitData = { ...data }
  if (submitData.createTime) {
    submitData.createTime = moment(submitData.createTime).format('YYYY-MM-DD HH:mm:ss')
  }
  if (submitData.updateTime) {
    submitData.updateTime = moment(submitData.updateTime).format('YYYY-MM-DD HH:mm:ss')
  }
  
  return request({
    url: `/comprehensive/usmAlarmNotify/update`,
    method: 'post',
    data: submitData
  })
}

/**
 * 获取报警通报详情
 * @param {string} id 报警通报ID
 * @returns {Promise}
 */
export function getAlarmNotifyDetail(id) {
  return request({
    url: `/comprehensive/usmAlarmNotify/${id}`,
    method: 'get'
  })
}

/**
 * 删除报警通报
 * @param {string} id 报警通报ID
 * @returns {Promise}
 */
export function deleteAlarmNotify(id) {
  return request({
    url: `/comprehensive/usmAlarmNotify/${id}`,
    method: 'delete'
  })
}

// 公众报警相关接口

/**
 * 获取报警状态统计
 * @returns {Promise}
 */
export function getAlarmStatusStatistics() {
  return request({
    url: `/comprehensive/usmAlarmPublic/alarmStatusStatistics`,
    method: 'get'
  })
}

/**
 * 分页查询公众报警列表
 * @param {number} pageNum 页码
 * @param {number} pageSize 每页大小
 * @param {object} params 查询参数
 * @returns {Promise}
 */
export function getPublicAlarmPage(pageNum, pageSize, params = {}) {
  return request({
    url: `/comprehensive/usmAlarmPublic/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  })
}

/**
 * 保存公众报警
 * @param {object} data 报警数据
 * @returns {Promise}
 */
export function savePublicAlarm(data) {
  // 处理时间字段格式
  const submitData = { ...data }
  if (submitData.alarmTime) {
    submitData.alarmTime = moment(submitData.alarmTime).format('YYYY-MM-DD HH:mm:ss')
  }
  if (submitData.reportTime) {
    submitData.reportTime = moment(submitData.reportTime).format('YYYY-MM-DD HH:mm:ss')
  }
  
  return request({
    url: `/comprehensive/usmAlarmPublic/save`,
    method: 'post',
    data: submitData
  })
}

/**
 * 更新公众报警
 * @param {object} data 报警数据
 * @returns {Promise}
 */
export function updatePublicAlarm(data) {
  // 处理时间字段格式
  const submitData = { ...data }
  if (submitData.alarmTime) {
    submitData.alarmTime = moment(submitData.alarmTime).format('YYYY-MM-DD HH:mm:ss')
  }
  if (submitData.reportTime) {
    submitData.reportTime = moment(submitData.reportTime).format('YYYY-MM-DD HH:mm:ss')
  }
  
  return request({
    url: `/comprehensive/usmAlarmPublic/update`,
    method: 'post',
    data: submitData
  })
}

/**
 * 获取公众报警详情
 * @param {string} id 报警ID
 * @returns {Promise}
 */
export function getPublicAlarmDetail(id) {
  return request({
    url: `/comprehensive/usmAlarmPublic/${id}`,
    method: 'get'
  })
}

/**
 * 删除公众报警
 * @param {string} id 报警ID
 * @returns {Promise}
 */
export function deletePublicAlarm(id) {
  return request({
    url: `/comprehensive/usmAlarmPublic/${id}`,
    method: 'delete'
  })
}

/**
 * 新增/编辑报警处置
 * @param {object} data 处置数据
 * @returns {Promise}
 */
export function addAlarmHandle(data) {
  // 处理时间字段格式
  const submitData = { ...data }
  if (submitData.dealTime) {
    submitData.dealTime = moment(submitData.dealTime).format('YYYY-MM-DD HH:mm:ss')
  }
  
  return request({
    url: `/comprehensive/usmAlarmPublic/addHandle`,
    method: 'post',
    data: submitData
  })
}

/**
 * 获取报警处置列表
 * @param {string} id 报警ID
 * @returns {Promise}
 */
export function getAlarmHandleList(id) {
  return request({
    url: `/comprehensive/usmAlarmPublic/handleList/${id}`,
    method: 'get'
  })
}

/**
 * 删除报警处置
 * @param {string} id 处置ID
 * @returns {Promise}
 */
export function deleteAlarmHandle(id) {
  return request({
    url: `/comprehensive/usmAlarmPublic/deleteHandle/${id}`,
    method: 'delete'
  })
}

/**
 * 获取报警处置状态时间线
 * @param {string} alarmId 报警ID
 * @returns {Promise}
 */
export function getAlarmStatusList(alarmId) {
  return request({
    url: `/comprehensive/usmAlarmPublicStatus/list`,
    method: 'post',
    data: { alarmId }
  })
}

// 专家申请相关接口

/**
 * 分页查询专家申请列表
 * @param {number} pageNum 页码
 * @param {number} pageSize 每页大小
 * @param {object} params 查询参数
 * @returns {Promise}
 */
export function getExpertApplyPage(pageNum, pageSize, params = {}) {
  return request({
    url: `/comprehensive/usmComprehensiveExpertApply/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  })
}

/**
 * 保存专家申请
 * @param {object} data 专家申请数据
 * @returns {Promise}
 */
export function saveExpertApply(data) {
  // 处理时间字段格式
  const submitData = { ...data }
  if (submitData.applyTime) {
    submitData.applyTime = moment(submitData.applyTime).format('YYYY-MM-DD HH:mm:ss')
  }
  if (submitData.replyTime) {
    submitData.replyTime = moment(submitData.replyTime).format('YYYY-MM-DD HH:mm:ss')
  }
  
  return request({
    url: `/comprehensive/usmComprehensiveExpertApply/save`,
    method: 'post',
    data: submitData
  })
}

/**
 * 更新专家申请
 * @param {object} data 专家申请数据
 * @returns {Promise}
 */
export function updateExpertApply(data) {
  // 处理时间字段格式
  const submitData = { ...data }
  if (submitData.applyTime) {
    submitData.applyTime = moment(submitData.applyTime).format('YYYY-MM-DD HH:mm:ss')
  }
  if (submitData.replyTime) {
    submitData.replyTime = moment(submitData.replyTime).format('YYYY-MM-DD HH:mm:ss')
  }
  
  return request({
    url: `/comprehensive/usmComprehensiveExpertApply/update`,
    method: 'post',
    data: submitData
  })
}

/**
 * 获取专家申请详情
 * @param {string} id 专家申请ID
 * @returns {Promise}
 */
export function getExpertApplyDetail(id) {
  return request({
    url: `/comprehensive/usmComprehensiveExpertApply/${id}`,
    method: 'get'
  })
}

/**
 * 删除专家申请
 * @param {string} id 专家申请ID
 * @returns {Promise}
 */
export function deleteExpertApply(id) {
  return request({
    url: `/comprehensive/usmComprehensiveExpertApply/${id}`,
    method: 'delete'
  })
}

// 专家咨询相关接口

/**
 * 分页查询专家咨询列表
 * @param {number} pageNum 页码
 * @param {number} pageSize 每页大小
 * @param {object} params 查询参数
 * @returns {Promise}
 */
export function getExpertConsultPage(pageNum, pageSize, params = {}) {
  return request({
    url: `/comprehensive/usmComprehensiveExpertConsult/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  })
}

/**
 * 保存专家咨询
 * @param {object} data 专家咨询数据
 * @returns {Promise}
 */
export function saveExpertConsult(data) {
  // 处理时间字段格式
  const submitData = { ...data }
  if (submitData.consultTime) {
    submitData.consultTime = moment(submitData.consultTime).format('YYYY-MM-DD HH:mm:ss')
  }
  if (submitData.replyTime) {
    submitData.replyTime = moment(submitData.replyTime).format('YYYY-MM-DD HH:mm:ss')
  }
  
  return request({
    url: `/comprehensive/usmComprehensiveExpertConsult/save`,
    method: 'post',
    data: submitData
  })
}

/**
 * 更新专家咨询
 * @param {object} data 专家咨询数据
 * @returns {Promise}
 */
export function updateExpertConsult(data) {
  // 处理时间字段格式
  const submitData = { ...data }
  if (submitData.consultTime) {
    submitData.consultTime = moment(submitData.consultTime).format('YYYY-MM-DD HH:mm:ss')
  }
  if (submitData.replyTime) {
    submitData.replyTime = moment(submitData.replyTime).format('YYYY-MM-DD HH:mm:ss')
  }
  
  return request({
    url: `/comprehensive/usmComprehensiveExpertConsult/update`,
    method: 'post',
    data: submitData
  })
}

/**
 * 获取专家咨询详情
 * @param {string} id 专家咨询ID
 * @returns {Promise}
 */
export function getExpertConsultDetail(id) {
  return request({
    url: `/comprehensive/usmComprehensiveExpertConsult/${id}`,
    method: 'get'
  })
}

/**
 * 删除专家咨询
 * @param {string} id 专家咨询ID
 * @returns {Promise}
 */
export function deleteExpertConsult(id) {
  return request({
    url: `/comprehensive/usmComprehensiveExpertConsult/${id}`,
    method: 'delete'
  })
}

/**
 * 获取应急专家列表
 * @param {object} params 查询参数
 * @returns {Promise}
 */
export function getEmergencyExpertList(params = {}) {
  return request({
    url: `/comprehensive/usmEmergencyExpert/list`,
    method: 'post',
    data: params
  })
}

// 应急事件管理相关接口

/**
 * 分页查询应急事件列表
 * @param {number} pageNum 页码
 * @param {number} pageSize 每页大小
 * @param {object} params 查询参数
 * @returns {Promise}
 */
export function getEmergencyEventPage(pageNum, pageSize, params = {}) {
  return request({
    url: `/comprehensive/usmEmergencyEvent/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  })
}

/**
 * 保存应急事件
 * @param {object} data 应急事件数据
 * @returns {Promise}
 */
export function saveEmergencyEvent(data) {
  // 处理时间字段格式
  const submitData = { ...data }
  if (submitData.eventTime) {
    submitData.eventTime = moment(submitData.eventTime).format('YYYY-MM-DD HH:mm:ss')
  }
  if (submitData.receiveTime) {
    submitData.receiveTime = moment(submitData.receiveTime).format('YYYY-MM-DD HH:mm:ss')
  }
  if (submitData.handleTime) {
    submitData.handleTime = moment(submitData.handleTime).format('YYYY-MM-DD HH:mm:ss')
  }
  
  return request({
    url: `/comprehensive/usmEmergencyEvent/save`,
    method: 'post',
    data: submitData
  })
}

/**
 * 更新应急事件
 * @param {object} data 应急事件数据
 * @returns {Promise}
 */
export function updateEmergencyEvent(data) {
  // 处理时间字段格式
  const submitData = { ...data }
  if (submitData.eventTime) {
    submitData.eventTime = moment(submitData.eventTime).format('YYYY-MM-DD HH:mm:ss')
  }
  if (submitData.receiveTime) {
    submitData.receiveTime = moment(submitData.receiveTime).format('YYYY-MM-DD HH:mm:ss')
  }
  if (submitData.handleTime) {
    submitData.handleTime = moment(submitData.handleTime).format('YYYY-MM-DD HH:mm:ss')
  }
  
  return request({
    url: `/comprehensive/usmEmergencyEvent/update`,
    method: 'post',
    data: submitData
  })
}

/**
 * 获取应急事件详情
 * @param {string} id 应急事件ID
 * @returns {Promise}
 */
export function getEmergencyEventDetail(id) {
  return request({
    url: `/comprehensive/usmEmergencyEvent/${id}`,
    method: 'get'
  })
}

/**
 * 删除应急事件
 * @param {string} id 应急事件ID
 * @returns {Promise}
 */
export function deleteEmergencyEvent(id) {
  return request({
    url: `/comprehensive/usmEmergencyEvent/${id}`,
    method: 'delete'
  })
}

// 应急事件统计相关接口

/**
 * 事件状态统计
 * @param {object} params 查询参数 {startTime, endTime}
 * @returns {Promise}
 */
export function getEmergencyEventStatusStatistics(params = {}) {
  // 处理时间字段格式
  const submitData = { ...params }
  if (submitData.startTime) {
    submitData.startTime = moment(submitData.startTime).format('YYYY-MM-DD HH:mm:ss')
  }
  if (submitData.endTime) {
    submitData.endTime = moment(submitData.endTime).format('YYYY-MM-DD HH:mm:ss')
  }
  
  return request({
    url: `/comprehensive/usmEmergencyEventStatistics/status/statistics`,
    method: 'post',
    data: submitData
  })
}

/**
 * 事件等级统计
 * @param {object} params 查询参数 {startTime, endTime}
 * @returns {Promise}
 */
export function getEmergencyEventLevelStatistics(params = {}) {
  // 处理时间字段格式
  const submitData = { ...params }
  if (submitData.startTime) {
    submitData.startTime = moment(submitData.startTime).format('YYYY-MM-DD HH:mm:ss')
  }
  if (submitData.endTime) {
    submitData.endTime = moment(submitData.endTime).format('YYYY-MM-DD HH:mm:ss')
  }
  
  return request({
    url: `/comprehensive/usmEmergencyEventStatistics/level/statistics`,
    method: 'post',
    data: submitData
  })
}

/**
 * 所属行业统计
 * @param {object} params 查询参数 {startTime, endTime}
 * @returns {Promise}
 */
export function getEmergencyEventRelatedBusinessStatistics(params = {}) {
  // 处理时间字段格式
  const submitData = { ...params }
  if (submitData.startTime) {
    submitData.startTime = moment(submitData.startTime).format('YYYY-MM-DD HH:mm:ss')
  }
  if (submitData.endTime) {
    submitData.endTime = moment(submitData.endTime).format('YYYY-MM-DD HH:mm:ss')
  }
  
  return request({
    url: `/comprehensive/usmEmergencyEventStatistics/relatedBusiness/statistics`,
    method: 'post',
    data: submitData
  })
}

/**
 * 事件趋势分析
 * @param {object} params 查询参数 {startTime, endTime}
 * @returns {Promise}
 */
export function getEmergencyEventTrendStatistics(params = {}) {
  // 处理时间字段格式
  const submitData = { ...params }
  if (submitData.startTime) {
    submitData.startTime = moment(submitData.startTime).format('YYYY-MM-DD HH:mm:ss')
  }
  if (submitData.endTime) {
    submitData.endTime = moment(submitData.endTime).format('YYYY-MM-DD HH:mm:ss')
  }
  
  return request({
    url: `/comprehensive/usmEmergencyEventStatistics/trend/statistics`,
    method: 'post',
    data: submitData
  })
}

// 预警信息管理相关接口

/**
 * 获取预警状态统计
 * @returns {Promise}
 */
export function getWarningStatusStatistics() {
  return request({
    url: `/comprehensive/usmWarningInfo/warningStatusStatistics`,
    method: 'get'
  })
}

/**
 * 获取预警等级统计
 * @returns {Promise}
 */
export function getWarningLevelStatistics() {
  return request({
    url: `/comprehensive/usmWarningInfo/warningLevelStatistics`,
    method: 'get'
  })
}

/**
 * 分页查询预警信息列表
 * @param {number} pageNum 页码
 * @param {number} pageSize 每页大小
 * @param {object} params 查询参数
 * @returns {Promise}
 */
export function getWarningInfoPage(pageNum, pageSize, params = {}) {
  return request({
    url: `/comprehensive/usmWarningInfo/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  })
}

/**
 * 保存预警信息
 * @param {object} data 预警信息数据
 * @returns {Promise}
 */
export function saveWarningInfo(data) {
  // 处理时间字段格式
  const submitData = { ...data }
  if (submitData.warningTime) {
    submitData.warningTime = moment(submitData.warningTime).format('YYYY-MM-DD HH:mm:ss')
  }
  if (submitData.publishTime) {
    submitData.publishTime = moment(submitData.publishTime).format('YYYY-MM-DD HH:mm:ss')
  }
  if (submitData.startTime) {
    submitData.startTime = moment(submitData.startTime).format('YYYY-MM-DD HH:mm:ss')
  }
  if (submitData.endTime) {
    submitData.endTime = moment(submitData.endTime).format('YYYY-MM-DD HH:mm:ss')
  }
  
  return request({
    url: `/comprehensive/usmWarningInfo/save`,
    method: 'post',
    data: submitData
  })
}

/**
 * 更新预警信息
 * @param {object} data 预警信息数据
 * @returns {Promise}
 */
export function updateWarningInfo(data) {
  // 处理时间字段格式
  const submitData = { ...data }
  if (submitData.warningTime) {
    submitData.warningTime = moment(submitData.warningTime).format('YYYY-MM-DD HH:mm:ss')
  }
  if (submitData.publishTime) {
    submitData.publishTime = moment(submitData.publishTime).format('YYYY-MM-DD HH:mm:ss')
  }
  if (submitData.startTime) {
    submitData.startTime = moment(submitData.startTime).format('YYYY-MM-DD HH:mm:ss')
  }
  if (submitData.endTime) {
    submitData.endTime = moment(submitData.endTime).format('YYYY-MM-DD HH:mm:ss')
  }
  
  return request({
    url: `/comprehensive/usmWarningInfo/update`,
    method: 'post',
    data: submitData
  })
}

/**
 * 获取预警信息详情
 * @param {string} id 预警信息ID
 * @returns {Promise}
 */
export function getWarningInfoDetail(id) {
  return request({
    url: `/comprehensive/usmWarningInfo/${id}`,
    method: 'get'
  })
}

/**
 * 删除预警信息
 * @param {string} id 预警信息ID
 * @returns {Promise}
 */
export function deleteWarningInfo(id) {
  return request({
    url: `/comprehensive/usmWarningInfo/${id}`,
    method: 'delete'
  })
}

/**
 * 获取关联预案
 * @param {string} warningType 预警类型
 * @returns {Promise}
 */
export function getRelatedScheme(warningType) {
  return request({
    url: `/comprehensive/usmWarningInfo/getRelatedScheme`,
    method: 'get',
    params: { warningType }
  })
}

// 预警信息管理常量定义

// 预警级别选项
export const WARNING_LEVEL_OPTIONS = [
  { label: '一级预警', value: 7002201 },
  { label: '二级预警', value: 7002202 },
  { label: '三级预警', value: 7002203 }
]

// 预警状态选项
export const WARNING_STATUS_OPTIONS = [
  { label: '待处置', value: 7002301 },
  { label: '处置中', value: 7002302 },
  { label: '已处置', value: 7002303 },
  { label: '已解除', value: 7002304 }
]

// 预警类型选项
export const WARNING_TYPE_OPTIONS = [
  { label: '管网爆管预警', value: 7002101 },
  { label: '泄露量预警', value: 7002102 },
  { label: '暴雨洪涝预警', value: 7002103 },
  { label: '排水管网淤积预警', value: 7002104 },
  { label: '排水管网溢流预警', value: 7002105 },
  { label: '桥梁结构损伤预警', value: 7002106 },
  { label: '极端天气预警', value: 7002107 },
  { label: '其他预警', value: 7002108 }
]

// 预警信息处置相关接口

/**
 * 获取预警处置列表第一层
 * @param {object} data 查询参数 {warningId}
 * @returns {Promise}
 */
export function getWarningDealList(data) {
  return request({
    url: `/comprehensive/usmWarningDeal/list`,
    method: 'post',
    data
  })
}

/**
 * 获取预警处置列表第二层
 * @param {object} data 查询参数 {dealId}
 * @returns {Promise}
 */
export function getWarningDealStatusList(data) {
  return request({
    url: `/comprehensive/usmWarningDealStatus/list`,
    method: 'post',
    data
  })
}

/**
 * 保存预警处置状态
 * @param {object} data 处置数据
 * @returns {Promise}
 */
export function saveWarningDealStatus(data) {
  // 处理时间字段格式
  const submitData = { ...data }
  if (submitData.dealTime) {
    submitData.dealTime = moment(submitData.dealTime).format('YYYY-MM-DD HH:mm:ss')
  }
  
  return request({
    url: `/comprehensive/usmWarningDealStatus/save`,
    method: 'post',
    data: submitData
  })
}

/**
 * 更新预警处置状态
 * @param {object} data 处置数据
 * @returns {Promise}
 */
export function updateWarningDealStatus(data) {
  // 处理时间字段格式
  const submitData = { ...data }
  if (submitData.dealTime) {
    submitData.dealTime = moment(submitData.dealTime).format('YYYY-MM-DD HH:mm:ss')
  }
  
  return request({
    url: `/comprehensive/usmWarningDealStatus/update`,
    method: 'post',
    data: submitData
  })
}

/**
 * 获取预警处置详情
 * @param {string} id 处置ID
 * @returns {Promise}
 */
export function getWarningDealStatusDetail(id) {
  return request({
    url: `/comprehensive/usmWarningDealStatus/${id}`,
    method: 'get'
  })
}

/**
 * 删除预警处置
 * @param {string} id 处置ID
 * @returns {Promise}
 */
export function deleteWarningDealStatus(id) {
  return request({
    url: `/comprehensive/usmWarningDealStatus/${id}`,
    method: 'delete'
  })
}

// 预警处置常量定义

// 处置状态选项
export const DEAL_STATUS_OPTIONS = [
  { label: '发布预警', value: 7001601 },
  { label: '现场处置', value: 7001602 },
  { label: '处置完成', value: 7001603 },
  { label: '预警解除', value: 7001604 }
]

// 处理状态选项
export const HANDLE_STATUS_OPTIONS = [
  { label: '已处置', value: 7001701 },
  { label: '处置中', value: 7001702 }
]

// 隐患排查治理相关接口

/**
 * 获取隐患状态统计
 * @returns {Promise}
 */
export function getHiddenDangerStatusStatistics() {
  return request({
    url: `/comprehensive/usmRiskHiddenDanger/statisticsByStatus`,
    method: 'get'
  })
}

/**
 * 获取隐患等级统计
 * @returns {Promise}
 */
export function getHiddenDangerLevelStatistics() {
  return request({
    url: `/comprehensive/usmRiskHiddenDanger/statisticsByLevel`,
    method: 'get'
  })
}

/**
 * 分页查询隐患列表
 * @param {number} pageNum 页码
 * @param {number} pageSize 每页大小
 * @param {object} params 查询参数
 * @returns {Promise}
 */
export function getHiddenDangerPage(pageNum, pageSize, params = {}) {
  return request({
    url: `/comprehensive/usmRiskHiddenDanger/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  })
}

/**
 * 保存隐患信息
 * @param {object} data 隐患数据
 * @returns {Promise}
 */
export function saveHiddenDanger(data) {
  // 处理时间字段格式
  const submitData = { ...data }
  if (submitData.reportTime) {
    submitData.reportTime = moment(submitData.reportTime).format('YYYY-MM-DD HH:mm:ss')
  }
  if (submitData.rectificationDeadline) {
    submitData.rectificationDeadline = moment(submitData.rectificationDeadline).format('YYYY-MM-DD HH:mm:ss')
  }
  
  return request({
    url: `/comprehensive/usmRiskHiddenDanger/save`,
    method: 'post',
    data: submitData
  })
}

/**
 * 更新隐患信息
 * @param {object} data 隐患数据
 * @returns {Promise}
 */
export function updateHiddenDanger(data) {
  // 处理时间字段格式
  const submitData = { ...data }
  if (submitData.reportTime) {
    submitData.reportTime = moment(submitData.reportTime).format('YYYY-MM-DD HH:mm:ss')
  }
  if (submitData.rectificationDeadline) {
    submitData.rectificationDeadline = moment(submitData.rectificationDeadline).format('YYYY-MM-DD HH:mm:ss')
  }
  
  return request({
    url: `/comprehensive/usmRiskHiddenDanger/update`,
    method: 'post',
    data: submitData
  })
}

/**
 * 获取隐患详情
 * @param {string} id 隐患ID
 * @returns {Promise}
 */
export function getHiddenDangerDetail(id) {
  return request({
    url: `/comprehensive/usmRiskHiddenDanger/${id}`,
    method: 'get'
  })
}

/**
 * 删除隐患
 * @param {string} id 隐患ID
 * @returns {Promise}
 */
export function deleteHiddenDanger(id) {
  return request({
    url: `/comprehensive/usmRiskHiddenDanger/${id}`,
    method: 'delete'
  })
}

/**
 * 获取隐患整改列表
 * @param {string} id 隐患ID
 * @returns {Promise}
 */
export function getHiddenDangerHandleList(id) {
  return request({
    url: `/comprehensive/usmRiskHiddenDanger/handleList/${id}`,
    method: 'get'
  })
}

/**
 * 新增/编辑隐患整改
 * @param {object} data 整改数据
 * @returns {Promise}
 */
export function addHiddenDangerHandle(data) {
  // 处理时间字段格式
  const submitData = { ...data }
  if (submitData.dealTime) {
    submitData.dealTime = moment(submitData.dealTime).format('YYYY-MM-DD HH:mm:ss')
  }
  
  return request({
    url: `/comprehensive/usmRiskHiddenDanger/addHandle`,
    method: 'post',
    data: submitData
  })
}

/**
 * 删除隐患整改
 * @param {string} id 整改ID
 * @returns {Promise}
 */
export function deleteHiddenDangerHandle(id) {
  return request({
    url: `/comprehensive/usmRiskHiddenDanger/deleteHandle/${id}`,
    method: 'delete'
  })
}

/**
 * 隐患复查
 * @param {object} data 复查数据
 * @returns {Promise}
 */
export function reviewHiddenDanger(data) {
  // 处理时间字段格式
  const submitData = { ...data }
  if (submitData.dealTime) {
    submitData.dealTime = moment(submitData.dealTime).format('YYYY-MM-DD HH:mm:ss')
  }
  
  return request({
    url: `/comprehensive/usmRiskHiddenDanger/review`,
    method: 'post',
    data: submitData
  })
}

/**
 * 获取隐患整改情况时间线
 * @param {string} dangerId 隐患ID
 * @returns {Promise}
 */
export function getHiddenDangerStatusList(dangerId) {
  return request({
    url: `/comprehensive/usmRiskHiddenDangerStatus/list`,
    method: 'post',
    data: { dangerId }
  })
}

// 应急预案管理相关接口

/**
 * 分页查询应急预案列表
 * @param {number} pageNum 页码
 * @param {number} pageSize 每页大小
 * @param {object} params 查询参数
 * @returns {Promise}
 */
export function getEmergencySchemiePage(pageNum, pageSize, params = {}) {
  return request({
    url: `/comprehensive/usmEmergencyScheme/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  })
}

/**
 * 保存应急预案
 * @param {object} data 应急预案数据
 * @returns {Promise}
 */
export function saveEmergencyScheme(data) {
  // 处理时间字段格式
  const submitData = { ...data }
  if (submitData.issuedTime) {
    submitData.issuedTime = moment(submitData.issuedTime).format('YYYY-MM-DD HH:mm:ss')
  }
  
  return request({
    url: `/comprehensive/usmEmergencyScheme/save`,
    method: 'post',
    data: submitData
  })
}

/**
 * 更新应急预案
 * @param {object} data 应急预案数据
 * @returns {Promise}
 */
export function updateEmergencyScheme(data) {
  // 处理时间字段格式
  const submitData = { ...data }
  if (submitData.issuedTime) {
    submitData.issuedTime = moment(submitData.issuedTime).format('YYYY-MM-DD HH:mm:ss')
  }
  
  return request({
    url: `/comprehensive/usmEmergencyScheme/update`,
    method: 'post',
    data: submitData
  })
}

/**
 * 获取应急预案详情
 * @param {string} id 应急预案ID
 * @returns {Promise}
 */
export function getEmergencySchemeDetail(id) {
  return request({
    url: `/comprehensive/usmEmergencyScheme/${id}`,
    method: 'get'
  })
}

/**
 * 删除应急预案
 * @param {string} id 应急预案ID
 * @returns {Promise}
 */
export function deleteEmergencyScheme(id) {
  return request({
    url: `/comprehensive/usmEmergencyScheme/${id}`,
    method: 'delete'
  })
}

// 应急资源管理相关接口

// 救援队伍相关接口
/**
 * 分页查询救援队伍列表
 * @param {number} pageNum 页码
 * @param {number} pageSize 每页大小
 * @param {object} params 查询参数
 * @returns {Promise}
 */
export function getEmergencyTeamPage(pageNum, pageSize, params = {}) {
  return request({
    url: `/comprehensive/usmEmergencyTeam/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  })
}

/**
 * 保存救援队伍
 * @param {object} data 救援队伍数据
 * @returns {Promise}
 */
export function saveEmergencyTeam(data) {
  const submitData = { ...data }
  if (submitData.establishTime) {
    submitData.establishTime = moment(submitData.establishTime).format('YYYY-MM-DD HH:mm:ss')
  }
  
  return request({
    url: `/comprehensive/usmEmergencyTeam/save`,
    method: 'post',
    data: submitData
  })
}

/**
 * 更新救援队伍
 * @param {object} data 救援队伍数据
 * @returns {Promise}
 */
export function updateEmergencyTeam(data) {
  const submitData = { ...data }
  if (submitData.establishTime) {
    submitData.establishTime = moment(submitData.establishTime).format('YYYY-MM-DD HH:mm:ss')
  }
  
  return request({
    url: `/comprehensive/usmEmergencyTeam/update`,
    method: 'post',
    data: submitData
  })
}

/**
 * 获取救援队伍详情
 * @param {string} id 救援队伍ID
 * @returns {Promise}
 */
export function getEmergencyTeamDetail(id) {
  return request({
    url: `/comprehensive/usmEmergencyTeam/${id}`,
    method: 'get'
  })
}

/**
 * 删除救援队伍
 * @param {string} id 救援队伍ID
 * @returns {Promise}
 */
export function deleteEmergencyTeam(id) {
  return request({
    url: `/comprehensive/usmEmergencyTeam/${id}`,
    method: 'delete'
  })
}

/**
 * 获取救援队伍列表
 * @param {object} params 查询参数
 * @returns {Promise}
 */
export function getEmergencyTeamList(params = {}) {
  return request({
    url: `/comprehensive/usmEmergencyTeam/list`,
    method: 'post',
    data: params
  })
}

// 应急物资相关接口
/**
 * 分页查询应急物资列表
 * @param {number} pageNum 页码
 * @param {number} pageSize 每页大小
 * @param {object} params 查询参数
 * @returns {Promise}
 */
export function getEmergencySuppliesPage(pageNum, pageSize, params = {}) {
  return request({
    url: `/comprehensive/usmEmergencySupplies/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  })
}

/**
 * 保存应急物资
 * @param {object} data 应急物资数据
 * @returns {Promise}
 */
export function saveEmergencySupplies(data) {
  return request({
    url: `/comprehensive/usmEmergencySupplies/save`,
    method: 'post',
    data
  })
}

/**
 * 更新应急物资
 * @param {object} data 应急物资数据
 * @returns {Promise}
 */
export function updateEmergencySupplies(data) {
  return request({
    url: `/comprehensive/usmEmergencySupplies/update`,
    method: 'post',
    data
  })
}

/**
 * 获取应急物资详情
 * @param {string} id 应急物资ID
 * @returns {Promise}
 */
export function getEmergencySuppliesDetail(id) {
  return request({
    url: `/comprehensive/usmEmergencySupplies/${id}`,
    method: 'get'
  })
}

/**
 * 删除应急物资
 * @param {string} id 应急物资ID
 * @returns {Promise}
 */
export function deleteEmergencySupplies(id) {
  return request({
    url: `/comprehensive/usmEmergencySupplies/${id}`,
    method: 'delete'
  })
}

// 避难场所相关接口
/**
 * 分页查询避难场所列表
 * @param {number} pageNum 页码
 * @param {number} pageSize 每页大小
 * @param {object} params 查询参数
 * @returns {Promise}
 */
export function getEmergencyShelterPage(pageNum, pageSize, params = {}) {
  return request({
    url: `/comprehensive/usmEmergencyShelter/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  })
}

/**
 * 保存避难场所
 * @param {object} data 避难场所数据
 * @returns {Promise}
 */
export function saveEmergencyShelter(data) {
  return request({
    url: `/comprehensive/usmEmergencyShelter/save`,
    method: 'post',
    data
  })
}

/**
 * 更新避难场所
 * @param {object} data 避难场所数据
 * @returns {Promise}
 */
export function updateEmergencyShelter(data) {
  return request({
    url: `/comprehensive/usmEmergencyShelter/update`,
    method: 'post',
    data
  })
}

/**
 * 获取避难场所详情
 * @param {string} id 避难场所ID
 * @returns {Promise}
 */
export function getEmergencyShelterDetail(id) {
  return request({
    url: `/comprehensive/usmEmergencyShelter/${id}`,
    method: 'get'
  })
}

/**
 * 删除避难场所
 * @param {string} id 避难场所ID
 * @returns {Promise}
 */
export function deleteEmergencyShelter(id) {
  return request({
    url: `/comprehensive/usmEmergencyShelter/${id}`,
    method: 'delete'
  })
}

// 救援人员相关接口
/**
 * 分页查询救援人员列表
 * @param {number} pageNum 页码
 * @param {number} pageSize 每页大小
 * @param {object} params 查询参数
 * @returns {Promise}
 */
export function getEmergencyRespondersPage(pageNum, pageSize, params = {}) {
  return request({
    url: `/comprehensive/usmEmergencyResponders/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  })
}

/**
 * 保存救援人员
 * @param {object} data 救援人员数据
 * @returns {Promise}
 */
export function saveEmergencyResponders(data) {
  return request({
    url: `/comprehensive/usmEmergencyResponders/save`,
    method: 'post',
    data
  })
}

/**
 * 更新救援人员
 * @param {object} data 救援人员数据
 * @returns {Promise}
 */
export function updateEmergencyResponders(data) {
  return request({
    url: `/comprehensive/usmEmergencyResponders/update`,
    method: 'post',
    data
  })
}

/**
 * 获取救援人员详情
 * @param {string} id 救援人员ID
 * @returns {Promise}
 */
export function getEmergencyRespondersDetail(id) {
  return request({
    url: `/comprehensive/usmEmergencyResponders/${id}`,
    method: 'get'
  })
}

/**
 * 删除救援人员
 * @param {string} id 救援人员ID
 * @returns {Promise}
 */
export function deleteEmergencyResponders(id) {
  return request({
    url: `/comprehensive/usmEmergencyResponders/${id}`,
    method: 'delete'
  })
}

// 医疗机构相关接口
/**
 * 分页查询医疗机构列表
 * @param {number} pageNum 页码
 * @param {number} pageSize 每页大小
 * @param {object} params 查询参数
 * @returns {Promise}
 */
export function getEmergencyHospitalPage(pageNum, pageSize, params = {}) {
  return request({
    url: `/comprehensive/usmEmergencyHospital/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  })
}

/**
 * 保存医疗机构
 * @param {object} data 医疗机构数据
 * @returns {Promise}
 */
export function saveEmergencyHospital(data) {
  return request({
    url: `/comprehensive/usmEmergencyHospital/save`,
    method: 'post',
    data
  })
}

/**
 * 更新医疗机构
 * @param {object} data 医疗机构数据
 * @returns {Promise}
 */
export function updateEmergencyHospital(data) {
  return request({
    url: `/comprehensive/usmEmergencyHospital/update`,
    method: 'post',
    data
  })
}

/**
 * 获取医疗机构详情
 * @param {string} id 医疗机构ID
 * @returns {Promise}
 */
export function getEmergencyHospitalDetail(id) {
  return request({
    url: `/comprehensive/usmEmergencyHospital/${id}`,
    method: 'get'
  })
}

/**
 * 删除医疗机构
 * @param {string} id 医疗机构ID
 * @returns {Promise}
 */
export function deleteEmergencyHospital(id) {
  return request({
    url: `/comprehensive/usmEmergencyHospital/${id}`,
    method: 'delete'
  })
}

// 应急仓库相关接口
/**
 * 分页查询应急仓库列表
 * @param {number} pageNum 页码
 * @param {number} pageSize 每页大小
 * @param {object} params 查询参数
 * @returns {Promise}
 */
export function getEmergencyStorePage(pageNum, pageSize, params = {}) {
  return request({
    url: `/comprehensive/usmEmergencyStore/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  })
}

/**
 * 保存应急仓库
 * @param {object} data 应急仓库数据
 * @returns {Promise}
 */
export function saveEmergencyStore(data) {
  const submitData = { ...data }
  if (submitData.investTime) {
    submitData.investTime = moment(submitData.investTime).format('YYYY-MM-DD HH:mm:ss')
  }
  
  return request({
    url: `/comprehensive/usmEmergencyStore/save`,
    method: 'post',
    data: submitData
  })
}

/**
 * 更新应急仓库
 * @param {object} data 应急仓库数据
 * @returns {Promise}
 */
export function updateEmergencyStore(data) {
  const submitData = { ...data }
  if (submitData.investTime) {
    submitData.investTime = moment(submitData.investTime).format('YYYY-MM-DD HH:mm:ss')
  }
  
  return request({
    url: `/comprehensive/usmEmergencyStore/update`,
    method: 'post',
    data: submitData
  })
}

/**
 * 获取应急仓库详情
 * @param {string} id 应急仓库ID
 * @returns {Promise}
 */
export function getEmergencyStoreDetail(id) {
  return request({
    url: `/comprehensive/usmEmergencyStore/${id}`,
    method: 'get'
  })
}

/**
 * 删除应急仓库
 * @param {string} id 应急仓库ID
 * @returns {Promise}
 */
export function deleteEmergencyStore(id) {
  return request({
    url: `/comprehensive/usmEmergencyStore/${id}`,
    method: 'delete'
  })
}

/**
 * 获取应急仓库列表
 * @param {object} params 查询参数
 * @returns {Promise}
 */
export function getEmergencyStoreList(params = {}) {
  return request({
    url: `/comprehensive/usmEmergencyStore/list`,
    method: 'post',
    data: params
  })
}

// 应急专家管理相关接口

/**
 * 分页查询应急专家列表
 * @param {number} pageNum 页码
 * @param {number} pageSize 每页大小
 * @param {object} params 查询参数
 * @returns {Promise}
 */
export function getEmergencyExpertPage(pageNum, pageSize, params = {}) {
  return request({
    url: `/comprehensive/usmEmergencyExpert/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  })
}

/**
 * 保存应急专家
 * @param {object} data 应急专家数据
 * @returns {Promise}
 */
export function saveEmergencyExpert(data) {
  return request({
    url: `/comprehensive/usmEmergencyExpert/save`,
    method: 'post',
    data
  })
}

/**
 * 更新应急专家
 * @param {object} data 应急专家数据
 * @returns {Promise}
 */
export function updateEmergencyExpert(data) {
  return request({
    url: `/comprehensive/usmEmergencyExpert/update`,
    method: 'post',
    data
  })
}

/**
 * 获取应急专家详情
 * @param {string} id 应急专家ID
 * @returns {Promise}
 */
export function getEmergencyExpertDetail(id) {
  return request({
    url: `/comprehensive/usmEmergencyExpert/${id}`,
    method: 'get'
  })
}

/**
 * 删除应急专家
 * @param {string} id 应急专家ID
 * @returns {Promise}
 */
export function deleteEmergencyExpert(id) {
  return request({
    url: `/comprehensive/usmEmergencyExpert/${id}`,
    method: 'delete'
  })
}

// 应急专家管理常量定义

// 专家性别选项
export const EXPERT_GENDER_OPTIONS = [
  { label: '男', value: '男' },
  { label: '女', value: '女' }
]

// 专业领域选项
export const PROFESSIONAL_FIELD_OPTIONS = [
  { label: '燃气安全', value: '燃气安全' },
  { label: '供热管理', value: '供热管理' },
  { label: '排水工程', value: '排水工程' },
  { label: '桥梁工程', value: '桥梁工程' },
  { label: '应急救援', value: '应急救援' },
  { label: '环境保护', value: '环境保护' },
  { label: '安全生产', value: '安全生产' },
  { label: '其他', value: '其他' }
]

// 值班管理相关接口

// 排班管理相关接口
/**
 * 分页查询排班列表
 * @param {number} pageNum 页码
 * @param {number} pageSize 每页大小
 * @param {object} params 查询参数
 * @returns {Promise}
 */
export function getDutySchedulePage(pageNum, pageSize, params = {}) {
  // 处理时间字段格式
  const submitData = { ...params }
  if (submitData.scheduleTime) {
    submitData.scheduleTime = moment(submitData.scheduleTime).format('YYYY-MM-DD HH:mm:ss')
  }
  
  return request({
    url: `/comprehensive/usmDutySchedule/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: submitData
  })
}

/**
 * 保存排班
 * @param {object} data 排班数据
 * @returns {Promise}
 */
export function saveDutySchedule(data) {
  // 处理时间字段格式
  const submitData = { ...data }
  if (submitData.scheduleTime) {
    submitData.scheduleTime = moment(submitData.scheduleTime).format('YYYY-MM-DD HH:mm:ss')
  }
  
  return request({
    url: `/comprehensive/usmDutySchedule/save`,
    method: 'post',
    data: submitData
  })
}

/**
 * 更新排班
 * @param {object} data 排班数据
 * @returns {Promise}
 */
export function updateDutySchedule(data) {
  // 处理时间字段格式
  const submitData = { ...data }
  if (submitData.scheduleTime) {
    submitData.scheduleTime = moment(submitData.scheduleTime).format('YYYY-MM-DD HH:mm:ss')
  }
  
  return request({
    url: `/comprehensive/usmDutySchedule/update`,
    method: 'post',
    data: submitData
  })
}

/**
 * 获取排班详情
 * @param {string} id 排班ID
 * @returns {Promise}
 */
export function getDutyScheduleDetail(id) {
  return request({
    url: `/comprehensive/usmDutySchedule/${id}`,
    method: 'get'
  })
}

/**
 * 删除排班
 * @param {string} id 排班ID
 * @returns {Promise}
 */
export function deleteDutySchedule(id) {
  return request({
    url: `/comprehensive/usmDutySchedule/${id}`,
    method: 'delete'
  })
}

/**
 * 自动排班
 * @param {object} data 自动排班参数
 * @returns {Promise}
 */
export function autoSchedule(data) {
  // 处理时间字段格式
  const submitData = { ...data }
  if (submitData.restDates && submitData.restDates.length > 0) {
    submitData.restDates = submitData.restDates.map(date => 
      moment(date).format('YYYY-MM-DD HH:mm:ss')
    )
  }
  if (submitData.shiftDates && submitData.shiftDates.length > 0) {
    submitData.shiftDates = submitData.shiftDates.map(date => 
      moment(date).format('YYYY-MM-DD HH:mm:ss')
    )
  }
  
  return request({
    url: `/comprehensive/usmDutySchedule/autoSchedule`,
    method: 'post',
    data: submitData
  })
}

// 值班人员管理相关接口
/**
 * 分页查询值班人员列表
 * @param {number} pageNum 页码
 * @param {number} pageSize 每页大小
 * @param {object} params 查询参数
 * @returns {Promise}
 */
export function getDutyUserPage(pageNum, pageSize, params = {}) {
  return request({
    url: `/comprehensive/usmDutyUser/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  })
}

/**
 * 保存值班人员
 * @param {object} data 值班人员数据
 * @returns {Promise}
 */
export function saveDutyUser(data) {
  return request({
    url: `/comprehensive/usmDutyUser/save`,
    method: 'post',
    data
  })
}

/**
 * 更新值班人员
 * @param {object} data 值班人员数据
 * @returns {Promise}
 */
export function updateDutyUser(data) {
  return request({
    url: `/comprehensive/usmDutyUser/update`,
    method: 'post',
    data
  })
}

/**
 * 获取值班人员详情
 * @param {string} id 值班人员ID
 * @returns {Promise}
 */
export function getDutyUserDetail(id) {
  return request({
    url: `/comprehensive/usmDutyUser/${id}`,
    method: 'get'
  })
}

/**
 * 删除值班人员
 * @param {string} id 值班人员ID
 * @returns {Promise}
 */
export function deleteDutyUser(id) {
  return request({
    url: `/comprehensive/usmDutyUser/${id}`,
    method: 'delete'
  })
}

// 班次管理相关接口
/**
 * 分页查询班次列表
 * @param {number} pageNum 页码
 * @param {number} pageSize 每页大小
 * @param {object} params 查询参数
 * @returns {Promise}
 */
export function getDutyShiftPage(pageNum, pageSize, params = {}) {
  // 处理时间字段格式
  const submitData = { ...params }
  if (submitData.startTime) {
    submitData.startTime = moment(submitData.startTime).format('YYYY-MM-DD HH:mm:ss')
  }
  if (submitData.endTime) {
    submitData.endTime = moment(submitData.endTime).format('YYYY-MM-DD HH:mm:ss')
  }
  
  return request({
    url: `/comprehensive/usmDutyShift/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: submitData
  })
}

/**
 * 保存班次
 * @param {object} data 班次数据
 * @returns {Promise}
 */
export function saveDutyShift(data) {
  // 处理时间字段格式
  const submitData = { ...data }
  if (submitData.startTime) {
    submitData.startTime = moment(submitData.startTime).format('YYYY-MM-DD HH:mm:ss')
  }
  if (submitData.endTime) {
    submitData.endTime = moment(submitData.endTime).format('YYYY-MM-DD HH:mm:ss')
  }
  
  return request({
    url: `/comprehensive/usmDutyShift/save`,
    method: 'post',
    data: submitData
  })
}

/**
 * 更新班次
 * @param {object} data 班次数据
 * @returns {Promise}
 */
export function updateDutyShift(data) {
  // 处理时间字段格式
  const submitData = { ...data }
  if (submitData.startTime) {
    submitData.startTime = moment(submitData.startTime).format('YYYY-MM-DD HH:mm:ss')
  }
  if (submitData.endTime) {
    submitData.endTime = moment(submitData.endTime).format('YYYY-MM-DD HH:mm:ss')
  }
  
  return request({
    url: `/comprehensive/usmDutyShift/update`,
    method: 'post',
    data: submitData
  })
}

/**
 * 获取班次详情
 * @param {string} id 班次ID
 * @returns {Promise}
 */
export function getDutyShiftDetail(id) {
  return request({
    url: `/comprehensive/usmDutyShift/${id}`,
    method: 'get'
  })
}

/**
 * 删除班次
 * @param {string} id 班次ID
 * @returns {Promise}
 */
export function deleteDutyShift(id) {
  return request({
    url: `/comprehensive/usmDutyShift/${id}`,
    method: 'delete'
  })
}

/**
 * 获取班次列表
 * @param {object} params 查询参数
 * @returns {Promise}
 */
export function getDutyShiftList(params = {}) {
  // 处理时间字段格式
  const submitData = { ...params }
  if (submitData.startTime) {
    submitData.startTime = moment(submitData.startTime).format('YYYY-MM-DD HH:mm:ss')
  }
  if (submitData.endTime) {
    submitData.endTime = moment(submitData.endTime).format('YYYY-MM-DD HH:mm:ss')
  }
  
  return request({
    url: `/comprehensive/usmDutyShift/list`,
    method: 'post',
    data: submitData
  })
}

/**
 * 获取值班人员列表
 * @param {object} params 查询参数
 * @returns {Promise}
 */
export function getDutyUserList(params = {}) {
  return request({
    url: `/comprehensive/usmDutyUser/list`,
    method: 'post',
    data: params
  })
}

// 交接班记录管理相关接口
/**
 * 分页查询交接班记录列表
 * @param {number} pageNum 页码
 * @param {number} pageSize 每页大小
 * @param {object} params 查询参数
 * @returns {Promise}
 */
export function getDutyShiftChangePage(pageNum, pageSize, params = {}) {
  // 处理时间字段格式
  const submitData = { ...params }
  if (submitData.scheduleTime) {
    submitData.scheduleTime = moment(submitData.scheduleTime).format('YYYY-MM-DD HH:mm:ss')
  }
  if (submitData.handoverTime) {
    submitData.handoverTime = moment(submitData.handoverTime).format('YYYY-MM-DD HH:mm:ss')
  }
  if (submitData.takeoverTime) {
    submitData.takeoverTime = moment(submitData.takeoverTime).format('YYYY-MM-DD HH:mm:ss')
  }
  
  return request({
    url: `/comprehensive/usmDutyShiftChange/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: submitData
  })
}

/**
 * 保存交接班记录
 * @param {object} data 交接班记录数据
 * @returns {Promise}
 */
export function saveDutyShiftChange(data) {
  // 处理时间字段格式
  const submitData = { ...data }
  if (submitData.scheduleTime) {
    submitData.scheduleTime = moment(submitData.scheduleTime).format('YYYY-MM-DD HH:mm:ss')
  }
  if (submitData.handoverTime) {
    submitData.handoverTime = moment(submitData.handoverTime).format('YYYY-MM-DD HH:mm:ss')
  }
  if (submitData.takeoverTime) {
    submitData.takeoverTime = moment(submitData.takeoverTime).format('YYYY-MM-DD HH:mm:ss')
  }
  
  return request({
    url: `/comprehensive/usmDutyShiftChange/save`,
    method: 'post',
    data: submitData
  })
}

/**
 * 更新交接班记录
 * @param {object} data 交接班记录数据
 * @returns {Promise}
 */
export function updateDutyShiftChange(data) {
  // 处理时间字段格式
  const submitData = { ...data }
  if (submitData.scheduleTime) {
    submitData.scheduleTime = moment(submitData.scheduleTime).format('YYYY-MM-DD HH:mm:ss')
  }
  if (submitData.handoverTime) {
    submitData.handoverTime = moment(submitData.handoverTime).format('YYYY-MM-DD HH:mm:ss')
  }
  if (submitData.takeoverTime) {
    submitData.takeoverTime = moment(submitData.takeoverTime).format('YYYY-MM-DD HH:mm:ss')
  }
  
  return request({
    url: `/comprehensive/usmDutyShiftChange/update`,
    method: 'post',
    data: submitData
  })
}

/**
 * 获取交接班记录详情
 * @param {string} id 交接班记录ID
 * @returns {Promise}
 */
export function getDutyShiftChangeDetail(id) {
  return request({
    url: `/comprehensive/usmDutyShiftChange/${id}`,
    method: 'get'
  })
}

/**
 * 删除交接班记录
 * @param {string} id 交接班记录ID
 * @returns {Promise}
 */
export function deleteDutyShiftChange(id) {
  return request({
    url: `/comprehensive/usmDutyShiftChange/${id}`,
    method: 'delete'
  })
}

// 值班统计相关接口
/**
 * 单位值班人数统计
 * @returns {Promise}
 */
export function getDutyUnitStatistics() {
  return request({
    url: `/comprehensive/usmDutySchedule/unitStatistics`,
    method: 'post',
    data: {}
  })
}

/**
 * 值班人员天数统计
 * @param {object} params 查询参数 {startTime, endTime}
 * @returns {Promise}
 */
export function getDutyPersonStatistics(params = {}) {
  // 处理时间字段格式
  const submitData = { ...params }
  if (submitData.startTime) {
    submitData.startTime = moment(submitData.startTime).format('YYYY-MM-DD HH:mm:ss')
  }
  if (submitData.endTime) {
    submitData.endTime = moment(submitData.endTime).format('YYYY-MM-DD HH:mm:ss')
  }
  
  return request({
    url: `/comprehensive/usmDutySchedule/userStatistics`,
    method: 'post',
    data: submitData
  })
}

// 巡检管理相关接口

/**
 * 分页查询巡检任务列表
 * @param {number} pageNum 页码
 * @param {number} pageSize 每页大小
 * @param {object} params 查询参数
 * @returns {Promise}
 */
export function getInspectionTaskPage(pageNum, pageSize, params = {}) {
  return request({
    url: `/comprehensive/usmInspectionTask/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  })
}

/**
 * 保存巡检任务
 * @param {object} data 巡检任务数据
 * @returns {Promise}
 */
export function saveInspectionTask(data) {
  // 处理时间字段格式
  const submitData = { ...data }
  if (submitData.startTime) {
    submitData.startTime = moment(submitData.startTime).format('YYYY-MM-DD HH:mm:ss')
  }
  if (submitData.endTime) {
    submitData.endTime = moment(submitData.endTime).format('YYYY-MM-DD HH:mm:ss')
  }
  
  return request({
    url: `/comprehensive/usmInspectionTask/save`,
    method: 'post',
    data: submitData
  })
}

/**
 * 更新巡检任务
 * @param {object} data 巡检任务数据
 * @returns {Promise}
 */
export function updateInspectionTask(data) {
  // 处理时间字段格式
  const submitData = { ...data }
  if (submitData.startTime) {
    submitData.startTime = moment(submitData.startTime).format('YYYY-MM-DD HH:mm:ss')
  }
  if (submitData.endTime) {
    submitData.endTime = moment(submitData.endTime).format('YYYY-MM-DD HH:mm:ss')
  }
  
  return request({
    url: `/comprehensive/usmInspectionTask/update`,
    method: 'post',
    data: submitData
  })
}

/**
 * 获取巡检任务详情
 * @param {string} id 巡检任务ID
 * @returns {Promise}
 */
export function getInspectionTaskDetail(id) {
  return request({
    url: `/comprehensive/usmInspectionTask/${id}`,
    method: 'get'
  })
}

/**
 * 删除巡检任务
 * @param {string} id 巡检任务ID
 * @returns {Promise}
 */
export function deleteInspectionTask(id) {
  return request({
    url: `/comprehensive/usmInspectionTask/${id}`,
    method: 'delete'
  })
}

/**
 * 获取巡检设备列表
 * @param {object} params 查询参数
 * @returns {Promise}
 */
export function getInspectionDeviceList(params = {}) {
  return request({
    url: `/comprehensive/usmInspectionTaskDevice/list`,
    method: 'post',
    data: params
  })
}

// 巡检工单管理相关接口

/**
 * 分页查询巡检工单列表
 * @param {number} pageNum 页码
 * @param {number} pageSize 每页大小
 * @param {object} params 查询参数
 * @returns {Promise}
 */
export function getInspectionRecordPage(pageNum, pageSize, params = {}) {
  return request({
    url: `/comprehensive/usmInspectionRecord/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  })
}

/**
 * 获取巡检工单详情
 * @param {string} id 巡检工单ID
 * @returns {Promise}
 */
export function getInspectionRecordDetail(id) {
  return request({
    url: `/comprehensive/usmInspectionRecord/${id}`,
    method: 'get'
  })
}

/**
 * 巡检工单填报
 * @param {object} data 巡检工单数据
 * @returns {Promise}
 */
export function reportInspectionRecord(data) {
  // 处理时间字段格式
  const submitData = { ...data }
  if (submitData.completeTime) {
    submitData.completeTime = moment(submitData.completeTime).format('YYYY-MM-DD HH:mm:ss')
  }
  
  return request({
    url: `/comprehensive/usmInspectionRecord/report`,
    method: 'post',
    data: submitData
  })
}

// 设备维修管理相关接口

/**
 * 分页查询设备维修列表
 * @param {number} pageNum 页码
 * @param {number} pageSize 每页大小
 * @param {object} params 查询参数
 * @returns {Promise}
 */
export function getDeviceRepairPage(pageNum, pageSize, params = {}) {
  return request({
    url: `/comprehensive/usmMonitorDeviceRepair/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  })
}

/**
 * 保存设备维修
 * @param {object} data 设备维修数据
 * @returns {Promise}
 */
export function saveDeviceRepair(data) {
  // 处理时间字段格式
  const submitData = { ...data }
  if (submitData.repairTime) {
    submitData.repairTime = moment(submitData.repairTime).format('YYYY-MM-DD HH:mm:ss')
  }
  
  return request({
    url: `/comprehensive/usmMonitorDeviceRepair/save`,
    method: 'post',
    data: submitData
  })
}

/**
 * 更新设备维修
 * @param {object} data 设备维修数据
 * @returns {Promise}
 */
export function updateDeviceRepair(data) {
  // 处理时间字段格式
  const submitData = { ...data }
  if (submitData.repairTime) {
    submitData.repairTime = moment(submitData.repairTime).format('YYYY-MM-DD HH:mm:ss')
  }
  
  return request({
    url: `/comprehensive/usmMonitorDeviceRepair/update`,
    method: 'post',
    data: submitData
  })
}

/**
 * 获取设备维修详情
 * @param {string} id 设备维修ID
 * @returns {Promise}
 */
export function getDeviceRepairDetail(id) {
  return request({
    url: `/comprehensive/usmMonitorDeviceRepair/${id}`,
    method: 'get'
  })
}

/**
 * 删除设备维修
 * @param {string} id 设备维修ID
 * @returns {Promise}
 */
export function deleteDeviceRepair(id) {
  return request({
    url: `/comprehensive/usmMonitorDeviceRepair/${id}`,
    method: 'delete'
  })
}

/**
 * 分页查询监测设备列表
 * @param {number} page 页码
 * @param {number} size 每页大小
 * @param {object} params 查询参数
 * @returns {Promise}
 */
export function getMonitorDevicePage(page, size, params = {}) {
  return request({
    url: `/comprehensive/usmMonitorDevice/page/${page}/${size}`,
    method: 'post',
    data: params
  })
}

// 风险评估标识管理相关接口

/**
 * 分页查询风险评估标识列表
 * @param {number} pageNum 页码
 * @param {number} pageSize 每页大小
 * @param {object} params 查询参数
 * @returns {Promise}
 */
export function getRiskRegionSignPage(pageNum, pageSize, params = {}) {
  return request({
    url: `/comprehensive/usmRiskRegionSign/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  })
}

/**
 * 保存风险评估标识
 * @param {object} data 风险评估标识数据
 * @returns {Promise}
 */
export function saveRiskRegionSign(data) {
  // 处理时间字段格式
  const submitData = { ...data }
  if (submitData.createTime) {
    submitData.createTime = moment(submitData.createTime).format('YYYY-MM-DD HH:mm:ss')
  }
  if (submitData.updateTime) {
    submitData.updateTime = moment(submitData.updateTime).format('YYYY-MM-DD HH:mm:ss')
  }
  
  return request({
    url: `/comprehensive/usmRiskRegionSign/save`,
    method: 'post',
    data: submitData
  })
}

/**
 * 更新风险评估标识
 * @param {object} data 风险评估标识数据
 * @returns {Promise}
 */
export function updateRiskRegionSign(data) {
  // 处理时间字段格式
  const submitData = { ...data }
  if (submitData.createTime) {
    submitData.createTime = moment(submitData.createTime).format('YYYY-MM-DD HH:mm:ss')
  }
  if (submitData.updateTime) {
    submitData.updateTime = moment(submitData.updateTime).format('YYYY-MM-DD HH:mm:ss')
  }
  
  return request({
    url: `/comprehensive/usmRiskRegionSign/update`,
    method: 'post',
    data: submitData
  })
}

/**
 * 获取风险评估标识详情
 * @param {string} id 风险评估标识ID
 * @returns {Promise}
 */
export function getRiskRegionSignDetail(id) {
  return request({
    url: `/comprehensive/usmRiskRegionSign/${id}`,
    method: 'get'
  })
}

/**
 * 删除风险评估标识
 * @param {string} id 风险评估标识ID
 * @returns {Promise}
 */
export function deleteRiskRegionSign(id) {
  return request({
    url: `/comprehensive/usmRiskRegionSign/${id}`,
    method: 'delete'
  })
}

/**
 * 获取风险区域名称列表
 * @param {object} params 查询参数
 * @returns {Promise}
 */
export function getRiskRegionDivisionList(params = {}) {
  return request({
    url: `/comprehensive/usmRiskRegionDivision/list`,
    method: 'post',
    data: params
  })
}

// 风险区域划分管理相关接口

/**
 * 分页查询风险区域划分列表
 * @param {number} pageNum 页码
 * @param {number} pageSize 每页大小
 * @param {object} params 查询参数
 * @returns {Promise}
 */
export function getRiskRegionDivisionPage(pageNum, pageSize, params = {}) {
  return request({
    url: `/comprehensive/usmRiskRegionDivision/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  })
}

/**
 * 保存风险区域划分
 * @param {object} data 风险区域划分数据
 * @returns {Promise}
 */
export function saveRiskRegionDivision(data) {
  // 处理时间字段格式
  const submitData = { ...data }
  if (submitData.createTime) {
    submitData.createTime = moment(submitData.createTime).format('YYYY-MM-DD HH:mm:ss')
  }
  if (submitData.updateTime) {
    submitData.updateTime = moment(submitData.updateTime).format('YYYY-MM-DD HH:mm:ss')
  }
  
  return request({
    url: `/comprehensive/usmRiskRegionDivision/save`,
    method: 'post',
    data: submitData
  })
}

/**
 * 更新风险区域划分
 * @param {object} data 风险区域划分数据
 * @returns {Promise}
 */
export function updateRiskRegionDivision(data) {
  // 处理时间字段格式
  const submitData = { ...data }
  if (submitData.createTime) {
    submitData.createTime = moment(submitData.createTime).format('YYYY-MM-DD HH:mm:ss')
  }
  if (submitData.updateTime) {
    submitData.updateTime = moment(submitData.updateTime).format('YYYY-MM-DD HH:mm:ss')
  }
  
  return request({
    url: `/comprehensive/usmRiskRegionDivision/update`,
    method: 'post',
    data: submitData
  })
}

/**
 * 获取风险区域划分详情
 * @param {string} id 风险区域划分ID
 * @returns {Promise}
 */
export function getRiskRegionDivisionDetail(id) {
  return request({
    url: `/comprehensive/usmRiskRegionDivision/${id}`,
    method: 'get'
  })
}

/**
 * 删除风险区域划分
 * @param {string} id 风险区域划分ID
 * @returns {Promise}
 */
export function deleteRiskRegionDivision(id) {
  return request({
    url: `/comprehensive/usmRiskRegionDivision/${id}`,
    method: 'delete'
  })
}