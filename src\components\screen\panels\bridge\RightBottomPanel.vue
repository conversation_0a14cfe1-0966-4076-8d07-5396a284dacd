<template>
  <PanelBox title="安全评分">
    <div class="panel-content">
      <div class="monitoring-list">
        <template v-if="safetyScoreData.length > 0">
          <div class="monitor-item" v-for="(item, index) in safetyScoreData" :key="index">
            <div class="monitor-info">
              <span class="monitor-name">{{ item.name }}</span>
              <div class="chart-area">
                <div class="progress-container">
                  <div class="progress-bar" :style="{ width: `${item.percentage}%` }">
                    <span class="indicator-mark"></span>
                  </div>
                </div>
                <div class="percentage">{{ item.value }}</div>
              </div>
            </div>
          </div>
        </template>
        <template v-else>
          <div class="no-data-wrapper">
            <NoData />
          </div>
        </template>
      </div>
    </div>
  </PanelBox>
</template>

<script setup>
import PanelBox from '@/components/screen/PanelBox.vue'
import { ref, onMounted } from 'vue'
import { getBridgeSafetyScoreStatistics } from '@/api/bridge'
import NoData from '@/components/common/NoData.vue'

// 安全评分数据
const safetyScoreData = ref([])

// 获取安全评分数据
const fetchSafetyScoreData = async () => {
  try {
    const res = await getBridgeSafetyScoreStatistics()
    if (res && res.data && Array.isArray(res.data)) {
      // 将API返回的数据转换为组件需要的格式
      safetyScoreData.value = res.data.map(item => ({
        name: item.bridgeName,
        value: item.totalScore,
        percentage: item.totalScore, // 百分比与分数相同
        bridgeId: item.bridgeId // 保存桥梁ID，以备后用
      }))
    } else {
      console.error('获取安全评分数据格式不正确:', res)
      safetyScoreData.value = []
    }
  } catch (error) {
    console.error('获取安全评分数据失败:', error)
    safetyScoreData.value = []
  }
}

// 组件挂载时获取数据
onMounted(() => {
  fetchSafetyScoreData()
})
</script>

<style scoped>
.panel-content {
  flex: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.monitoring-list {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 10px;
  overflow-y: auto;
} 

.monitor-item {
  display: flex;
  flex-direction: column;
}

.monitor-info {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.monitor-name {
  color: #fff;
  font-size: 12px;
  margin-left: 5px;
  opacity: 0.6;
  margin-bottom: -8px;
}

.chart-area {
  display: flex;
  align-items: center;
  gap: 5px;
}

.progress-container {
  flex: 1;
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: visible;
  position: relative;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #6DBFEC 0%, #129AFF 100%);
  border-radius: 1px;
  position: relative;
}

.indicator-mark {
  position: absolute;
  width: 2px;
  height: 14px;
  background: #FFFFFF;
  right: 0;
  top: -3px;
}

.percentage {
  min-width: 45px;
  text-align: right;
  color: #fff;
  font-size: 16px;
  font-weight: 500;
}

.no-data-wrapper {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 响应式布局适配 */
@media screen and (min-width: 1920px) and (max-width: 2560px) {
  .panel-content {
    padding: 15px;
  }
}

@media screen and (max-width: 1919px) {
  .panel-content {
    padding: 12px;
  }
}

@media screen and (min-width: 2561px) {
  .panel-content {
    padding: 18px;
  }
}

/* 940px左右高度的屏幕特别优化 */
@media (min-height: 910px) and (max-height: 1055px) {
  .panel-content {
    padding: 10px;
    gap: 1px;
  }

  .monitoring-list {
    gap: 0px;
  }

  .monitor-name {
    font-size: 11px;
    margin-bottom: 1px;
  }

  .chart-area {
    gap: 2px;
  }

  .progress-container {
    height: 6px;
  }

  .indicator-mark {
    height: 10px;
    width: 1px;
    top: -2px;
  }

  .percentage {
    min-width: 35px;
    font-size: 12px;
  }
}
</style>