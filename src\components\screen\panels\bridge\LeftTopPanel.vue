<template>
  <PanelBox title="桥梁统计">
    <template #extra>
      <div class="com-select">
        <CommonSelect v-model="statType" :options="typeOptions" @change="handleTypeChange" />
      </div>
    </template>
    <div class="panel-content">
      <!-- 桥梁数量统计区域 -->
      <div class="stats-row">
        <div class="stat-item">
          <div class="stat-info">
            <span class="stat-dot"><span class="stat-dot-inner blue"></span></span>
            <span class="stat-label">桥梁总数</span>
          </div>
          <span class="stat-value blue-gradient">{{ bridgeStats.total }} <span class="unit">座</span></span>
        </div>
        <div class="stat-item">
          <div class="stat-info">
            <span class="stat-dot"><span class="stat-dot-inner cyan"></span></span>
            <span class="stat-label">监测桥梁数</span>
          </div>
          <span class="stat-value cyan-gradient">{{ bridgeStats.monitoring }} <span class="unit">座</span></span>
        </div>
      </div>

      <!-- 3D图表区域 -->
      <Pie3D :data="chartData" height="170px" :internal-diameter-ratio="0.6" totalVisible/>

      <!-- 图例区域 -->
      <!-- <div class="legend-container">
        <div class="legend-item" v-for="(item, index) in currentChartData" :key="index">
          <div class="legend-color" :style="{ backgroundColor: item.color }"></div>
          <div class="legend-text">{{ item.name }} {{ item.value }}座</div>
        </div>
      </div> -->
    </div>
  </PanelBox>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import PanelBox from '@/components/screen/PanelBox.vue'
import CommonSelect from '@/components/screen/CommonSelect.vue'
import Pie3D from '@/components/screen/common/Pie3D.vue'
import { getBridgeStatisticsScreen } from '@/api/bridge'

// 桥梁类型选择
const statType = ref('1')
const typeOptions = [
  { label: '桥梁类型', value: '1' },
  { label: '桥龄', value: '2' },
  { label: '养护等级', value: '3' },
  { label: '技术状况', value: '4' }
]

// 桥梁统计数据
const bridgeStats = ref({
  total: 0,
  monitoring: 0
})

// 统计列表数据
const statisticsList = ref([])

// 颜色映射表
const colorMap = {
  '1': ['#36a4e2', '#32d396', '#5d76f9', '#ffc75a'],
  '2': ['#36a4e2', '#32d396', '#5d76f9', '#ffc75a'],
  '3': ['#32d396', '#36a4e2', '#5d76f9', '#fb3737'],
  '4': ['#32d396', '#36a4e2', '#fb3737', '#ff6d28']
}

// 获取桥梁统计数据
const fetchBridgeStatistics = async (type) => {
  try {
    const res = await getBridgeStatisticsScreen(type)
    if (res && res.code === 200 && res.data) {
      bridgeStats.value = {
        total: res.data.bridgeTotalCount || 0,
        monitoring: res.data.monitorTotalCount || 0
      }
      statisticsList.value = res.data.statisticsList || []
    }
  } catch (error) {
    console.error('获取桥梁统计数据失败:', error)
    // 出错时使用默认数据
    statisticsList.value = []
  }
}

// 当前选择的图表数据
const currentChartData = computed(() => {
  return statisticsList.value.map((item, index) => ({
    name: item.name,
    value: item.count,
    color: colorMap[statType.value][index % colorMap[statType.value].length]
  }))
})

// 为ECharts 3D饼图准备数据
const chartData = computed(() => {
  return currentChartData.value.map(item => ({
    name: item.name,
    value: item.value,
    itemStyle: {
      color: item.color
    }
  }))
})

// 计算总数
const totalValue = computed(() => {
  return currentChartData.value.reduce((acc, item) => acc + item.value, 0)
})

// 处理类型变更
const handleTypeChange = () => {
  console.log('类型变更为:', statType.value)
  fetchBridgeStatistics(statType.value)
}

// 组件挂载时获取数据
onMounted(() => {
  fetchBridgeStatistics(statType.value)
})
</script>

<style scoped>
.panel-content {
  flex: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.com-select {
  margin-right: 20px;
}

/* 统计数据样式 */
.stats-row {
  display: flex;
  justify-content: space-around;
  padding: 0 5px 10px;
  position: relative;
  z-index: 2;
}

.stat-item {
  display: flex;
  /* flex-direction: column; */
  align-items: center;
  position: relative;
  gap: 16px;
}

.stat-info {
  display: flex;
  align-items: center;
  gap: 4px;
}

.stat-dot {
  width: 9px;
  height: 9px;
  border-radius: 50%;
  position: relative;
}

.stat-dot-inner {
  width: 5px;
  height: 5px;
  border-radius: 50%;
  position: absolute;
  top: 2px;
  left: 2px;
}

.stat-dot-inner.blue { 
  background: #055ADB;
}

.stat-dot-inner.cyan { 
  background: #3CF3FF;
}

.stat-dot:has(.stat-dot-inner.blue) {
  background: rgba(5, 90, 219, 0.4);
}

.stat-dot:has(.stat-dot-inner.cyan) {
  background: rgba(60, 243, 255, 0.4);
}

.stat-label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #FFFFFF;
  opacity: 0.8;
}

.stat-value {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 24px;
  line-height: 26px;
  font-style: normal;
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  text-align: left;
}

.blue-gradient {
  background: linear-gradient(90deg, #FFFFFF 0%, #055ADB 100%);
  -webkit-background-clip: text;
}

.cyan-gradient {
  background: linear-gradient(90deg, #FFFFFF 0%, #3CF3FF 100%);
  -webkit-background-clip: text;
}

.unit {
  font-size: 14px;
}

/* 图例样式 */
.legend-container {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  justify-content: center;
  margin-top: 5px;
  position: relative;
  z-index: 2;
  padding-right: 10px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.legend-color {
  width: 10px;
  height: 10px;
  border-radius: 2px;
}

.legend-text {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
}

/* 响应式布局适配 */
@media screen and (min-width: 1920px) and (max-width: 2560px) {
  .panel-content {
    padding: 15px;
  }
}

@media screen and (max-width: 1919px) {
  .panel-content {
    padding: 12px;
  }

  .stat-value {
    font-size: 20px;
    line-height: 22px;
  }
}

@media screen and (min-width: 2561px) {
  .panel-content {
    padding: 18px;
  }

  .stat-value {
    font-size: 28px;
    line-height: 30px;
  }
}

/* 添加全屏模式(1080px高度)的适配 */
@media screen and (min-height: 1056px) and (max-height: 1080px) {
  .panel-content {
    padding: 15px;
  }
  
  .stat-value {
    font-size: 24px;
    line-height: 26px;
  }
}

@media screen and (min-height: 940px) and (max-height: 1055px) {
  .panel-content {
    padding: 10px;
    gap: 10px;
  }

  .stat-value {
    font-size: 20px;
    line-height: 22px;
  }
}

@media screen and (min-height: 900px) and (max-height: 940px) {
  .panel-content {
    padding: 8px;
    gap: 8px;
  }

  .stat-value {
    font-size: 18px;
    line-height: 20px;
  }
}
</style>