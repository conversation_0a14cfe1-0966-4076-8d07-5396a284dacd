<template>
  <PanelBox title="监测设备">
    <div class="panel-content">
      <!-- 顶部统计信息 -->
      <div class="stats-header">
        <div class="stat-item">
          <div class="stat-value">{{ monitoringData.totalDevices }}</div>
          <div class="stat-label">监测设备</div>
        </div>
        <div class="stat-item">
          <div class="stat-value online-rate">{{ monitoringData.onlineRate }}%</div>
          <div class="stat-label">在线率</div>
        </div>
        <!-- <div class="online-indicator">
          <span class="online-dot"></span>
          <span class="online-text">在线</span>
        </div> -->
      </div>

      <!-- 设备类型列表 -->
      <div class="device-list">
        <div 
          v-for="device in monitoringData.deviceTypes" 
          :key="device.id"
          class="device-item"
        >
          <div class="device-info">
            <span class="device-name">{{ device.name }}</span>
            <span class="device-rate">在线率：{{ device.onlineRate }}%</span>
          </div>
          <div class="device-progress">
            <div class="progress-bar">
              <div 
                class="progress-fill" 
                :style="{ width: device.onlineRate + '%' }"
              ></div>
            </div>
            <span class="device-count">{{ device.count }}</span>
          </div>
        </div>
      </div>
    </div>
  </PanelBox>
</template>

<script setup>
import { ref } from 'vue'
import PanelBox from '@/components/screen/PanelBox.vue'

// 定义props
const props = defineProps({
  bridgeId: {
    type: [String, Number],
    required: true
  },
  bridgeName: {
    type: String,
    default: '未知桥梁'
  }
})

// 监测设备数据
const monitoringData = ref({
  totalDevices: 59,
  onlineRate: 93.5,
  deviceTypes: [
    {
      id: 1,
      name: '设备类型1',
      onlineRate: 91.7,
      count: 30
    },
    {
      id: 2,
      name: '设备类型2',
      onlineRate: 91.7,
      count: 80
    },
    {
      id: 3,
      name: '设备类型3',
      onlineRate: 91.7,
      count: 80
    },
    {
      id: 4,
      name: '设备类型4',
      onlineRate: 91.7,
      count: 100
    }
  ]
})
</script>

<style scoped>
.panel-content {
  flex: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

/* 顶部统计信息 */
.stats-header {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  position: relative;
  margin-bottom: 10px;
  gap: 64px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.stat-value {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 24px;
  line-height: 26px;
  color: #3CF3FF;
}

.online-rate {
  color: #3CF3FF;
}

.stat-label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #FFFFFF;
}

.online-indicator {
  position: absolute;
  top: 0;
  right: 0;
  display: flex;
  align-items: center;
  gap: 4px;
}

.online-dot {
  width: 8px;
  height: 8px;
  background: #3FD87C;
  border-radius: 50%;
  box-shadow: 0 0 6px rgba(63, 216, 124, 0.6);
}

.online-text {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #3FD87C;
}

/* 设备列表 */
.device-list {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 10px;
  padding: 0 10px;
}

.device-item {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.device-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.device-name {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 13px;
  color: #FFFFFF;
}

.device-rate {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #3FD87C;
}

.device-progress {
  display: flex;
  align-items: center;
  gap: 10px;
}

.progress-bar {
  flex: 1;
  height: 6px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3FD87C 0%, #66DA43 100%);
  border-radius: 3px;
  transition: width 0.3s ease;
}

.device-count {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 16px;
  color: #FFFFFF;
  min-width: 30px;
  text-align: right;
}

/* 响应式布局适配 */
@media screen and (min-width: 1920px) and (max-width: 2560px) {
  .panel-content {
    padding: 15px;
    gap: 15px;
  }
  
  .stat-value {
    font-size: 24px;
  }
  
  .device-count {
    font-size: 16px;
  }
}

@media screen and (max-width: 1919px) {
  .panel-content {
    padding: 12px;
    gap: 12px;
  }
  
  .stat-value {
    font-size: 22px;
  }
  
  .device-name {
    font-size: 12px;
  }
  
  .device-rate {
    font-size: 11px;
  }
  
  .device-count {
    font-size: 15px;
  }
  
  .device-list {
    gap: 10px;
  }
}

@media screen and (min-width: 2561px) {
  .panel-content {
    padding: 18px;
    gap: 18px;
  }
  
  .stat-value {
    font-size: 26px;
  }
  
  .device-name {
    font-size: 14px;
  }
  
  .device-count {
    font-size: 18px;
  }
}

@media screen and (min-height: 940px) and (max-height: 1055px) {
  .panel-content {
    padding: 10px;
    gap: 10px;
  }
  
  .stat-value {
    font-size: 20px;
    line-height: 22px;
  }
  
  .stat-label {
    font-size: 11px;
  }
  
  .device-name {
    font-size: 11px;
  }
  
  .device-rate {
    font-size: 10px;
  }
  
  .device-count {
    font-size: 14px;
  }
  
  .device-list {
    gap: 5px;
  }
  
  .device-item {
    gap: 4px;
  }
}

@media screen and (min-height: 900px) and (max-height: 940px) {
  .panel-content {
    padding: 8px;
    gap: 8px;
  }
  
  .stats-header {
    margin-bottom: 5px;
  }
  
  .stat-value {
    font-size: 18px;
    line-height: 20px;
  }
  
  .stat-label {
    font-size: 10px;
  }
  
  .device-name {
    font-size: 10px;
  }
  
  .device-rate {
    font-size: 9px;
  }
  
  .device-count {
    font-size: 13px;
  }
  
  .device-list {
    gap: 1px;
  }
  
  .device-item {
    gap: 3px;
  }
  
  .online-dot {
    width: 6px;
    height: 6px;
  }
  
  .online-text {
    font-size: 10px;
  }
}
</style> 