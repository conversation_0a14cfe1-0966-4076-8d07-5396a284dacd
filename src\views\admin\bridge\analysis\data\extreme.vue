<template>
  <div class="bridge-page-container">
    <!-- 筛选区域 -->
    <div class="filter-section">
      <div class="filter-card">
        <el-form :model="filterForm" ref="filterFormRef" inline class="filter-form">
          <el-form-item label="桥梁名称" prop="bridgeId">
            <el-select v-model="filterForm.bridgeId" placeholder="请选择桥梁" style="width: 200px;" clearable
              @change="handleBridgeChange">
              <el-option v-for="bridge in bridgeList" :key="bridge.id" :label="bridge.bridgeName" :value="bridge.id" />
            </el-select>
          </el-form-item>

          <el-form-item label="设备类型" prop="deviceType">
            <el-select v-model="filterForm.deviceType" placeholder="请选择设备类型" style="width: 200px;" clearable
              @change="handleDeviceTypeChange">
              <el-option v-for="device in deviceTypeOptions" :key="device.value" :label="device.label"
                :value="device.value" />
            </el-select>
          </el-form-item>

          <el-form-item label="设备名称" prop="deviceId">
            <el-select v-model="filterForm.deviceId" placeholder="请选择设备" style="width: 200px;" clearable>
              <el-option v-for="device in deviceNameList" :key="device.id" :label="device.deviceName"
                :value="device.id" />
            </el-select>
          </el-form-item>

          <el-form-item label="统计时段" prop="dateRange">
            <el-date-picker v-model="filterForm.dateRange" type="datetimerange" range-separator="至"
              start-placeholder="开始时间" end-placeholder="结束时间" format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss" style="width: 350px;" />
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="handleSearch" :loading="searchLoading">
              <el-icon>
                <Search />
              </el-icon>
              查询
            </el-button>
            <el-button @click="handleReset">
              <el-icon>
                <Refresh />
              </el-icon>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="chart-section" v-loading="loading" element-loading-text="数据加载中...">
      <div class="chart-container">
        <div class="chart-header">
          <h3 class="chart-title">极值分析</h3>
          <div class="chart-legend">
            <div class="legend-item">
              <span class="legend-dot max-value"></span>
              <span class="legend-text">最大值</span>
            </div>
            <div class="legend-item">
              <span class="legend-dot min-value"></span>
              <span class="legend-text">最小值</span>
            </div>
          </div>
        </div>
        <div class="chart-content">
          <div ref="chartRef" class="chart-canvas"></div>
        </div>
      </div>
    </div>

    <!-- 数据概览 -->
    <div class="data-overview" v-if="chartData.length > 0">
      <div class="overview-cards">
        <div class="overview-card" v-for="item in chartData" :key="item.deviceName">
          <div class="card-header">
            <h4 class="device-name">{{ item.deviceName }}</h4>
            <span class="device-unit">{{ item.unitName }}</span>
          </div>
          <div class="card-content">
            <div class="value-item max">
              <span class="value-label">最大值</span>
              <span class="value-number">{{ item.dataValue[0]?.maxvalue || 0 }}</span>
            </div>
            <div class="value-item min">
              <span class="value-label">最小值</span>
              <span class="value-number">{{ item.dataValue[0]?.minvalue || 0 }}</span>
            </div>
            <div class="value-item time">
              <span class="value-label">时间段</span>
              <span class="value-text">{{ item.dataValue[0]?.timePeriodName || '-' }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <!-- <div class="empty-state" v-if="!loading && chartData.length === 0">
      <el-empty description="暂无数据" />
    </div> -->
  </div>
</template>

<script setup>
import { ref, onMounted, reactive, nextTick, onBeforeUnmount } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Refresh } from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import moment from 'moment'
import {
  getBridgeBasicInfoList,
  getPipelineInfoList,
  getDataAssociationAnalysis
} from '@/api/bridge'
import { DEVICE_TYPE_OPTIONS } from '@/constants/bridge'

// 响应式数据
const loading = ref(false)
const searchLoading = ref(false)
const filterFormRef = ref(null)

// 表单数据
const filterForm = reactive({
  bridgeId: '',
  deviceType: '',
  deviceId: '',
  dateRange: []
})

// 下拉选项数据
const bridgeList = ref([])
const deviceNameList = ref([])
const deviceTypeOptions = ref(DEVICE_TYPE_OPTIONS)

// 图表相关
const chartRef = ref(null)
let chartInstance = null
const chartData = ref([])

// 初始化
onMounted(async () => {
  await loadBridgeList()
  await loadDeviceList()
  // 设置默认时间范围为最近7天
  setDefaultDateRange()
})

// 组件卸载时销毁图表
onBeforeUnmount(() => {
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
})

// 设置默认时间范围
const setDefaultDateRange = () => {
  const endTime = moment().format('YYYY-MM-DD HH:mm:ss')
  const startTime = moment().subtract(7, 'days').format('YYYY-MM-DD HH:mm:ss')
  filterForm.dateRange = [startTime, endTime]
}

// 加载桥梁列表
const loadBridgeList = async () => {
  try {
    const response = await getBridgeBasicInfoList()
    if (response.code === 200 && response.data) {
      bridgeList.value = response.data.map(item => ({
        id: item.id,
        bridgeName: item.bridgeName
      }))
    }
  } catch (error) {
    console.error('加载桥梁列表失败:', error)
    ElMessage.error('加载桥梁列表失败')
  }
}

// 加载设备列表
const loadDeviceList = async (bridgeId = '') => {
  try {
    const params = bridgeId ? { bridgeId } : {}
    const response = await getPipelineInfoList(params)
    if (response.code === 200 && response.data) {
      deviceNameList.value = response.data.map(item => ({
        id: item.id,
        deviceName: item.deviceName
      }))
    }
  } catch (error) {
    console.error('加载设备列表失败:', error)
    ElMessage.error('加载设备列表失败')
  }
}

// 桥梁选择变化
const handleBridgeChange = (bridgeId) => {
  filterForm.deviceId = ''
  if (bridgeId) {
    loadDeviceList(bridgeId)
  } else {
    loadDeviceList()
  }
}

// 设备类型变化
const handleDeviceTypeChange = () => {
  filterForm.deviceId = ''
  // 可以根据设备类型过滤设备列表
}

// 查询数据
const handleSearch = async () => {
  if (!filterForm.dateRange || filterForm.dateRange.length !== 2) {
    ElMessage.warning('请选择统计时段')
    return
  }

  searchLoading.value = true
  loading.value = true

  try {
    // 构建请求参数
    const params = {
      bridgeInfos: [{
        bridgeName: getBridgeName(filterForm.bridgeId),
        deviceName: getDeviceName(filterForm.deviceId),
        deviceType: filterForm.deviceType
      }],
      startDate: filterForm.dateRange[0],
      endDate: filterForm.dateRange[1]
    }

    const response = await getDataAssociationAnalysis(params)

    if (response.code === 200) {
      if (!response.data) {
        ElMessage.info('查询结果为空')
        return
      }
      chartData.value = response.data.associationData || []

      // 渲染图表
      await nextTick()
      renderChart()

      if (chartData.value.length === 0) {
        ElMessage.info('查询结果为空')
      }
    } else {
      ElMessage.error(response.message || '查询失败')
    }
  } catch (error) {
    console.error('查询数据失败:', error)
    ElMessage.error('查询数据失败')
  } finally {
    searchLoading.value = false
    loading.value = false
  }
}

// 重置表单
const handleReset = () => {
  filterForm.bridgeId = ''
  filterForm.deviceType = ''
  filterForm.deviceId = ''
  setDefaultDateRange()
  chartData.value = []

  // 清空图表
  if (chartInstance) {
    chartInstance.clear()
  }
}

// 获取桥梁名称
const getBridgeName = (bridgeId) => {
  const bridge = bridgeList.value.find(item => item.id === bridgeId)
  return bridge ? bridge.bridgeName : ''
}

// 获取设备名称
const getDeviceName = (deviceId) => {
  const device = deviceNameList.value.find(item => item.id === deviceId)
  return device ? device.deviceName : ''
}

// 渲染图表
const renderChart = () => {
  if (!chartRef.value || chartData.value.length === 0) return

  // 销毁已存在的图表实例
  if (chartInstance) {
    chartInstance.dispose()
  }

  chartInstance = echarts.init(chartRef.value)

  // 准备图表数据
  const categories = []
  const maxValues = []
  const minValues = []
  const colors = ['#ff4757', '#3742fa', '#2ed573', '#ffa502', '#ff6348']

  chartData.value.forEach((item, index) => {
    if (item.dataValue && item.dataValue.length > 0) {
      const dataValue = item.dataValue[0]
      categories.push(item.deviceName)
      maxValues.push({
        value: dataValue.maxvalue || 0,
        itemStyle: { color: colors[index % colors.length] }
      })
      minValues.push({
        value: dataValue.minvalue || 0,
        itemStyle: { color: colors[index % colors.length] + '80' }
      })
    }
  })

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#999'
        }
      },
      formatter: function (params) {
        let tooltip = `<div style="margin-bottom: 5px;">${params[0].axisValue}</div>`
        params.forEach(param => {
          const unit = chartData.value.find(item => item.deviceName === param.axisValue)?.unitName || ''
          tooltip += `<div>
            <span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${param.color};"></span>
            ${param.seriesName}: ${param.value} ${unit}
          </div>`
        })
        return tooltip
      }
    },
    legend: {
      data: ['最大值', '最小值'],
      top: 10,
      textStyle: {
        color: '#666'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: categories,
      axisPointer: {
        type: 'shadow'
      },
      axisLine: {
        lineStyle: {
          color: '#e6e6e6'
        }
      },
      axisLabel: {
        color: '#666',
        rotate: categories.length > 3 ? 45 : 0
      }
    },
    yAxis: {
      type: 'value',
      name: '数值',
      axisLine: {
        lineStyle: {
          color: '#e6e6e6'
        }
      },
      axisLabel: {
        color: '#666'
      },
      splitLine: {
        lineStyle: {
          color: '#f5f5f5'
        }
      }
    },
    series: [
      {
        name: '最大值',
        type: 'bar',
        data: maxValues,
        barWidth: '30%',
        label: {
          show: true,
          position: 'top',
          color: '#666',
          fontSize: 12
        }
      },
      {
        name: '最小值',
        type: 'bar',
        data: minValues,
        barWidth: '30%',
        label: {
          show: true,
          position: 'top',
          color: '#666',
          fontSize: 12
        }
      }
    ],
    animationEasing: 'elasticOut',
    animationDelayUpdate: function (idx) {
      return idx * 100
    }
  }

  chartInstance.setOption(option)

  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    chartInstance && chartInstance.resize()
  })
}

// 清理事件监听
onBeforeUnmount(() => {
  window.removeEventListener('resize', () => {
    chartInstance && chartInstance.resize()
  })
})
</script>

<style scoped>
.bridge-page-container {
  height: calc(100vh - 200px);
  overflow-y: auto;
}

/* 筛选区域 */
.filter-section {
  margin-bottom: 20px;
}

.filter-card {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.filter-form {
  margin: 0;
}

.filter-form .el-form-item {
  margin-bottom: 0;
  margin-right: 20px;
}

.filter-form .el-form-item:last-child {
  margin-right: 0;
}

/* 图表区域 */
.chart-section {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.chart-container {
  padding: 20px;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #f0f0f0;
}

.chart-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.chart-legend {
  display: flex;
  align-items: center;
  gap: 20px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 6px;
}

.legend-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.legend-dot.max-value {
  background: #ff4757;
}

.legend-dot.min-value {
  background: #3742fa;
}

.legend-text {
  font-size: 14px;
  color: #666;
}

.chart-content {
  height: 400px;
}

.chart-canvas {
  width: 100%;
  height: 100%;
}

/* 数据概览区域 */
.data-overview {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
}

.overview-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.overview-card {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #e9ecef;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.device-name {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.device-unit {
  font-size: 12px;
  color: #909399;
  background: #e7f3ff;
  padding: 2px 8px;
  border-radius: 4px;
}

.card-content {
  display: flex;
  justify-content: space-between;
  gap: 16px;
}

.value-item {
  text-align: center;
  flex: 1;
}

.value-label {
  display: block;
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
}

.value-number {
  display: block;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.value-item.max .value-number {
  color: #ff4757;
}

.value-item.min .value-number {
  color: #3742fa;
}

.value-text {
  display: block;
  font-size: 14px;
  color: #606266;
}

/* 空状态 */
.empty-state {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 40px;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .filter-form {
    display: block;
  }

  .filter-form .el-form-item {
    display: block;
    margin-bottom: 16px;
    margin-right: 0;
  }

  .chart-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .chart-legend {
    align-self: flex-end;
  }
}

@media (max-width: 768px) {
  .bridge-page-container {
    padding: 10px;
  }

  .filter-card {
    padding: 15px;
  }

  .chart-container {
    padding: 15px;
  }

  .chart-content {
    height: 300px;
  }

  .overview-cards {
    grid-template-columns: 1fr;
  }
}
</style>