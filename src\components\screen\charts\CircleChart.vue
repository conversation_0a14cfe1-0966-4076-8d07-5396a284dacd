<template>
  <div class="circle-chart-container">
    <div ref="chartRef" class="chart-content"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
  title: {
    type: String,
    required: true
  },
  data: {
    type: Array,
    required: true
  },
  colorMap: {
    type: Object,
    default: () => ({})
  },
  showPercentage: {
    type: Boolean,
    default: false
  }
})

const chartRef = ref(null)
let chartInstance = null

onMounted(async () => {
  // 使用nextTick确保DOM已更新
  await nextTick()
  if (chartRef.value) {
    console.log('初始化图表:', props.title)
    initChart()
  }
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
  }
  window.removeEventListener('resize', handleResize)
})

watch(() => props.data, () => {
  if (chartInstance) {
    console.log('数据变化，更新图表:', props.title)
    updateChart()
  }
}, { deep: true })

const initChart = () => {
  try {
    // 如果已存在实例，先销毁
    if (chartInstance) {
      chartInstance.dispose()
    }
    
    console.log('创建echarts实例，容器大小:', chartRef.value.offsetWidth, chartRef.value.offsetHeight)
    // 创建新实例
    chartInstance = echarts.init(chartRef.value)
    
    // 添加窗口大小变化监听
    window.addEventListener('resize', handleResize)
    
    // 立即更新图表，不再使用延时
    updateChart()
    // 强制重新计算大小
    chartInstance.resize()
  } catch (error) {
    console.error('初始化图表失败:', error)
  }
}

const handleResize = () => {
  if (chartInstance) {
    try {
      chartInstance.resize()
    } catch (error) {
      console.error('调整图表大小失败:', error)
    }
  }
}

const updateChart = () => {
  if (!chartInstance) {
    console.warn('图表实例不存在，无法更新')
    return
  }
  
  try {
    // 创建本地副本，避免直接使用props
    const localData = JSON.parse(JSON.stringify(props.data))
    const localTitle = String(props.title)
    
    // 计算总和，用于百分比显示
    const total = localData.reduce((sum, item) => sum + item.value, 0)
    
    // 内环UI效果的数据（一个完整的圆）
    const innerRingData = [{
      name: 'innerRing',
      value: 1,
      itemStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            { offset: 0, color: '#00A3FF' },
            { offset: 1, color: '#00FFF0' }
          ]
        },
        borderWidth: 0.66,
        borderType: 'solid',
        borderColor: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            { offset: 0, color: '#00A3FF' },
            { offset: 1, color: '#00FFF0' }
          ]
        }
      },
      label: { show: false },
      labelLine: { show: false }
    }]
    
    // 外环数据颜色，按顺序使用指定颜色
    const colorArray = ['#00A2FF', '#eeeeee', '#16C843', '#00EAFF']
    
    // 创建外环的数据，添加百分比和自定义颜色
    const outerRingData = localData.map((item, index) => {
      const percentage = ((item.value / total) * 100).toFixed(2)
      return {
        name: item.name,
        value: item.value,
        unit: item.unit || '',
        percentage: percentage,
        itemStyle: {
          color: colorArray[index % colorArray.length]
        }
      }
    })
    
    const option = {
      backgroundColor: 'transparent',
      title: {
        text: localTitle,
        left: 'center',
        top: 'center',
        textStyle: {
          color: '#FFFFFF',
          fontSize: 14,
          fontWeight: 'normal',
          fontFamily: 'PingFangSC, PingFang SC'
        }
      },
      grid: {
        top: 0,
        left: 0,
        right: 0,
        bottom: 0
      },
      tooltip: {
        trigger: 'item',
        formatter: function(params) {
          // 不显示内环的tooltip
          if (params.name === 'innerRing') return null
          
          const item = outerRingData.find(d => d.name === params.name)
          if (!item) return null
          
          return `${params.name}: ${params.value}${item.unit} (${item.percentage}%)`
        }
      },
      series: [
        // 内环 - 仅作为UI效果
        {
          name: 'innerRing',
          type: 'pie',
          radius: ['27px', '32px'],  // 调整内环直径32px-38px
          center: ['52%', '50%'],
          silent: true,  // 禁止交互
          clockwise: true,
          avoidLabelOverlap: false,
          animation: false,
          zlevel: 1,  // 确保内环在底层
          itemStyle: {
            borderWidth: 0.66
          },
          label: {
            show: false
          },
          data: innerRingData
        },
        // 外环 - 显示实际数据
        {
          name: localTitle,
          type: 'pie',
          radius: ['36px', '42px'],  // 调整外环直径46px-52px
          center: ['52%', '50%'],
          avoidLabelOverlap: false,
          clockwise: true,
          animation: true,
          zlevel: 2,  // 确保外环在上层
          itemStyle: {
            borderWidth: 0
          },
          emphasis: {
            scale: false,
            scaleSize: 0
          },
          label: {
            show: true,
            position: 'outside',
            distance: 5, // 控制标签距离图形的距离
            formatter: function(params) {
              const item = outerRingData.find(d => d.name === params.name)
              if (!item) return ''
              
              let labelText = `{name|${params.name}}\n{value|${params.value}${item.unit}}`
              
              // 如果需要显示百分比
              if (props.showPercentage) {
                labelText += `\n{percent|${item.percentage}%}`
              }
              
              return labelText
            },
            rich: {
              name: {
                color: '#D3E5FF',
                fontSize: 11,
                fontFamily: 'PingFangSC, PingFang SC',
                lineHeight: 13
              },
              value: {
                color: '#D3E5FF',
                fontSize: 11,
                fontFamily: 'D-DIN, D-DIN',
                fontWeight: 'normal',
                lineHeight: 13
              },
              percent: {
                color: '#FF4D4F',
                fontSize: 12,
                fontFamily: 'D-DIN, D-DIN',
                fontWeight: 'bold',
                padding: [2, 0, 0, 0],
                borderRadius: 10,
                backgroundColor: 'rgba(255, 77, 79, 0.15)'
              }
            },
            padding: [0, 0, 0, 0],
            alignTo: 'labelLine'
          },
          labelLine: {
            show: true,
            length: 5,
            length2: 8,
            maxSurfaceAngle: 80,
            lineStyle: {
              color: '#007AFF',
              opacity: 0.6,
              width: 1
            }
          },
          labelLayout: {
            hideOverlap: true
          },
          data: outerRingData
        }
      ]
    }
    
    console.log('设置图表选项:', props.title)
    chartInstance.setOption(option, true)
  } catch (error) {
    console.error('更新图表失败:', error, props.title)
  }
}
</script>

<style scoped>
.circle-chart-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 140px;
  max-height: 150px;
}

.chart-content {
  width: 100%;
  height: 100%;
  min-height: 140px;
  max-height: 150px;
}
</style>
