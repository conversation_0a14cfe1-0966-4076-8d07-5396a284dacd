// 综合管理
export const comprehensiveLayerTreeList = [
  {
    id: "com_project",
    label: "综合专项",
    special: true,
    children: [
      {
        id: "com_warning_info",
        label: "预警信息",
        children: [
          {
            id: "com_warning_level1",
            label: "一级预警",
            icon: new URL(
                `@/components/GisMap/Tools/components/Legend/images/com_warning_level1.svg`,
                import.meta.url
            ).href,
          },
          {
            id: "com_warning_level2",
            label: "二级预警",
            icon: new URL(
                `@/components/GisMap/Tools/components/Legend/images/com_warning_level2.svg`,
                import.meta.url
            ).href,
          },
          {
            id: "com_warning_level3",
            label: "三级预警",
            icon: new URL(
                `@/components/GisMap/Tools/components/Legend/images/com_warning_level3.svg`,
                import.meta.url
            ).href,
          }
        ],
      },
      {
        id: "com_emergency_event",
        label: "应急事件",
        children: [
          {
            id: "com_gas_event",
            label: "燃气事件",
            icon: new URL(
                `@/components/GisMap/Tools/components/Legend/images/com_gas_event.svg`,
                import.meta.url
            ).href,
          },
          {
            id: "com_drainage_event",
            label: "排水事件",
            icon: new URL(
                `@/components/GisMap/Tools/components/Legend/images/com_drainage_event.svg`,
                import.meta.url
            ).href,
          },
          {
            id: "com_heating_event",
            label: "供热事件",
            icon: new URL(
                `@/components/GisMap/Tools/components/Legend/images/com_heating_event.svg`,
                import.meta.url
            ).href,
          },
          {
            id: "com_bridge_event",
            label: "桥梁事件",
            icon: new URL(
                `@/components/GisMap/Tools/components/Legend/images/com_bridge_event.svg`,
                import.meta.url
            ).href,
          }
        ],
      },
      {
        id: "com_emergency_resource",
        label: "应急资源",
        children: [
          {
            id: "com_shelter",
            label: "避难场所",
            icon: new URL(
                `@/components/GisMap/Tools/components/Legend/images/com_shelter.svg`,
                import.meta.url
            ).href,
          },
          {
            id: "com_emergency_team",
            label: "应急队伍",
            icon: new URL(
                `@/components/GisMap/Tools/components/Legend/images/com_emergency_team.svg`,
                import.meta.url
            ).href,
          },
          {
            id: "com_emergency_material",
            label: "应急物资",
            icon: new URL(
                `@/components/GisMap/Tools/components/Legend/images/com_emergency_material.svg`,
                import.meta.url
            ).href,
          },
          {
            id: "com_rescue_personnel",
            label: "救援人员",
            icon: new URL(
                `@/components/GisMap/Tools/components/Legend/images/com_rescue_personnel.svg`,
                import.meta.url
            ).href,
          },
          {
            id: "com_medical_institution",
            label: "医疗机构",
            icon: new URL(
                `@/components/GisMap/Tools/components/Legend/images/com_medical_institution.svg`,
                import.meta.url
            ).href,
          },
          {
            id: "com_emergency_warehouse",
            label: "应急仓库",
            icon: new URL(
                `@/components/GisMap/Tools/components/Legend/images/com_emergency_warehouse.svg`,
                import.meta.url
            ).href,
          }
        ],
      },
      {
        id: "com_hidden_danger",
        label: "隐患排查",
        children: [
          {
            id: "com_gas_hidden",
            label: "燃气隐患",
            icon: new URL(
                `@/components/GisMap/Tools/components/Legend/images/com_gas_hidden.svg`,
                import.meta.url
            ).href,
          },
          {
            id: "com_drainage_hidden",
            label: "排水隐患",
            icon: new URL(
                `@/components/GisMap/Tools/components/Legend/images/com_drainage_hidden.svg`,
                import.meta.url
            ).href,
          },
          {
            id: "com_heating_hidden",
            label: "供热隐患",
            icon: new URL(
                `@/components/GisMap/Tools/components/Legend/images/com_heating_hidden.svg`,
                import.meta.url
            ).href,
          },
          {
            id: "com_bridge_hidden",
            label: "桥梁隐患",
            icon: new URL(
                `@/components/GisMap/Tools/components/Legend/images/com_bridge_hidden.svg`,
                import.meta.url
            ).href,
          },
        ],
      }
    ]
  },
  {
    id: "gas_project",
    label: "燃气专项",
    special: true,
    children: [
      {
        id: "gas_infrastructure",
        label: "基础设施",
        children: [
          {
            id: "gas_pipeline",
            label: "管线",
            icon: new URL(
                `@/components/GisMap/Tools/components/Legend/images/gas_pipeline.svg`,
                import.meta.url
            ).href,
          },
          {
            id: "gas_station",
            label: "场站",
            icon: new URL(
                `@/components/GisMap/Tools/components/Legend/images/gas_station.svg`,
                import.meta.url
            ).href,
          },
          {
            id: "gas_pipeline_point",
            label: "管点",
            icon: new URL(
                `@/components/GisMap/Tools/components/Legend/images/gas_pipeline_point.svg`,
                import.meta.url
            ).href,
          },
          {
            id: "gas_well",
            label: "窨井",
            icon: new URL(
                `@/components/GisMap/Tools/components/Legend/images/gas_well.svg`,
                import.meta.url
            ).href,
          },
          {
            id: "gas_dangerous_source",
            label: "危险源",
            icon: new URL(
                `@/components/GisMap/Tools/components/Legend/images/gas_dangerous_source.svg`,
                import.meta.url
            ).href,
          },
          {
            id: "gas_protection_target",
            label: "防护目标",
            icon: new URL(
                `@/components/GisMap/Tools/components/Legend/images/gas_protection_target.svg`,
                import.meta.url
            ).href,
          }
        ],
      },
      {
        id: "gas_pipeline_risk",
        label: "管线风险",
        children: [
          {
            id: "gas_pipeline_risk1",
            label: "重大风险",
            icon: "default",
            color: "#FF2E01",
          },
          {
            id: "gas_pipeline_risk2",
            label: "较大风险",
            icon: "default",
            color: "#FF8200",
          },
          {
            id: "gas_pipeline_risk3",
            label: "一般风险",
            icon: "default",
            color: "#FFFE26",
          },
          {
            id: "gas_pipeline_risk4",
            label: "低风险",
            icon: "default",
            color: "#02ADF7",
          }
        ],
      },
      {
        id: "gas_station_risk",
        label: "场站风险",
        children: [
          {
            id: "gas_station_risk1",
            label: "重大风险",
            icon: "default",
            color: "#FF2E01",
          },
          {
            id: "gas_station_risk2",
            label: "较大风险",
            icon: "default",
            color: "#FF8200",
          },
          {
            id: "gas_station_risk3",
            label: "一般风险",
            icon: "default",
            color: "#FFFE26",
          },
          {
            id: "gas_station_risk4",
            label: "低风险",
            icon: "default",
            color: "#02ADF7",
          }
        ],
      },
      {
        id: "gas_monitor_device",
        label: "监测设备",
        children: [
          {
            id: "gas_flowmeter",
            label: "流量监测",
            icon: new URL(
                `@/components/GisMap/Tools/components/Legend/images/gas_flowmeter.svg`,
                import.meta.url
            ).href,
          },
          {
            id: "gas_manometer",
            label: "压力监测",
            icon: new URL(
                `@/components/GisMap/Tools/components/Legend/images/gas_manometer.svg`,
                import.meta.url
            ).href,
          },
          {
            id: "gas_combustible",
            label: "可燃气体监测",
            icon: new URL(
                `@/components/GisMap/Tools/components/Legend/images/gas_combustible.svg`,
                import.meta.url
            ).href,
          },
          {
            id: "gas_temperature",
            label: "温度监测",
            icon: new URL(
                `@/components/GisMap/Tools/components/Legend/images/gas_temperature.svg`,
                import.meta.url
            ).href,
          },
          {
            id: "gas_manhole_cover",
            label: "井盖位移监测",
            icon: new URL(
                `@/components/GisMap/Tools/components/Legend/images/manhole_cover.svg`,
                import.meta.url
            ).href,
          },
          {
            id: "gas_video",
            label: "视频监控",
            icon: new URL(
                `@/components/GisMap/Tools/components/Legend/images/gas_video.svg`,
                import.meta.url
            ).href,
          },
        ],
      },
      {
        id: "gas_monitor_alarm",
        label: "报警信息",
        children: [
          {
            id: "gas_alarm1",
            label: "一级报警",
            icon: "default",
            color: "#FF2E01",
          },
          {
            id: "gas_alarm2",
            label: "二级报警",
            icon: "default",
            color: "#FF8200",
          },
          {
            id: "gas_alarm3",
            label: "三级报警",
            icon: "default",
            color: "#FFFE26",
          }
        ],
      }
    ]
  },
  {
    id: "drainage_project",
    label: "排水专项",
    special: true,
    children: [
      {
        id: "drainage_infrastructure",
        label: "基础设施",
        children: [
          {
            id: "drainage_pipeline",
            label: "管线",
            icon: new URL(
                `@/components/GisMap/Tools/components/Legend/images/drainage_pipeline.svg`,
                import.meta.url
            ).href,
          },
          {
            id: "drainage_pump_station",
            label: "泵站",
            icon: new URL(
                `@/components/GisMap/Tools/components/Legend/images/drainage_pump_station.svg`,
                import.meta.url
            ).href,
          },
          {
            id: "drainage_sewage_works",
            label: "污水厂",
            icon: new URL(
                `@/components/GisMap/Tools/components/Legend/images/drainage_sewage_works.svg`,
                import.meta.url
            ).href,
          },
          {
            id: "drainage_water_outlet",
            label: "排水口",
            icon: new URL(
                `@/components/GisMap/Tools/components/Legend/images/drainage_water_outlet.svg`,
                import.meta.url
            ).href,
          },
          {
            id: "drainage_pipeline_point",
            label: "管点",
            icon: new URL(
                `@/components/GisMap/Tools/components/Legend/images/drainage_pipeline_point.svg`,
                import.meta.url
            ).href,
          },
          {
            id: "drainage_well",
            label: "窨井",
            icon: new URL(
                `@/components/GisMap/Tools/components/Legend/images/drainage_well.svg`,
                import.meta.url
            ).href,
          },
          {
            id: "drainage_flooding_point",
            label: "易涝点",
            icon: new URL(
                `@/components/GisMap/Tools/components/Legend/images/drainage_flooding_point.svg`,
                import.meta.url
            ).href,
          },
          {
            id: "drainage_dangerous_source",
            label: "危险源",
            icon: new URL(
                `@/components/GisMap/Tools/components/Legend/images/drainage_dangerous_source.svg`,
                import.meta.url
            ).href,
          },
          {
            id: "drainage_protection_target",
            label: "防护目标",
            icon: new URL(
                `@/components/GisMap/Tools/components/Legend/images/drainage_protection_target.svg`,
                import.meta.url
            ).href,
          },
        ],
      },
      {
        id: "drainage_pipeline_risk",
        label: "管线风险",
        children: [
          {
            id: "drainage_pipeline_risk1",
            label: "重大风险",
            icon: "default",
            color: "#FF2E01",
          },
          {
            id: "drainage_pipeline_risk2",
            label: "较大风险",
            icon: "default",
            color: "#FF8200",
          },
          {
            id: "drainage_pipeline_risk3",
            label: "一般风险",
            icon: "default",
            color: "#FFFE26",
          },
          {
            id: "drainage_pipeline_risk4",
            label: "低风险",
            icon: "default",
            color: "#02ADF7",
          }
        ],
      },
      {
        id: "drainage_sewage_risk",
        label: "污水厂风险",
        children: [
          {
            id: "drainage_sewage_risk1",
            label: "重大风险",
            icon: "default",
            color: "#FF2E01",
          },
          {
            id: "drainage_sewage_risk2",
            label: "较大风险",
            icon: "default",
            color: "#FF8200",
          },
          {
            id: "drainage_sewage_risk3",
            label: "一般风险",
            icon: "default",
            color: "#FFFE26",
          },
          {
            id: "drainage_sewage_risk4",
            label: "低风险",
            icon: "default",
            color: "#02ADF7",
          }
        ],
      },
      {
        id: "drainage_pump_risk",
        label: "泵站风险",
        children: [
          {
            id: "drainage_pump_risk1",
            label: "重大风险",
            icon: "default",
            color: "#FF2E01",
          },
          {
            id: "drainage_pump_risk2",
            label: "较大风险",
            icon: "default",
            color: "#FF8200",
          },
          {
            id: "drainage_pump_risk3",
            label: "一般风险",
            icon: "default",
            color: "#FFFE26",
          },
          {
            id: "drainage_pump_risk4",
            label: "低风险",
            icon: "default",
            color: "#02ADF7",
          }
        ],
      },
      {
        id: "drainage_hidden_risk",
        label: "隐患排查",
        children: [
          {
            id: "drainage_hidden_risk1",
            label: "重大隐患",
            icon: "default",
            color: "#FF2E01",
          },
          {
            id: "drainage_hidden_risk2",
            label: "较大隐患",
            icon: "default",
            color: "#FF8200",
          },
          {
            id: "drainage_hidden_risk3",
            label: "一般隐患",
            icon: "default",
            color: "#FFFE26",
          },
        ],
      },
      {
        id: "drainage_monitor_device",
        label: "监测设备",
        children: [
          {
            id: "drainage_level",
            label: "液位计",
            icon: new URL(
                `@/components/GisMap/Tools/components/Legend/images/drainage_level.svg`,
                import.meta.url
            ).href,
          },
          {
            id: "drainage_flowmeter",
            label: "流量计",
            icon: new URL(
                `@/components/GisMap/Tools/components/Legend/images/drainage_flowmeter.svg`,
                import.meta.url
            ).href,
          },
          {
            id: "drainage_rain",
            label: "雨量计",
            icon: new URL(
                `@/components/GisMap/Tools/components/Legend/images/drainage_rain.svg`,
                import.meta.url
            ).href,
          },
          {
            id: "drainage_water_quality",
            label: "水质监测仪",
            icon: new URL(
                `@/components/GisMap/Tools/components/Legend/images/drainage_water_quality.svg`,
                import.meta.url
            ).href,
          },
          {
            id: "drainage_combustible",
            label: "可燃气体监测",
            icon: new URL(
                `@/components/GisMap/Tools/components/Legend/images/gas_combustible.svg`,
                import.meta.url
            ).href,
          },
          {
            id: "drainage_manhole_cover",
            label: "井盖位移监测",
            icon: new URL(
                `@/components/GisMap/Tools/components/Legend/images/manhole_cover.svg`,
                import.meta.url
            ).href,
          },
          {
            id: "drainage_video",
            label: "视频监控",
            icon: new URL(
                `@/components/GisMap/Tools/components/Legend/images/drainage_video.svg`,
                import.meta.url
            ).href,
          },
        ],
      },
      {
        id: "drainage_monitor_alarm",
        label: "报警信息",
        children: [
          {
            id: "drainage_alarm1",
            label: "一级报警",
            icon: "default",
            color: "#FF2E01",
          },
          {
            id: "drainage_alarm2",
            label: "二级报警",
            icon: "default",
            color: "#FF8200",
          },
          {
            id: "drainage_alarm3",
            label: "三级报警",
            icon: "default",
            color: "#FFFE26",
          }
        ],
      }
    ]
  },
  {
    id: "heating_project",
    label: "供热专项",
    special: true,
    children: [
      {
        id: "heating_infrastructure",
        label: "基础设施",
        children: [
          {
            id: "heating_pipeline",
            label: "管线",
            icon: new URL(
                `@/components/GisMap/Tools/components/Legend/images/heating_pipeline.svg`,
                import.meta.url
            ).href,
          },
          {
            id: "heating_pipeline_point",
            label: "管点",
            icon: new URL(
                `@/components/GisMap/Tools/components/Legend/images/heating_pipeline_point.svg`,
                import.meta.url
            ).href,
          },
          {
            id: "heating_well",
            label: "窨井",
            icon: new URL(
                `@/components/GisMap/Tools/components/Legend/images/heating_well.svg`,
                import.meta.url
            ).href,
          },
          {
            id: "heating_enterprise",
            label: "供热企业",
            icon: new URL(
                `@/components/GisMap/Tools/components/Legend/images/heating_enterprise.svg`,
                import.meta.url
            ).href,
          },
          {
            id: "heating_source_works",
            label: "热源厂",
            icon: new URL(
                `@/components/GisMap/Tools/components/Legend/images/heating_source_works.svg`,
                import.meta.url
            ).href,
          },
          {
            id: "heating_station",
            label: "换热站",
            icon: new URL(
                `@/components/GisMap/Tools/components/Legend/images/heating_station.svg`,
                import.meta.url
            ).href,
          },
          {
            id: "heating_user",
            label: "供热用户",
            icon: new URL(
                `@/components/GisMap/Tools/components/Legend/images/heating_user.svg`,
                import.meta.url
            ).href,
          },
          {
            id: "heating_dangerous_source",
            label: "危险源",
            icon: new URL(
                `@/components/GisMap/Tools/components/Legend/images/heating_dangerous_source.svg`,
                import.meta.url
            ).href,
          },
          {
            id: "heating_protection_target",
            label: "防护目标",
            icon: new URL(
                `@/components/GisMap/Tools/components/Legend/images/heating_protection_target.svg`,
                import.meta.url
            ).href,
          },
        ],
      },
      {
        id: "heating_pipeline_risk",
        label: "管线风险",
        children: [
          {
            id: "heating_pipeline_risk1",
            label: "重大风险",
            icon: "default",
            color: "#FF2E01",
          },
          {
            id: "heating_pipeline_risk2",
            label: "较大风险",
            icon: "default",
            color: "#FF8200",
          },
          {
            id: "heating_pipeline_risk3",
            label: "一般风险",
            icon: "default",
            color: "#FFFE26",
          },
          {
            id: "heating_pipeline_risk4",
            label: "低风险",
            icon: "default",
            color: "#02ADF7",
          }
        ],
      },
      {
        id: "heating_source_risk",
        label: "热源风险",
        children: [
          {
            id: "heating_source_risk1",
            label: "重大风险",
            icon: "default",
            color: "#FF2E01",
          },
          {
            id: "heating_source_risk2",
            label: "较大风险",
            icon: "default",
            color: "#FF8200",
          },
          {
            id: "heating_source_risk3",
            label: "一般风险",
            icon: "default",
            color: "#FFFE26",
          },
          {
            id: "heating_source_risk4",
            label: "低风险",
            icon: "default",
            color: "#02ADF7",
          }
        ],
      },
      {
        id: "heating_station_risk",
        label: "换热站风险",
        children: [
          {
            id: "heating_station_risk1",
            label: "重大风险",
            icon: "default",
            color: "#FF2E01",
          },
          {
            id: "heating_station_risk2",
            label: "较大风险",
            icon: "default",
            color: "#FF8200",
          },
          {
            id: "heating_station_risk3",
            label: "一般风险",
            icon: "default",
            color: "#FFFE26",
          },
          {
            id: "heating_station_risk4",
            label: "低风险",
            icon: "default",
            color: "#02ADF7",
          }
        ],
      },
      {
        id: "heating_hidden_risk",
        label: "隐患排查",
        children: [
          {
            id: "heating_hidden_risk1",
            label: "重大隐患",
            icon: "default",
            color: "#FF2E01",
          },
          {
            id: "heating_hidden_risk2",
            label: "较大隐患",
            icon: "default",
            color: "#FF8200",
          },
          {
            id: "heating_hidden_risk3",
            label: "一般隐患",
            icon: "default",
            color: "#FFFE26",
          },
        ],
      },
      {
        id: "heating_monitor_device",
        label: "监测设备",
        children: [
          {
            id: "heating_level",
            label: "液位计",
            icon: new URL(
                `@/components/GisMap/Tools/components/Legend/images/drainage_level.svg`,
                import.meta.url
            ).href,
          },
          {
            id: "heating_flowmeter",
            label: "流量计",
            icon: new URL(
                `@/components/GisMap/Tools/components/Legend/images/heating_flowmeter.svg`,
                import.meta.url
            ).href,
          },
          {
            id: "heating_rain",
            label: "雨量计",
            icon: new URL(
                `@/components/GisMap/Tools/components/Legend/images/heating_rain.svg`,
                import.meta.url
            ).href,
          },
          {
            id: "heating_water_quality",
            label: "水质监测仪",
            icon: new URL(
                `@/components/GisMap/Tools/components/Legend/images/heating_water_quality.svg`,
                import.meta.url
            ).href,
          },
          {
            id: "heating_manhole_cover",
            label: "井盖位移监测",
            icon: new URL(
                `@/components/GisMap/Tools/components/Legend/images/manhole_cover.svg`,
                import.meta.url
            ).href,
          },
          {
            id: "heating_video",
            label: "视频监控",
            icon: new URL(
                `@/components/GisMap/Tools/components/Legend/images/heating_video.svg`,
                import.meta.url
            ).href,
          },
        ],
      },
      {
        id: "heating_monitor_alarm",
        label: "报警信息",
        children: [
          {
            id: "heating_alarm1",
            label: "一级报警",
            icon: "default",
            color: "#FF2E01",
          },
          {
            id: "heating_alarm2",
            label: "二级报警",
            icon: "default",
            color: "#FF8200",
          },
          {
            id: "heating_alarm3",
            label: "三级报警",
            icon: "default",
            color: "#FFFE26",
          }
        ],
      }
    ]
  },
  {
    id: "bridge_project",
    label: "桥梁专项",
    special: true,
    children: [
      {
        id: "bridge_infrastructure",
        label: "基础设施",
        children: [
          {
            id: "bridge_info",
            label: "桥梁",
            icon: new URL(
                `@/components/GisMap/Tools/components/Legend/images/bridge_info.svg`,
                import.meta.url
            ).href,
          },
          {
            id: "bridge_safety_rating",
            label: "安全评分",
            icon: new URL(
                `@/components/GisMap/Tools/components/Legend/images/bridge_safety_rating.svg`,
                import.meta.url
            ).href,
          },
        ],
      },
      {
        id: "bridge_monitor_device",
        label: "监测设备",
        children: [
          {
            id: "bridge_wind_speed",
            label: "风速仪",
            icon: new URL(
                `@/components/GisMap/Tools/components/Legend/images/bridge_wind_speed.svg`,
                import.meta.url
            ).href,
          },
          {
            id: "bridge_static_level",
            label: "静力水准仪",
            icon: new URL(
                `@/components/GisMap/Tools/components/Legend/images/bridge_static_level.svg`,
                import.meta.url
            ).href,
          },
          {
            id: "bridge_temperature",
            label: "温度传感器",
            icon: new URL(
                `@/components/GisMap/Tools/components/Legend/images/bridge_temperature.svg`,
                import.meta.url
            ).href,
          },
          {
            id: "bridge_humidity",
            label: "湿度传感器",
            icon: new URL(
                `@/components/GisMap/Tools/components/Legend/images/bridge_humidity.svg`,
                import.meta.url
            ).href,
          },
          {
            id: "bridge_vibration",
            label: "三向加速度传感器",
            icon: new URL(
                `@/components/GisMap/Tools/components/Legend/images/bridge_vibration.svg`,
                import.meta.url
            ).href,
          },
          {
            id: "bridge_dynamic_weight",
            label: "动态称重系统",
            icon: new URL(
                `@/components/GisMap/Tools/components/Legend/images/bridge_dynamic_weight.svg`,
                import.meta.url
            ).href,
          },
          {
            id: "bridge_displacement",
            label: "位移计",
            icon: new URL(
                `@/components/GisMap/Tools/components/Legend/images/bridge_displacement.svg`,
                import.meta.url
            ).href,
          },
          {
            id: "bridge_tilt",
            label: "倾角传感器",
            icon: new URL(
                `@/components/GisMap/Tools/components/Legend/images/bridge_tilt.svg`,
                import.meta.url
            ).href,
          },
          {
            id: "bridge_deflection",
            label: "挠度仪",
            icon: new URL(
                `@/components/GisMap/Tools/components/Legend/images/bridge_deflection.svg`,
                import.meta.url
            ).href,
          },
          {
            id: "bridge_strain",
            label: "应变传感器",
            icon: new URL(
                `@/components/GisMap/Tools/components/Legend/images/bridge_strain.svg`,
                import.meta.url
            ).href,
          },
          {
            id: "bridge_load_cell",
            label: "测力支座",
            icon: new URL(
                `@/components/GisMap/Tools/components/Legend/images/bridge_load_cell.svg`,
                import.meta.url
            ).href,
          },
          {
            id: "bridge_acceleration",
            label: "加速度传感器",
            icon: new URL(
                `@/components/GisMap/Tools/components/Legend/images/bridge_acceleration.svg`,
                import.meta.url
            ).href,
          },
          {
            id: "bridge_crack",
            label: "车船撞击传感器",
            icon: new URL(
                `@/components/GisMap/Tools/components/Legend/images/bridge_crack.svg`,
                import.meta.url
            ).href,
          },
          {
            id: "bridge_tilt_alarm",
            label: "梁体偏位报警器",
            icon: new URL(
                `@/components/GisMap/Tools/components/Legend/images/bridge_tilt_alarm.svg`,
                import.meta.url
            ).href,
          },
          {
            id: "bridge_crack_sensor",
            label: "裂缝传感器",
            icon: new URL(
                `@/components/GisMap/Tools/components/Legend/images/bridge_crack_sensor.svg`,
                import.meta.url
            ).href,
          },
          {
            id: "bridge_vibration_sensor",
            label: "测振仪",
            icon: new URL(
                `@/components/GisMap/Tools/components/Legend/images/bridge_vibration_sensor.svg`,
                import.meta.url
            ).href,
          },
          {
            id: "bridge_video",
            label: "视频监控",
            icon: new URL(
                `@/components/GisMap/Tools/components/Legend/images/bridge_video.svg`,
                import.meta.url
            ).href,
          },
        ],
      },
      /* {
         id: "bridge_diseases",
         label: "病害",
         children: [
           {
             id: "bridge_diseases1",
             label: "病害等级一",
             icon: "",
           },
           {
             id: "bridge_diseases2",
             label: "病害等级二",
             icon: "",
           },
           {
             id: "bridge_diseases3",
             label: "病害等级三",
             icon: "",
           },
         ],
       },*/
    ]
  }
];

//燃气专项
export const gasLayerTreeList = [
  {
    id: "gas_infrastructure",
    label: "基础设施",
    children: [
      {
        id: "gas_pipeline",
        label: "管线",
        icon: new URL(
            `@/components/GisMap/Tools/components/Legend/images/gas_pipeline.svg`,
            import.meta.url
        ).href,
      },
      {
        id: "gas_station",
        label: "场站",
        icon: new URL(
            `@/components/GisMap/Tools/components/Legend/images/gas_station.svg`,
            import.meta.url
        ).href,
      },
      {
        id: "gas_pipeline_point",
        label: "管点",
        icon: new URL(
            `@/components/GisMap/Tools/components/Legend/images/gas_pipeline_point.svg`,
            import.meta.url
        ).href,
      },
      {
        id: "gas_well",
        label: "窨井",
        icon: new URL(
            `@/components/GisMap/Tools/components/Legend/images/gas_well.svg`,
            import.meta.url
        ).href,
      },
      {
        id: "gas_dangerous_source",
        label: "危险源",
        icon: new URL(
            `@/components/GisMap/Tools/components/Legend/images/gas_dangerous_source.svg`,
            import.meta.url
        ).href,
      },
      {
        id: "gas_protection_target",
        label: "防护目标",
        icon: new URL(
            `@/components/GisMap/Tools/components/Legend/images/gas_protection_target.svg`,
            import.meta.url
        ).href,
      }
    ],
  },
  {
    id: "gas_pipeline_risk",
    label: "管线风险",
    children: [
      {
        id: "gas_pipeline_risk1",
        label: "重大风险",
        icon: "default",
        color: "#ff2e01",
      },
      {
        id: "gas_pipeline_risk2",
        label: "较大风险",
        icon: "default",
        color: "#ff8200",
      },
      {
        id: "gas_pipeline_risk3",
        label: "一般风险",
        icon: "default",
        color: "#fffe26",
      },
      {
        id: "gas_pipeline_risk4",
        label: "低风险",
        icon: "default",
        color: "#02adf7",
      }
    ],
  },
  {
    id: "gas_station_risk",
    label: "场站风险",
    children: [
      {
        id: "gas_station_risk1",
        label: "重大风险",
        icon: "default",
        color: "#FF2E01",
      },
      {
        id: "gas_station_risk2",
        label: "较大风险",
        icon: "default",
        color: "#FF8200",
      },
      {
        id: "gas_station_risk3",
        label: "一般风险",
        icon: "default",
        color: "#FFFE26",
      },
      {
        id: "gas_station_risk4",
        label: "低风险",
        icon: "default",
        color: "#02ADF7",
      }
    ],
  },
  {
    id: "gas_monitor_device",
    label: "监测设备",
    children: [
      {
        id: "gas_flowmeter",
        label: "流量监测",
        icon: new URL(
            `@/components/GisMap/Tools/components/Legend/images/gas_flowmeter.svg`,
            import.meta.url
        ).href,
      },
      {
        id: "gas_manometer",
        label: "压力监测",
        icon: new URL(
            `@/components/GisMap/Tools/components/Legend/images/gas_manometer.svg`,
            import.meta.url
        ).href,
      },
      {
        id: "gas_combustible",
        label: "可燃气体监测",
        icon: new URL(
            `@/components/GisMap/Tools/components/Legend/images/gas_combustible.svg`,
            import.meta.url
        ).href,
      },
      {
        id: "gas_temperature",
        label: "温度监测",
        icon: new URL(
            `@/components/GisMap/Tools/components/Legend/images/gas_temperature.svg`,
            import.meta.url
        ).href,
      },
      {
        id: "gas_manhole_cover",
        label: "井盖位移监测",
        icon: new URL(
            `@/components/GisMap/Tools/components/Legend/images/manhole_cover.svg`,
            import.meta.url
        ).href,
      },
      {
        id: "gas_video",
        label: "视频监控",
        icon: new URL(
            `@/components/GisMap/Tools/components/Legend/images/gas_video.svg`,
            import.meta.url
        ).href,
      },
    ],
  },
  {
    id: "gas_monitor_alarm",
    label: "报警信息",
    children: [
      {
        id: "gas_alarm1",
        label: "一级报警",
        icon: "default",
        color: "#FF2E01",
      },
      {
        id: "gas_alarm2",
        label: "二级报警",
        icon: "default",
        color: "#FF8200",
      },
      {
        id: "gas_alarm3",
        label: "三级报警",
        icon: "default",
        color: "#FFFE26",
      }
    ],
  }
];

//排水专项
export const drainageLayerTreeList = [
  {
    id: "drainage_infrastructure",
    label: "基础设施",
    children: [
      {
        id: "drainage_pipeline",
        label: "管线",
        icon: new URL(
            `@/components/GisMap/Tools/components/Legend/images/drainage_pipeline.svg`,
            import.meta.url
        ).href,
      },
      {
        id: "drainage_pump_station",
        label: "泵站",
        icon: new URL(
            `@/components/GisMap/Tools/components/Legend/images/drainage_pump_station.svg`,
            import.meta.url
        ).href,
      },
      {
        id: "drainage_sewage_works",
        label: "污水厂",
        icon: new URL(
            `@/components/GisMap/Tools/components/Legend/images/drainage_sewage_works.svg`,
            import.meta.url
        ).href,
      },
      {
        id: "drainage_water_outlet",
        label: "排水口",
        icon: new URL(
            `@/components/GisMap/Tools/components/Legend/images/drainage_water_outlet.svg`,
            import.meta.url
        ).href,
      },
      {
        id: "drainage_pipeline_point",
        label: "管点",
        icon: new URL(
            `@/components/GisMap/Tools/components/Legend/images/drainage_pipeline_point.svg`,
            import.meta.url
        ).href,
      },
      {
        id: "drainage_well",
        label: "窨井",
        icon: new URL(
            `@/components/GisMap/Tools/components/Legend/images/drainage_well.svg`,
            import.meta.url
        ).href,
      },
      {
        id: "drainage_flooding_point",
        label: "易涝点",
        icon: new URL(
            `@/components/GisMap/Tools/components/Legend/images/drainage_flooding_point.svg`,
            import.meta.url
        ).href,
      },
      {
        id: "drainage_dangerous_source",
        label: "危险源",
        icon: new URL(
            `@/components/GisMap/Tools/components/Legend/images/drainage_dangerous_source.svg`,
            import.meta.url
        ).href,
      },
      {
        id: "drainage_protection_target",
        label: "防护目标",
        icon: new URL(
            `@/components/GisMap/Tools/components/Legend/images/drainage_protection_target.svg`,
            import.meta.url
        ).href,
      },
    ],
  },
  {
    id: "drainage_pipeline_risk",
    label: "管线风险",
    children: [
      {
        id: "drainage_pipeline_risk1",
        label: "重大风险",
        icon: "default",
        color: "#FF2E01",
      },
      {
        id: "drainage_pipeline_risk2",
        label: "较大风险",
        icon: "default",
        color: "#FF8200",
      },
      {
        id: "drainage_pipeline_risk3",
        label: "一般风险",
        icon: "default",
        color: "#FFFE26",
      },
      {
        id: "drainage_pipeline_risk4",
        label: "低风险",
        icon: "default",
        color: "#02ADF7",
      }
    ],
  },
  {
    id: "drainage_sewage_risk",
    label: "污水厂风险",
    children: [
      {
        id: "drainage_sewage_risk1",
        label: "重大风险",
        icon: "default",
        color: "#FF2E01",
      },
      {
        id: "drainage_sewage_risk2",
        label: "较大风险",
        icon: "default",
        color: "#FF8200",
      },
      {
        id: "drainage_sewage_risk3",
        label: "一般风险",
        icon: "default",
        color: "#FFFE26",
      },
      {
        id: "drainage_sewage_risk4",
        label: "低风险",
        icon: "default",
        color: "#02ADF7",
      }
    ],
  },
  {
    id: "drainage_pump_risk",
    label: "泵站风险",
    children: [
      {
        id: "drainage_pump_risk1",
        label: "重大风险",
        icon: "default",
        color: "#FF2E01",
      },
      {
        id: "drainage_pump_risk2",
        label: "较大风险",
        icon: "default",
        color: "#FF8200",
      },
      {
        id: "drainage_pump_risk3",
        label: "一般风险",
        icon: "default",
        color: "#FFFE26",
      },
      {
        id: "drainage_pump_risk4",
        label: "低风险",
        icon: "default",
        color: "#02ADF7",
      }
    ],
  },
  {
    id: "drainage_hidden_risk",
    label: "隐患排查",
    children: [
      {
        id: "drainage_hidden_risk1",
        label: "重大隐患",
        icon: "default",
        color: "#FF2E01",
      },
      {
        id: "drainage_hidden_risk2",
        label: "较大隐患",
        icon: "default",
        color: "#FF8200",
      },
      {
        id: "drainage_hidden_risk3",
        label: "一般隐患",
        icon: "default",
        color: "#FFFE26",
      },
    ],
  },
  {
    id: "drainage_monitor_device",
    label: "监测设备",
    children: [
      {
        id: "drainage_level",
        label: "液位计",
        icon: new URL(
            `@/components/GisMap/Tools/components/Legend/images/drainage_level.svg`,
            import.meta.url
        ).href,
      },
      {
        id: "drainage_flowmeter",
        label: "流量计",
        icon: new URL(
            `@/components/GisMap/Tools/components/Legend/images/drainage_flowmeter.svg`,
            import.meta.url
        ).href,
      },
      {
        id: "drainage_rain",
        label: "雨量计",
        icon: new URL(
            `@/components/GisMap/Tools/components/Legend/images/drainage_rain.svg`,
            import.meta.url
        ).href,
      },
      {
        id: "drainage_water_quality",
        label: "水质监测仪",
        icon: new URL(
            `@/components/GisMap/Tools/components/Legend/images/drainage_water_quality.svg`,
            import.meta.url
        ).href,
      },
      {
        id: "drainage_combustible",
        label: "可燃气体监测",
        icon: new URL(
            `@/components/GisMap/Tools/components/Legend/images/gas_combustible.svg`,
            import.meta.url
        ).href,
      },
      {
        id: "drainage_manhole_cover",
        label: "井盖位移监测",
        icon: new URL(
            `@/components/GisMap/Tools/components/Legend/images/manhole_cover.svg`,
            import.meta.url
        ).href,
      },
      {
        id: "drainage_video",
        label: "视频监控",
        icon: new URL(
            `@/components/GisMap/Tools/components/Legend/images/drainage_video.svg`,
            import.meta.url
        ).href,
      },
    ],
  },
  {
    id: "drainage_monitor_alarm",
    label: "报警信息",
    children: [
      {
        id: "drainage_alarm1",
        label: "一级报警",
        icon: "default",
        color: "#FF2E01",
      },
      {
        id: "drainage_alarm2",
        label: "二级报警",
        icon: "default",
        color: "#FF8200",
      },
      {
        id: "drainage_alarm3",
        label: "三级报警",
        icon: "default",
        color: "#FFFE26",
      }
    ],
  }
];

//供热专项
export const heatingLayerTreeList = [
  {
    id: "heating_infrastructure",
    label: "基础设施",
    children: [
      {
        id: "heating_pipeline",
        label: "管线",
        icon: new URL(
            `@/components/GisMap/Tools/components/Legend/images/heating_pipeline.svg`,
            import.meta.url
        ).href,
      },
      {
        id: "heating_pipeline_point",
        label: "管点",
        icon: new URL(
            `@/components/GisMap/Tools/components/Legend/images/heating_pipeline_point.svg`,
            import.meta.url
        ).href,
      },
      {
        id: "heating_well",
        label: "窨井",
        icon: new URL(
            `@/components/GisMap/Tools/components/Legend/images/heating_well.svg`,
            import.meta.url
        ).href,
      },
      {
        id: "heating_enterprise",
        label: "供热企业",
        icon: new URL(
            `@/components/GisMap/Tools/components/Legend/images/heating_enterprise.svg`,
            import.meta.url
        ).href,
      },
      {
        id: "heating_source_works",
        label: "热源厂",
        icon: new URL(
            `@/components/GisMap/Tools/components/Legend/images/heating_source_works.svg`,
            import.meta.url
        ).href,
      },
      {
        id: "heating_station",
        label: "换热站",
        icon: new URL(
            `@/components/GisMap/Tools/components/Legend/images/heating_station.svg`,
            import.meta.url
        ).href,
      },
      {
        id: "heating_user",
        label: "供热用户",
        icon: new URL(
            `@/components/GisMap/Tools/components/Legend/images/heating_user.svg`,
            import.meta.url
        ).href,
      },
      {
        id: "heating_dangerous_source",
        label: "危险源",
        icon: new URL(
            `@/components/GisMap/Tools/components/Legend/images/heating_dangerous_source.svg`,
            import.meta.url
        ).href,
      },
      {
        id: "heating_protection_target",
        label: "防护目标",
        icon: new URL(
            `@/components/GisMap/Tools/components/Legend/images/heating_protection_target.svg`,
            import.meta.url
        ).href,
      },
    ],
  },
  {
    id: "heating_pipeline_risk",
    label: "管线风险",
    children: [
      {
        id: "heating_pipeline_risk1",
        label: "重大风险",
        icon: "default",
        color: "#FF2E01",
      },
      {
        id: "heating_pipeline_risk2",
        label: "较大风险",
        icon: "default",
        color: "#FF8200",
      },
      {
        id: "heating_pipeline_risk3",
        label: "一般风险",
        icon: "default",
        color: "#FFFE26",
      },
      {
        id: "heating_pipeline_risk4",
        label: "低风险",
        icon: "default",
        color: "#02ADF7",
      }
    ],
  },
  {
    id: "heating_source_risk",
    label: "热源风险",
    children: [
      {
        id: "heating_source_risk1",
        label: "重大风险",
        icon: "default",
        color: "#FF2E01",
      },
      {
        id: "heating_source_risk2",
        label: "较大风险",
        icon: "default",
        color: "#FF8200",
      },
      {
        id: "heating_source_risk3",
        label: "一般风险",
        icon: "default",
        color: "#FFFE26",
      },
      {
        id: "heating_source_risk4",
        label: "低风险",
        icon: "default",
        color: "#02ADF7",
      }
    ],
  },
  {
    id: "heating_station_risk",
    label: "换热站风险",
    children: [
      {
        id: "heating_station_risk1",
        label: "重大风险",
        icon: "default",
        color: "#FF2E01",
      },
      {
        id: "heating_station_risk2",
        label: "较大风险",
        icon: "default",
        color: "#FF8200",
      },
      {
        id: "heating_station_risk3",
        label: "一般风险",
        icon: "default",
        color: "#FFFE26",
      },
      {
        id: "heating_station_risk4",
        label: "低风险",
        icon: "default",
        color: "#02ADF7",
      }
    ],
  },
  {
    id: "heating_hidden_risk",
    label: "隐患排查",
    children: [
      {
        id: "heating_hidden_risk1",
        label: "重大隐患",
        icon: "default",
        color: "#FF2E01",
      },
      {
        id: "heating_hidden_risk2",
        label: "较大隐患",
        icon: "default",
        color: "#FF8200",
      },
      {
        id: "heating_hidden_risk3",
        label: "一般隐患",
        icon: "default",
        color: "#FFFE26",
      },
    ],
  },
  {
    id: "heating_monitor_device",
    label: "监测设备",
    children: [
      {
        id: "heating_level",
        label: "液位计",
        icon: new URL(
            `@/components/GisMap/Tools/components/Legend/images/heating_level.svg`,
            import.meta.url
        ).href,
      },
      {
        id: "heating_flowmeter",
        label: "流量计",
        icon: new URL(
            `@/components/GisMap/Tools/components/Legend/images/heating_flowmeter.svg`,
            import.meta.url
        ).href,
      },
      {
        id: "heating_rain",
        label: "雨量计",
        icon: new URL(
            `@/components/GisMap/Tools/components/Legend/images/heating_rain.svg`,
            import.meta.url
        ).href,
      },
      {
        id: "heating_water_quality",
        label: "水质监测仪",
        icon: new URL(
            `@/components/GisMap/Tools/components/Legend/images/heating_water_quality.svg`,
            import.meta.url
        ).href,
      },
      {
        id: "heating_combustible",
        label: "可燃气体监测",
        icon: new URL(
            `@/components/GisMap/Tools/components/Legend/images/gas_combustible.svg`,
            import.meta.url
        ).href,
      },
      {
        id: "heating_temperature",
        label: "温度监测",
        icon: new URL(
            `@/components/GisMap/Tools/components/Legend/images/gas_temperature.svg`,
            import.meta.url
        ).href,
      },
      {
        id: "heating_manhole_cover",
        label: "井盖位移监测",
        icon: new URL(
            `@/components/GisMap/Tools/components/Legend/images/manhole_cover.svg`,
            import.meta.url
        ).href,
      },
      {
        id: "heating_video",
        label: "视频监控",
        icon: new URL(
            `@/components/GisMap/Tools/components/Legend/images/heating_video.svg`,
            import.meta.url
        ).href,
      },
    ],
  },
  {
    id: "heating_monitor_alarm",
    label: "报警信息",
    children: [
      {
        id: "heating_alarm1",
        label: "一级报警",
        icon: "default",
        color: "#FF2E01",
      },
      {
        id: "heating_alarm2",
        label: "二级报警",
        icon: "default",
        color: "#FF8200",
      },
      {
        id: "heating_alarm3",
        label: "三级报警",
        icon: "default",
        color: "#FFFE26",
      }
    ],
  }
];

//桥梁专项
export const bridgeLayerTreeList = [
  {
    id: "bridge_infrastructure",
    label: "基础设施",
    children: [
      {
        id: "bridge_info",
        label: "桥梁",
        icon: new URL(
            `@/components/GisMap/Tools/components/Legend/images/bridge_info.svg`,
            import.meta.url
        ).href,
      },
      {
        id: "bridge_safety_rating",
        label: "安全评分",
        icon: new URL(
            `@/components/GisMap/Tools/components/Legend/images/bridge_safety_rating.svg`,
            import.meta.url
        ).href,
      },
    ],
  },
  {
    id: "bridge_monitor_device",
    label: "监测设备",
    children: [
      {
        id: "bridge_wind_speed",
        label: "风速仪",
        icon: new URL(
            `@/components/GisMap/Tools/components/Legend/images/bridge_wind_speed.svg`,
            import.meta.url
        ).href,
      },
      {
        id: "bridge_static_level",
        label: "静力水准仪",
        icon: new URL(
            `@/components/GisMap/Tools/components/Legend/images/bridge_static_level.svg`,
            import.meta.url
        ).href,
      },
      {
        id: "bridge_temperature",
        label: "温度传感器",
        icon: new URL(
            `@/components/GisMap/Tools/components/Legend/images/bridge_temperature.svg`,
            import.meta.url
        ).href,
      },
      {
        id: "bridge_humidity",
        label: "湿度传感器",
        icon: new URL(
            `@/components/GisMap/Tools/components/Legend/images/bridge_humidity.svg`,
            import.meta.url
        ).href,
      },
      {
        id: "bridge_vibration",
        label: "三向加速度传感器",
        icon: new URL(
            `@/components/GisMap/Tools/components/Legend/images/bridge_vibration.svg`,
            import.meta.url
        ).href,
      },
      {
        id: "bridge_dynamic_weight",
        label: "动态称重系统",
        icon: new URL(
            `@/components/GisMap/Tools/components/Legend/images/bridge_dynamic_weight.svg`,
            import.meta.url
        ).href,
      },
      {
        id: "bridge_displacement",
        label: "位移计",
        icon: new URL(
            `@/components/GisMap/Tools/components/Legend/images/bridge_displacement.svg`,
            import.meta.url
        ).href,
      },
      {
        id: "bridge_tilt",
        label: "倾角传感器",
        icon: new URL(
            `@/components/GisMap/Tools/components/Legend/images/bridge_tilt.svg`,
            import.meta.url
        ).href,
      },
      {
        id: "bridge_deflection",
        label: "挠度仪",
        icon: new URL(
            `@/components/GisMap/Tools/components/Legend/images/bridge_deflection.svg`,
            import.meta.url
        ).href,
      },
      {
        id: "bridge_strain",
        label: "应变传感器",
        icon: new URL(
            `@/components/GisMap/Tools/components/Legend/images/bridge_strain.svg`,
            import.meta.url
        ).href,
      },
      {
        id: "bridge_load_cell",
        label: "测力支座",
        icon: new URL(
            `@/components/GisMap/Tools/components/Legend/images/bridge_load_cell.svg`,
            import.meta.url
        ).href,
      },
      {
        id: "bridge_acceleration",
        label: "加速度传感器",
        icon: new URL(
            `@/components/GisMap/Tools/components/Legend/images/bridge_acceleration.svg`,
            import.meta.url
        ).href,
      },
      {
        id: "bridge_crack",
        label: "车船撞击传感器",
        icon: new URL(
            `@/components/GisMap/Tools/components/Legend/images/bridge_crack.svg`,
            import.meta.url
        ).href,
      },
      {
        id: "bridge_tilt_alarm",
        label: "梁体偏位报警器",
        icon: new URL(
            `@/components/GisMap/Tools/components/Legend/images/bridge_tilt_alarm.svg`,
            import.meta.url
        ).href,
      },
      {
        id: "bridge_crack_sensor",
        label: "裂缝传感器",
        icon: new URL(
            `@/components/GisMap/Tools/components/Legend/images/bridge_crack_sensor.svg`,
            import.meta.url
        ).href,
      },
      {
        id: "bridge_vibration_sensor",
        label: "测振仪",
        icon: new URL(
            `@/components/GisMap/Tools/components/Legend/images/bridge_vibration_sensor.svg`,
            import.meta.url
        ).href,
      },
      {
        id: "bridge_video",
        label: "视频监控",
        icon: new URL(
            `@/components/GisMap/Tools/components/Legend/images/bridge_video.svg`,
            import.meta.url
        ).href,
      },
    ],
  },
  {
    id: "bridge_monitor_alarm",
    label: "报警信息",
    children: [
      {
        id: "bridge_alarm1",
        label: "一级报警",
        icon: "default",
        color: "#FF2E01",
      },
      {
        id: "bridge_alarm2",
        label: "二级报警",
        icon: "default",
        color: "#FF8200",
      },
      {
        id: "bridge_alarm3",
        label: "三级报警",
        icon: "default",
        color: "#FFFE26",
      }
    ],
  }
 /* {
    id: "bridge_diseases",
    label: "病害",
    children: [
      {
        id: "bridge_diseases1",
        label: "病害等级一",
        icon: "",
      },
      {
        id: "bridge_diseases2",
        label: "病害等级二",
        icon: "",
      },
      {
        id: "bridge_diseases3",
        label: "病害等级三",
        icon: "",
      },
    ],
  },*/
];

export const legendListMap= {
  "/comprehensive/overview": comprehensiveLayerTreeList, // 综合管理-总览
  "/comprehensive/risk": comprehensiveLayerTreeList, // 综合管理-风险隐患
  "/comprehensive/monitoring": comprehensiveLayerTreeList, // 综合管理-运行监测
  "/comprehensive/coordination": comprehensiveLayerTreeList, // 综合管理-协同指挥
  "/comprehensive/emergency": comprehensiveLayerTreeList, // 综合管理-应急辅助决策
  "/gas/overview": gasLayerTreeList, // 燃气管理-总览
  "/gas/network-risk": gasLayerTreeList, // 燃气管理-管线风险
  "/gas/monitoring": gasLayerTreeList, // 燃气管理-运行监测
  "/gas/decision-screen": gasLayerTreeList, // 燃气管理-辅助决策
  "/drainage/overview": drainageLayerTreeList, // 排水管理-总览
  "/drainage/risk": drainageLayerTreeList, // 排水管理-风险隐患
  "/drainage/flooding-risk": drainageLayerTreeList, // 排水管理-易涝点风险
  "/drainage/monitoring": drainageLayerTreeList, // 排水管理-运行监测
  "/drainage/decision": drainageLayerTreeList, // 排水管理-辅助决策
  "/heating/overview": heatingLayerTreeList, // 供热管理-总览
  "/heating/risk": heatingLayerTreeList, // 供热管理-风险隐患
  "/heating/monitoring": heatingLayerTreeList, // 供热管理-运行监测
  "/heating/decision": heatingLayerTreeList, // 供热管理-辅助决策
  "/bridge": bridgeLayerTreeList, // 桥梁管理-总览
  "/bridge/overview": bridgeLayerTreeList, // 桥梁管理-总览
  "/bridge/risk": bridgeLayerTreeList, // 桥梁管理-风险隐患
  "/bridge/monitoring": bridgeLayerTreeList, // 桥梁管理-运行监测
  "/bridge/decision": bridgeLayerTreeList, // 桥梁管理-辅助决策
};
