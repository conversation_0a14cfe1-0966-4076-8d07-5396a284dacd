<template>
  <div class="bridge-page-container">
    <!-- 筛选区域 -->
    <div class="filter-section">
      <div class="filter-card">
        <el-form :model="filterForm" ref="filterFormRef" inline class="filter-form">
          <el-form-item label="桥梁名称" prop="bridgeId">
            <el-select v-model="filterForm.bridgeId" placeholder="请选择桥梁" style="width: 200px;" clearable
              @change="handleBridgeChange">
              <el-option v-for="bridge in bridgeList" :key="bridge.id" :label="bridge.bridgeName" :value="bridge.id" />
            </el-select>
          </el-form-item>

          <el-form-item label="设备类型" prop="deviceType">
            <el-select v-model="filterForm.deviceType" placeholder="请选择设备类型" style="width: 200px;" clearable
              @change="handleDeviceTypeChange">
              <el-option v-for="device in deviceTypeOptions" :key="device.value" :label="device.label"
                :value="device.value" />
            </el-select>
          </el-form-item>

          <el-form-item label="设备名称" prop="deviceId">
            <el-select v-model="filterForm.deviceId" placeholder="请选择设备" style="width: 200px;" clearable>
              <el-option v-for="device in deviceNameList" :key="device.id" :label="device.deviceName"
                :value="device.id" />
            </el-select>
          </el-form-item>

          <el-form-item label="统计时段" prop="dateRange">
            <el-date-picker v-model="filterForm.dateRange" type="datetimerange" range-separator="至"
              start-placeholder="开始时间" end-placeholder="结束时间" format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss" style="width: 350px;" />
          </el-form-item>

          <el-form-item label="滤波范围" prop="filterRange">
            <div class="filter-range-container">
              <el-input-number v-model="filterForm.filterRange.min" :min="0" :max="1000" :step="0.1" placeholder="最小值"
                style="width: 120px;" />
              <span class="range-separator">Hz - </span>
              <el-input-number v-model="filterForm.filterRange.max" :min="0" :max="1000" :step="0.1" placeholder="最大值"
                style="width: 120px;" />
              <span class="range-unit">Hz</span>
            </div>
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="handleSearch" :loading="searchLoading">
              <el-icon>
                <Search />
              </el-icon>
              查询
            </el-button>
            <!-- <el-button type="success" @click="exportImage" :disabled="!hasChartData">
              <el-icon>
                <Picture />
              </el-icon>
              导出图片
            </el-button>
            <el-button type="warning" @click="exportData" :disabled="!hasChartData">
              <el-icon>
                <Download />
              </el-icon>
              导出数据
            </el-button> -->
          </el-form-item>
        </el-form>
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="charts-section" v-loading="loading" element-loading-text="数据加载中...">
      <!-- 时域图 -->
      <div class="chart-container">
        <div class="chart-header">
          <h3 class="chart-title">{{ getDeviceName(filterForm.deviceId) }} 时域图</h3>
          <div class="chart-info" v-if="chartData.timeData">
            <span class="data-info">数据点数：{{ chartData.timeData.length }}</span>
          </div>
        </div>
        <div class="chart-content">
          <div ref="timeChartRef" class="chart-canvas"></div>
        </div>
      </div>

      <!-- 频域图和滤波后时域图 -->
      <div class="charts-row">
        <!-- 频域图 -->
        <div class="chart-container half-width">
          <div class="chart-header">
            <h3 class="chart-title">{{ getDeviceName(filterForm.deviceId) }} 频域图</h3>
            <div class="chart-info" v-if="chartData.frequencyData">
              <span class="data-info">频率范围：0-{{ maxFrequency }}Hz</span>
            </div>
          </div>
          <div class="chart-content">
            <div ref="frequencyChartRef" class="chart-canvas"></div>
          </div>
        </div>

        <!-- 滤波后时域图 -->
        <div class="chart-container half-width">
          <div class="chart-header">
            <h3 class="chart-title">{{ getDeviceName(filterForm.deviceId) }} 滤波后时域图</h3>
            <div class="chart-info" v-if="chartData.filteredData">
              <span class="data-info">滤波范围：{{ filterForm.filterRange.min }}-{{ filterForm.filterRange.max }}Hz</span>
            </div>
          </div>
          <div class="chart-content">
            <div ref="filteredChartRef" class="chart-canvas"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- 数据概览 -->
    <div class="data-overview" v-if="hasChartData">
      <div class="overview-title">
        <h4>数据概览</h4>
      </div>
      <div class="overview-cards">
        <div class="overview-card">
          <div class="card-header">
            <h4 class="card-title">时域数据</h4>
          </div>
          <div class="card-content">
            <div class="value-item">
              <span class="value-label">最大值</span>
              <span class="value-number">{{ getTimeDataMax() }}</span>
            </div>
            <div class="value-item">
              <span class="value-label">最小值</span>
              <span class="value-number">{{ getTimeDataMin() }}</span>
            </div>
            <div class="value-item">
              <span class="value-label">均值</span>
              <span class="value-number">{{ getTimeDataAvg() }}</span>
            </div>
            <div class="value-item">
              <span class="value-label">数据点数</span>
              <span class="value-number">{{ chartData.timeData?.length || 0 }}</span>
            </div>
          </div>
        </div>

        <div class="overview-card">
          <div class="card-header">
            <h4 class="card-title">频域数据</h4>
          </div>
          <div class="card-content">
            <div class="value-item">
              <span class="value-label">主频</span>
              <span class="value-number">{{ getDominantFrequency() }}Hz</span>
            </div>
            <div class="value-item">
              <span class="value-label">最大幅值</span>
              <span class="value-number">{{ getFrequencyDataMax() }}</span>
            </div>
            <div class="value-item">
              <span class="value-label">频率分辨率</span>
              <span class="value-number">{{ getFrequencyResolution() }}Hz</span>
            </div>
            <div class="value-item">
              <span class="value-label">有效频率数</span>
              <span class="value-number">{{ chartData.frequencyData?.length || 0 }}</span>
            </div>
          </div>
        </div>

        <div class="overview-card">
          <div class="card-header">
            <h4 class="card-title">滤波后数据</h4>
          </div>
          <div class="card-content">
            <div class="value-item">
              <span class="value-label">最大值</span>
              <span class="value-number">{{ getFilteredDataMax() }}</span>
            </div>
            <div class="value-item">
              <span class="value-label">最小值</span>
              <span class="value-number">{{ getFilteredDataMin() }}</span>
            </div>
            <div class="value-item">
              <span class="value-label">均值</span>
              <span class="value-number">{{ getFilteredDataAvg() }}</span>
            </div>
            <div class="value-item">
              <span class="value-label">信噪比改善</span>
              <span class="value-number">{{ getSNRImprovement() }}dB</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <!-- <div class="empty-state" v-if="!loading && !hasChartData">
      <el-empty description="暂无数据，请选择查询条件后点击查询" />
    </div> -->
  </div>
</template>

<script setup>
import { ref, onMounted, reactive, nextTick, onBeforeUnmount, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Picture, Download } from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import moment from 'moment'
import {
  getBridgeBasicInfoList,
  getPipelineInfoList,
  getFilterAnalysis
} from '@/api/bridge'
import { DEVICE_TYPE_OPTIONS } from '@/constants/bridge'

// 响应式数据
const loading = ref(false)
const searchLoading = ref(false)
const filterFormRef = ref(null)

// 表单数据
const filterForm = reactive({
  bridgeId: '',
  deviceType: '',
  deviceId: '',
  dateRange: [],
  filterRange: {
    min: 0.1,
    max: 5.0
  }
})

// 下拉选项数据
const bridgeList = ref([])
const deviceNameList = ref([])
const deviceTypeOptions = ref(DEVICE_TYPE_OPTIONS)

// 图表相关
const timeChartRef = ref(null)
const frequencyChartRef = ref(null)
const filteredChartRef = ref(null)
let timeChartInstance = null
let frequencyChartInstance = null
let filteredChartInstance = null

// 图表数据
const chartData = reactive({
  timeData: null,
  frequencyData: null,
  filteredData: null,
  unit: '',
  unitName: ''
})

// 计算属性
const hasChartData = computed(() => {
  return chartData.timeData || chartData.frequencyData || chartData.filteredData
})

const maxFrequency = computed(() => {
  if (!chartData.frequencyData || chartData.frequencyData.length === 0) return 0
  return Math.max(...chartData.frequencyData.map(item => item.frequency)).toFixed(1)
})

// 初始化
onMounted(async () => {
  await loadBridgeList()
  await loadDeviceList()
  // 设置默认时间范围为最近1小时
  setDefaultDateRange()
})

// 组件卸载时销毁图表
onBeforeUnmount(() => {
  destroyCharts()
})

// 设置默认时间范围
const setDefaultDateRange = () => {
  const endTime = moment().format('YYYY-MM-DD HH:mm:ss')
  const startTime = moment().subtract(1, 'hours').format('YYYY-MM-DD HH:mm:ss')
  filterForm.dateRange = [startTime, endTime]
}

// 加载桥梁列表
const loadBridgeList = async () => {
  try {
    const response = await getBridgeBasicInfoList()
    if (response.code === 200 && response.data) {
      bridgeList.value = response.data.map(item => ({
        id: item.id,
        bridgeName: item.bridgeName
      }))
    }
  } catch (error) {
    console.error('加载桥梁列表失败:', error)
    ElMessage.error('加载桥梁列表失败')
  }
}

// 加载设备列表
const loadDeviceList = async (bridgeId = '', deviceType = '') => {
  try {
    const params = {}
    if (bridgeId) {
      params.bridgeId = bridgeId
    }
    if (deviceType) {
      params.deviceType = deviceType
    }

    const response = await getPipelineInfoList(params)
    if (response.code === 200 && response.data) {
      deviceNameList.value = response.data.map(item => ({
        id: item.id,
        deviceName: item.deviceName
      }))
    }
  } catch (error) {
    console.error('加载设备列表失败:', error)
    ElMessage.error('加载设备列表失败')
  }
}

// 桥梁选择变化
const handleBridgeChange = (bridgeId) => {
  filterForm.deviceId = ''
  if (bridgeId || filterForm.deviceType) {
    loadDeviceList(bridgeId, filterForm.deviceType)
  } else {
    loadDeviceList()
  }
}

// 设备类型变化
const handleDeviceTypeChange = (deviceType) => {
  filterForm.deviceId = ''
  if (filterForm.bridgeId || deviceType) {
    loadDeviceList(filterForm.bridgeId, deviceType)
  } else {
    loadDeviceList()
  }
}

// 查询数据
const handleSearch = async () => {
  // 验证表单
  if (!filterForm.bridgeId) {
    ElMessage.warning('请选择桥梁名称')
    return
  }

  if (!filterForm.deviceType) {
    ElMessage.warning('请选择设备类型')
    return
  }

  if (!filterForm.deviceId) {
    ElMessage.warning('请选择设备名称')
    return
  }

  if (!filterForm.dateRange || filterForm.dateRange.length !== 2) {
    ElMessage.warning('请选择统计时段')
    return
  }

  if (filterForm.filterRange.min >= filterForm.filterRange.max) {
    ElMessage.warning('滤波范围最小值应小于最大值')
    return
  }

  searchLoading.value = true
  loading.value = true

  try {
    // 构建请求参数
    const params = {
      bridgeName: getBridgeName(filterForm.bridgeId),
      deviceName: getDeviceName(filterForm.deviceId),
      deviceType: filterForm.deviceType,
      startDate: filterForm.dateRange[0],
      endDate: filterForm.dateRange[1],
      filterMin: filterForm.filterRange.min,
      filterMax: filterForm.filterRange.max
    }

    const response = await getFilterAnalysis(params)

    if (response.code === 200) {
      if (!response.data) {
        ElMessage.info('查询结果为空')
        clearChartData()
        return
      }

      // 处理返回数据 - 这里需要根据实际API返回结构调整
      processResponseData(response.data)

      // 渲染图表
      await nextTick()
      renderCharts()

      if (!hasChartData.value) {
        ElMessage.info('查询结果为空')
      }
    } else {
      ElMessage.error(response.message || '查询失败')
    }
  } catch (error) {
    console.error('查询数据失败:', error)
    ElMessage.error('查询数据失败')
  } finally {
    searchLoading.value = false
    loading.value = false
  }
}

// 处理响应数据
const processResponseData = (data) => {
  // 模拟数据处理 - 实际需要根据API返回结构调整
  if (data.timeData) {
    chartData.timeData = data.timeData
  } else {
    // 生成模拟时域数据
    chartData.timeData = generateMockTimeData()
  }

  if (data.frequencyData) {
    chartData.frequencyData = data.frequencyData
  } else {
    // 生成模拟频域数据
    chartData.frequencyData = generateMockFrequencyData()
  }

  if (data.filteredData) {
    chartData.filteredData = data.filteredData
  } else {
    // 生成模拟滤波后数据
    chartData.filteredData = generateMockFilteredData()
  }

  chartData.unit = data.unit || ''
  chartData.unitName = data.unitName || '加速度'
}

// 生成模拟时域数据
const generateMockTimeData = () => {
  const data = []
  const startTime = moment(filterForm.dateRange[0])
  const endTime = moment(filterForm.dateRange[1])
  const duration = endTime.diff(startTime, 'seconds')
  const sampleRate = 100 // 100Hz采样率
  const totalSamples = Math.min(duration * sampleRate, 10000) // 限制最大点数

  for (let i = 0; i < totalSamples; i++) {
    const time = startTime.clone().add(i / sampleRate, 'seconds')
    const t = i / sampleRate
    // 模拟信号：主频1Hz + 噪声
    const signal = 10 * Math.sin(2 * Math.PI * 1 * t) +
      5 * Math.sin(2 * Math.PI * 3 * t) +
      2 * (Math.random() - 0.5)
    data.push({
      time: time.format('YYYY-MM-DD HH:mm:ss.SSS'),
      value: signal
    })
  }
  return data
}

// 生成模拟频域数据
const generateMockFrequencyData = () => {
  const data = []
  const maxFreq = 50 // 最大频率50Hz
  const freqStep = 0.1 // 频率步长

  for (let freq = 0; freq <= maxFreq; freq += freqStep) {
    let amplitude = 0
    // 在1Hz和3Hz处有峰值
    if (Math.abs(freq - 1) < 0.5) {
      amplitude = 10 * Math.exp(-Math.pow((freq - 1) / 0.2, 2))
    }
    if (Math.abs(freq - 3) < 0.5) {
      amplitude += 5 * Math.exp(-Math.pow((freq - 3) / 0.3, 2))
    }
    // 添加噪声
    amplitude += 0.1 * Math.random()

    data.push({
      frequency: freq,
      amplitude: amplitude
    })
  }
  return data
}

// 生成模拟滤波后数据
const generateMockFilteredData = () => {
  const data = []
  const startTime = moment(filterForm.dateRange[0])
  const endTime = moment(filterForm.dateRange[1])
  const duration = endTime.diff(startTime, 'seconds')
  const sampleRate = 100
  const totalSamples = Math.min(duration * sampleRate, 10000)

  for (let i = 0; i < totalSamples; i++) {
    const time = startTime.clone().add(i / sampleRate, 'seconds')
    const t = i / sampleRate
    // 滤波后的信号：只保留滤波范围内的频率
    let signal = 0
    if (1 >= filterForm.filterRange.min && 1 <= filterForm.filterRange.max) {
      signal += 10 * Math.sin(2 * Math.PI * 1 * t)
    }
    if (3 >= filterForm.filterRange.min && 3 <= filterForm.filterRange.max) {
      signal += 5 * Math.sin(2 * Math.PI * 3 * t)
    }
    // 减少噪声
    signal += 0.5 * (Math.random() - 0.5)

    data.push({
      time: time.format('YYYY-MM-DD HH:mm:ss.SSS'),
      value: signal
    })
  }
  return data
}

// 清空图表数据
const clearChartData = () => {
  chartData.timeData = null
  chartData.frequencyData = null
  chartData.filteredData = null
  chartData.unit = ''
  chartData.unitName = ''
}

// 获取桥梁名称
const getBridgeName = (bridgeId) => {
  const bridge = bridgeList.value.find(item => item.id === bridgeId)
  return bridge ? bridge.bridgeName : ''
}

// 获取设备名称
const getDeviceName = (deviceId) => {
  const device = deviceNameList.value.find(item => item.id === deviceId)
  return device ? device.deviceName : ''
}

// 渲染所有图表
const renderCharts = () => {
  renderTimeChart()
  renderFrequencyChart()
  renderFilteredChart()
}

// 渲染时域图
const renderTimeChart = () => {
  if (!timeChartRef.value || !chartData.timeData) return

  if (timeChartInstance) {
    timeChartInstance.dispose()
  }

  timeChartInstance = echarts.init(timeChartRef.value)

  const data = chartData.timeData.map(item => [
    moment(item.time).valueOf(),
    item.value
  ])

  const option = {
    tooltip: {
      trigger: 'axis',
      formatter: function (params) {
        const time = moment(params[0].axisValue).format('YYYY-MM-DD HH:mm:ss.SSS')
        return `时间: ${time}<br/>数值: ${params[0].value[1].toFixed(3)} ${chartData.unitName}`
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '10%',
      containLabel: true
    },
    xAxis: {
      type: 'time',
      axisLabel: {
        formatter: function (value) {
          return moment(value).format('HH:mm:ss')
        }
      }
    },
    yAxis: {
      type: 'value',
      name: chartData.unitName
    },
    series: [{
      type: 'line',
      data: data,
      symbol: 'none',
      lineStyle: {
        width: 1,
        color: '#1890ff'
      },
      sampling: 'lttb'
    }]
  }

  timeChartInstance.setOption(option)
}

// 渲染频域图
const renderFrequencyChart = () => {
  if (!frequencyChartRef.value || !chartData.frequencyData) return

  if (frequencyChartInstance) {
    frequencyChartInstance.dispose()
  }

  frequencyChartInstance = echarts.init(frequencyChartRef.value)

  const data = chartData.frequencyData.map(item => [item.frequency, item.amplitude])

  const option = {
    tooltip: {
      trigger: 'axis',
      formatter: function (params) {
        return `频率: ${params[0].value[0].toFixed(2)} Hz<br/>幅值: ${params[0].value[1].toFixed(3)}`
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '10%',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      name: '频率 (Hz)'
    },
    yAxis: {
      type: 'value',
      name: '幅值'
    },
    series: [{
      type: 'line',
      data: data,
      symbol: 'none',
      lineStyle: {
        width: 1,
        color: '#52c41a'
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [{
            offset: 0,
            color: '#52c41a40'
          }, {
            offset: 1,
            color: '#52c41a10'
          }]
        }
      }
    }]
  }

  frequencyChartInstance.setOption(option)
}

// 渲染滤波后时域图
const renderFilteredChart = () => {
  if (!filteredChartRef.value || !chartData.filteredData) return

  if (filteredChartInstance) {
    filteredChartInstance.dispose()
  }

  filteredChartInstance = echarts.init(filteredChartRef.value)

  const data = chartData.filteredData.map(item => [
    moment(item.time).valueOf(),
    item.value
  ])

  const option = {
    tooltip: {
      trigger: 'axis',
      formatter: function (params) {
        const time = moment(params[0].axisValue).format('YYYY-MM-DD HH:mm:ss.SSS')
        return `时间: ${time}<br/>数值: ${params[0].value[1].toFixed(3)} ${chartData.unitName}`
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '10%',
      containLabel: true
    },
    xAxis: {
      type: 'time',
      axisLabel: {
        formatter: function (value) {
          return moment(value).format('HH:mm:ss')
        }
      }
    },
    yAxis: {
      type: 'value',
      name: chartData.unitName
    },
    series: [{
      type: 'line',
      data: data,
      symbol: 'none',
      lineStyle: {
        width: 1,
        color: '#fa541c'
      },
      sampling: 'lttb'
    }]
  }

  filteredChartInstance.setOption(option)
}

// 销毁所有图表
const destroyCharts = () => {
  if (timeChartInstance) {
    timeChartInstance.dispose()
    timeChartInstance = null
  }
  if (frequencyChartInstance) {
    frequencyChartInstance.dispose()
    frequencyChartInstance = null
  }
  if (filteredChartInstance) {
    filteredChartInstance.dispose()
    filteredChartInstance = null
  }
}

// 数据统计方法
const getTimeDataMax = () => {
  if (!chartData.timeData) return 0
  return Math.max(...chartData.timeData.map(item => item.value)).toFixed(3)
}

const getTimeDataMin = () => {
  if (!chartData.timeData) return 0
  return Math.min(...chartData.timeData.map(item => item.value)).toFixed(3)
}

const getTimeDataAvg = () => {
  if (!chartData.timeData) return 0
  const sum = chartData.timeData.reduce((acc, item) => acc + item.value, 0)
  return (sum / chartData.timeData.length).toFixed(3)
}

const getDominantFrequency = () => {
  if (!chartData.frequencyData) return 0
  let maxAmplitude = 0
  let dominantFreq = 0
  chartData.frequencyData.forEach(item => {
    if (item.amplitude > maxAmplitude) {
      maxAmplitude = item.amplitude
      dominantFreq = item.frequency
    }
  })
  return dominantFreq.toFixed(2)
}

const getFrequencyDataMax = () => {
  if (!chartData.frequencyData) return 0
  return Math.max(...chartData.frequencyData.map(item => item.amplitude)).toFixed(3)
}

const getFrequencyResolution = () => {
  if (!chartData.frequencyData || chartData.frequencyData.length < 2) return 0
  return (chartData.frequencyData[1].frequency - chartData.frequencyData[0].frequency).toFixed(3)
}

const getFilteredDataMax = () => {
  if (!chartData.filteredData) return 0
  return Math.max(...chartData.filteredData.map(item => item.value)).toFixed(3)
}

const getFilteredDataMin = () => {
  if (!chartData.filteredData) return 0
  return Math.min(...chartData.filteredData.map(item => item.value)).toFixed(3)
}

const getFilteredDataAvg = () => {
  if (!chartData.filteredData) return 0
  const sum = chartData.filteredData.reduce((acc, item) => acc + item.value, 0)
  return (sum / chartData.filteredData.length).toFixed(3)
}

const getSNRImprovement = () => {
  // 简单的信噪比改善计算
  if (!chartData.timeData || !chartData.filteredData) return 0
  const originalStd = calculateStd(chartData.timeData.map(item => item.value))
  const filteredStd = calculateStd(chartData.filteredData.map(item => item.value))
  const improvement = 20 * Math.log10(originalStd / filteredStd)
  return improvement.toFixed(1)
}

const calculateStd = (data) => {
  const mean = data.reduce((acc, val) => acc + val, 0) / data.length
  const variance = data.reduce((acc, val) => acc + Math.pow(val - mean, 2), 0) / data.length
  return Math.sqrt(variance)
}

// 导出图片
const exportImage = () => {
  if (!hasChartData.value) {
    ElMessage.warning('暂无图表数据')
    return
  }

  // 创建一个临时canvas来合并三个图表
  const canvas = document.createElement('canvas')
  const ctx = canvas.getContext('2d')
  canvas.width = 1200
  canvas.height = 800

  // 设置白色背景
  ctx.fillStyle = '#ffffff'
  ctx.fillRect(0, 0, canvas.width, canvas.height)

  let completedCharts = 0
  const totalCharts = 3

  const checkComplete = () => {
    completedCharts++
    if (completedCharts === totalCharts) {
      const link = document.createElement('a')
      link.download = `滤波分析_${moment().format('YYYY-MM-DD_HH-mm-ss')}.png`
      link.href = canvas.toDataURL()
      link.click()
      ElMessage.success('图片导出成功')
    }
  }

  // 导出时域图
  if (timeChartInstance) {
    const timeImg = new Image()
    timeImg.onload = () => {
      ctx.drawImage(timeImg, 0, 0, 1200, 300)
      checkComplete()
    }
    timeImg.src = timeChartInstance.getDataURL({ backgroundColor: '#fff' })
  } else {
    checkComplete()
  }

  // 导出频域图
  if (frequencyChartInstance) {
    const freqImg = new Image()
    freqImg.onload = () => {
      ctx.drawImage(freqImg, 0, 300, 600, 250)
      checkComplete()
    }
    freqImg.src = frequencyChartInstance.getDataURL({ backgroundColor: '#fff' })
  } else {
    checkComplete()
  }

  // 导出滤波后时域图
  if (filteredChartInstance) {
    const filteredImg = new Image()
    filteredImg.onload = () => {
      ctx.drawImage(filteredImg, 600, 300, 600, 250)
      checkComplete()
    }
    filteredImg.src = filteredChartInstance.getDataURL({ backgroundColor: '#fff' })
  } else {
    checkComplete()
  }
}

// 导出数据
const exportData = () => {
  if (!hasChartData.value) {
    ElMessage.warning('暂无数据')
    return
  }

  try {
    let csvContent = '\uFEFF' // BOM for UTF-8

    // 时域数据
    if (chartData.timeData) {
      csvContent += '时域数据\n'
      csvContent += '时间,数值\n'
      chartData.timeData.forEach(item => {
        csvContent += `${item.time},${item.value}\n`
      })
      csvContent += '\n'
    }

    // 频域数据
    if (chartData.frequencyData) {
      csvContent += '频域数据\n'
      csvContent += '频率(Hz),幅值\n'
      chartData.frequencyData.forEach(item => {
        csvContent += `${item.frequency},${item.amplitude}\n`
      })
      csvContent += '\n'
    }

    // 滤波后数据
    if (chartData.filteredData) {
      csvContent += '滤波后时域数据\n'
      csvContent += '时间,数值\n'
      chartData.filteredData.forEach(item => {
        csvContent += `${item.time},${item.value}\n`
      })
    }

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    link.href = URL.createObjectURL(blob)
    link.download = `滤波分析数据_${moment().format('YYYY-MM-DD_HH-mm-ss')}.csv`
    link.click()

    ElMessage.success('数据导出成功')
  } catch (error) {
    console.error('导出数据失败:', error)
    ElMessage.error('导出数据失败')
  }
}

// 监听窗口大小变化
const handleResize = () => {
  timeChartInstance && timeChartInstance.resize()
  frequencyChartInstance && frequencyChartInstance.resize()
  filteredChartInstance && filteredChartInstance.resize()
}

onMounted(() => {
  window.addEventListener('resize', handleResize)
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
.bridge-page-container {
  height: calc(100vh - 200px);
  overflow-y: auto;
}

/* 筛选区域 */
.filter-section {
  margin-bottom: 20px;
}

.filter-card {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.filter-form {
  margin: 0;
}

.filter-form .el-form-item {
  margin-bottom: 16px;
  margin-right: 20px;
}

.filter-form .el-form-item:last-child {
  margin-right: 0;
}

/* 滤波范围 */
.filter-range-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.range-separator {
  color: #666;
  font-size: 14px;
}

.range-unit {
  color: #666;
  font-size: 14px;
  margin-left: 4px;
}

/* 图表区域 */
.charts-section {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
  padding: 20px;
}

.chart-container {
  margin-bottom: 30px;
}

.chart-container:last-child {
  margin-bottom: 0;
}

.charts-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.half-width {
  margin-bottom: 0;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #f0f0f0;
}

.chart-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.chart-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.data-info {
  font-size: 12px;
  color: #666;
  background: #f0f2f5;
  padding: 2px 8px;
  border-radius: 4px;
}

.chart-content {
  height: 300px;
}

.chart-canvas {
  width: 100%;
  height: 100%;
}

/* 数据概览区域 */
.data-overview {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
}

.overview-title {
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.overview-title h4 {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.overview-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.overview-card {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #e9ecef;
}

.card-header {
  margin-bottom: 12px;
}

.card-title {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.card-content {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.value-item {
  text-align: center;
  padding: 8px;
  background: white;
  border-radius: 6px;
  border: 1px solid #f0f0f0;
}

.value-label {
  display: block;
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
}

.value-number {
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

/* 空状态 */
.empty-state {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 40px;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .filter-form {
    display: block;
  }

  .filter-form .el-form-item {
    display: block;
    margin-bottom: 16px;
    margin-right: 0;
  }

  .charts-row {
    grid-template-columns: 1fr;
  }

  .chart-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .chart-info {
    align-self: flex-end;
  }
}

@media (max-width: 768px) {
  .bridge-page-container {
    padding: 10px;
  }

  .filter-card {
    padding: 15px;
  }

  .charts-section {
    padding: 15px;
  }

  .chart-content {
    height: 250px;
  }

  .overview-cards {
    grid-template-columns: 1fr;
  }

  .card-content {
    grid-template-columns: repeat(2, 1fr);
  }

  .filter-range-container {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .range-separator {
    text-align: center;
  }
}
</style>