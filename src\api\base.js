import request from '@/utils/request'

/**
 * 获取视频通道树列表
 * @param {Object} params - 查询参数
 * @param {string} params.name - 设备名称，用于筛选
 * @returns {Promise} 返回Promise对象
 */
export function getVideoChannelTreeList(params = {}) {
  return request({
    url: '/basic/usmVideoChannel/videoChannelTreeList',
    method: 'post',
    data: {
      name: params.name || ''
    }
  }).catch(error => {
    console.error('获取视频通道树列表失败:', error);
    throw error;
  });
}

/**
 * 获取视频流地址
 * @param {Object} params - 查询参数
 * @param {string} params.deviceId - 设备ID
 * @param {string} params.channelId - 通道ID
 * @returns {Promise} 返回Promise对象
 */
export function getVideoStreamHls(params) {
  if (!params || !params.deviceId || !params.channelId) {
    return Promise.reject(new Error('设备ID和通道ID不能为空'));
  }
  
  return request({
    url: '/basic/usmVideoStream/queryStreamHls',
    method: 'post',
    data: {
      deviceId: params.deviceId,
      channelId: params.channelId
    }
  }).catch(error => {
    console.error('获取视频流地址失败:', error);
    throw error;
  });
}

export function getVideoChannelCount() {
  return request({
    url: '/basic/usmVideoChannel/videoChannelCount',
    method: 'post'
  })
}
