//燃气监测设备详情配置
export const gas_deviceInfoConfig = [
  { label: "设备名称", props: "deviceName", type:"row"},
  { label: "设备编码", props: "indexCode", type:"row"},
  { label: "设备类型", props: "deviceTypeName", type:"row"},
  { label: "监测指标", props: "monitorIndexName", type:"row"},
  { label: "安装时间", props: "installTime", type:"row"},
  { label: "权属单位", props: "ownershipUnitName", type:"row"},
  { label: "设备状态", props: "onlineStatus", type:"row"},
  { label: "位置信息", props: "address", type:"row"},
];


// 燃气管线详情
export const gas_guanxianConfig = [
  { label: "管段编码", props: "pipelineCode"},
  { label: "压力级别", props: "pressureLevelName"},
  { label: "管径", props: "pipeDiameter", unit: "mm"},
  { label: "管线材质", props: "materialName"},
  { label: "管线长度", props: "pipeLength", unit: "m"},
  { label: "所属单位", props: "managementUnitName"},
  { label: "所属区域", props: "address", type: "row" },
  { label: "所在道路", props: "roadName", type: "row" }
];

// 燃气场站详情
export const gas_changzhanConfig = [
  { label: "场站名称", props: "stationName"},
  { label: "场站类型", props: "stationTypeName"},
  { label: "分输介质", props: "distributionMedium"},
  { label: "设计供气能力", props: "designCapacity", unit: "立方米/日"},
  { label: "总储罐容量", props: "totalTankVolume"},
  { label: "储罐数量", props: "tankCount"},
  { label: "储罐类型", props: "tankTypeName"},
  { label: "储罐压力等级", props: "tankPressureLevel"},
  { label: "投运时间", props: "operationTime"},
  { label: "权属单位", props: "managementUnitName"},
  { label: "联系人", props: "contactPerson"},
  { label: "联系电话", props: "contactPhone"},
  { label: "位置", props: "address", type: "row" }
];

// 燃气窨井详情
export const gas_yinjingConfig = [
  { label: "窨井编码", props: "wellCode"},
  { label: "井深", props: "wellDepth", unit: "m"},
  { label: "所在道路", props: "roadName"},
  { label: "窨井形状", props: "wellShapeName"},
  { label: "窨井材质", props: "wellMaterialName"},
  { label: "井盖尺寸", props: "wellSize"},
  { label: "井室规格", props: "wellChamberSpecification"},
  { label: "埋深", props: "buriedDepth", unit: "m"},
  { label: "关联管线", props: "connectedPipeline"},
  { label: "权属单位", props: "managementUnitName"},
  { label: "建造时间", props: "installTime"},
  { label: "位置", props: "address"}
];

// 燃气管点详情
export const gas_guandianConfig = [
  { label: "管点编码", props: "pointCode"},
  { label: "管点类型", props: "pointTypeName"},
  { label: "埋深", props: "depth", unit: "m"},
  { label: "高度", props: "elevation", unit: "m"},
  { label: "附属物", props: "attachedFacilities"},
  { label: "所在道路", props: "roadName"},
  { label: "关联管线", props: "connectedPipeline"},
  { label: "关联窨井", props: "connectedWell"},
  { label: "权属单位", props: "managementUnitName"},
  { label: "安装时间", props: "installTime"},
  { label: "位置", props: "address", type: "row" }
];

// 燃气危险源详情
export const gas_weixianyuanConfig = [
  { label: "危险源编码", props: "dangerCode"},
  { label: "危险源名称", props: "dangerName" },
  { label: "建筑类型", props: "buildingTypeName"},
  { label: "是否重大危险源", props: "isMajor"},
  { label: "特征描述", props: "featureDesc", type: "row" },
  { label: "风险等级", props: "riskLevelName"},
  { label: "影响半径", props: "influenceRadius", unit: "km"},
  { label: "财产损失", props: "propertyLoss"},
  { label: "所属单位", props: "managementUnitName"},
  { label: "联系人", props: "contactUser"},
  { label: "联系电话", props: "contactInfo"},
  { label: "位置", props: "address", type: "row" }
];

// 燃气防护目标详情
export const gas_fanghumubiaoConfig = [
  { label: "防护目标编码", props: "protectCode"},
  { label: "防护目标名称", props: "protectName"},
  { label: "建筑类型", props: "buildingTypeName"},
  { label: "是否重点防护目标", props: "isMajor"},
  { label: "建筑面积", props: "buildingArea"},
  { label: "满负荷人数", props: "fullPeopleNumber"},
  { label: "建成年份", props: "buildingYear"},
  { label: "所属单位", props: "managementUnitName"},
  { label: "联系人", props: "contactUser"},
  { label: "联系电话", props: "contactInfo"},
  { label: "位置", props: "address", type: "row" }
];

// 燃气管线风险详情
export const gas_guanxianRiskConfig = [
  { label: "风险等级", props: "riskLevelName"},
  { label: "风险编号", props: "riskCode"},
  { label: "压力级别", props: "pressureLevelName"},
  { label: "管材", props: "materialName"},
  { label: "管径", props: "pipeDiameter"},
  { label: "管长", props: "pipeLength",  unit: "m"},
  { label: "所在道路", props: "roadName", type: "row" },
  { label: "建设时间", props: "constructionTime"},
  { label: "评估日期", props: "assessmentDate"},
  { label: "管控状态", props: "pipelineStatusName", type: "row" }
];

// 燃气场站风险详情
export const gas_changzhanRiskConfig = [
  { label: "风险等级", props: "riskLevelName"},
  { label: "风险编号", props: "riskCode"},
  { label: "场站类型", props: "stationTypeName"},
  { label: "场站位置", props: "address"},
  { label: "分输介质", props: "distributionMedium"},
  { label: "投入运行时间", props: "operationTime"},
  { label: "评估日期", props: "assessmentDate", type: "row" }
];

// 排水管线详情
export const drainage_guanxianConfig = [
  { label: "管道编号", props: "pipelineCode"},
  { label: "管道类型", props: "pipelineTypeName"},
  { label: "管道材质", props: "materialName"},
  { label: "管道外径", props: "pipeDiameter", unit: "mm"},
  { label: "起点埋深", props: "startPointDepth", unit: "m"},
  { label: "终点埋深", props: "endPointDepth", unit: "m"},
  { label: "管长", props: "pipeLength", unit: "m"},
  { label: "管理单位", props: "managementUnitName"},
  { label: "建设年代", props: "constructionTime"},
  { label: "地理位置", props: "address", type: "row" }
];

// 排水污水厂详情
export const drainage_sewageConfig = [
  { label: "污水厂名称", props: "factoryName"},
  { label: "污水厂类型", props: "factoryTypeName"},
  { label: "污水处理级别", props: "handleLevel"},
  { label: "污水处理设计计划量", props: "handleModel", unit: "m³/天"},
  { label: "所在道路", props: "roadName"},
  { label: "占地面积", props: "floorArea"},
  { label: "污水处理设施类型", props: "facilityType"},
  { label: "排水去向", props: "drainDirection"},
  { label: "处理工艺", props: "handleTechnology"},
  { label: "污泥去向", props: "sludgeDirection"},
  { label: "建设单位", props: "constructionUnitName"},
  { label: "运行时间", props: "constructionTime"},
  { label: "联系人", props: "contactUser"},
  { label: "联系电话", props: "contactInfo"},
  { label: "地理位置", props: "address", type: "row" }
];

// 排水窨井详情
export const drainage_yinjingConfig = [
  { label: "窨井编码", props: "wellCode"},
  { label: "井深", props: "wellDepth", unit: "m"},
  { label: "所在道路", props: "roadName"},
  { label: "窨井形状", props: "wellShapeName"},
  { label: "窨井材质", props: "wellMaterialName"},
  { label: "井盖尺寸", props: "wellSize"},
  { label: "井室规格", props: "wellRoomStandard"},
  { label: "埋深", props: "buriedDepth", unit: "m"},
  { label: "关联管线", props: "pipelineId"},
  { label: "权属单位", props: "managementUnitName"},
  { label: "建设时间", props: "constructionTime"},
  { label: "位置", props: "address"}
];

// 排水管点详情
export const drainage_guandianConfig = [
  { label: "管点编码", props: "pointCode"},
  { label: "管点类型", props: "pointTypeName"},
  { label: "埋深", props: "depth", unit: "m"},
  { label: "高程", props: "elevation", unit: "m"},
  { label: "附属物", props: "attachedFacilities"},
  { label: "所在道路", props: "roadName"},
  { label: "关联管线", props: "connectedPipelineId"},
  { label: "关联窨井", props: "connectedWellId"},
  { label: "权属单位", props: "managementUnitName"},
  { label: "安装时间", props: "installTime"},
  { label: "位置", props: "address", type: "row" }
];

// 排水泵站详情
export const drainage_pumpConfig = [
  { label: "泵站编码", props: "stationCode"},
  { label: "泵站名称", props: "stationName"},
  { label: "泵站类型", props: "stationTypeName"},
  { label: "拥有泵数量", props: "pumpNum"},
  { label: "设计雨水排水水能力", props: "drainRainCapacity", unit: "m³/s"},
  { label: "设计污水排水能力", props: "drainSewageCapacity", unit: "m³/s"},
  { label: "服务范围", props: "serverRange"},
  { label: "服务面积", props: "serverArea"},
  { label: "运行时间", props: "workTime"},
  { label: "建设单位", props: "constructionUnitName"},
  { label: "联系人", props: "contactUser"},
  { label: "联系方式", props: "contactInfo"},
  { label: "位置", props: "address", type: "row" }
];

// 排水口详情
export const drainage_outletConfig = [
  { label: "排口名称", props: "outletName"},
  { label: "排口类型", props: "outletTypeName"},
  { label: "地表高程", props: "floorDistance", unit: "m"},
  { label: "顶部高程", props: "topDistance", unit: "m"},
  { label: "排口尺寸", props: "outletSize", unit: "mm"},
  { label: "出流形式", props: "flowType"},
  { label: "是否有闸门", props: "isFlapGate"},
  { label: "位置", props: "address"}
];

// 排水危险源详情
export const drainage_dangerConfig = [
  { label: "危险源编码", props: "dangerCode"},
  { label: "危险源名称", props: "dangerName"},
  { label: "建筑类型", props: "buildingTypeName"},
  { label: "是否重大危险源", props: "isMajor"},
  { label: "特征描述", props: "featureDesc", type: "row" },
  { label: "风险等级", props: "riskLevelName"},
  { label: "影响半径", props: "influenceRadius", unit: "km"},
  { label: "财产损失", props: "propertyLoss"},
  { label: "所属单位", props: "managementUnitName"},
  { label: "联系人", props: "contactUser"},
  { label: "联系电话", props: "contactInfo"},
  { label: "位置", props: "address", type: "row" }
];

// 排水防护目标详情
export const drainage_protectionConfig = [
  { label: "防护目标编码", props: "protectCode"},
  { label: "防护目标名称", props: "protectName"},
  { label: "建筑类型", props: "buildingTypeName"},
  { label: "是否重点防护目标", props: "isMajor"},
  { label: "建筑面积", props: "buildingArea"},
  { label: "满负荷人数", props: "fullPeopleNumber"},
  { label: "建筑年份", props: "buildingYear"},
  { label: "所属单位", props: "managementUnitName"},
  { label: "联系人", props: "contactUser"},
  { label: "联系电话", props: "contactInfo"},
  { label: "位置", props: "address", type: "row" }
];

// 管线风险详情
export const drainage_pipelineRiskConfig = [
  { label: "风险等级", props: "riskLevelName"},
  { label: "风险编号", props: "riskCode"},
  { label: "管网类型", props: "pipelineTypeName"},
  { label: "管材", props: "materialName"},
  { label: "管径", props: "pipeDiameter", unit: "mm"},
  { label: "管长", props: "pipeLength", unit: "m"},
  { label: "所在道路", props: "roadName"},
  { label: "建设时间", props: "constructionTime"},
  { label: "评估日期", props: "assessmentDate"},
  { label: "管控状态", props: "pipelineStatusName"}
];

// 污水厂风险详情
export const drainage_sewageRiskConfig = [
  { label: "风险等级", props: "riskLevelName"},
  { label: "风险编号", props: "riskCode"},
  { label: "场站类型", props: "factoryTypeName"},
  { label: "位置", props: "address"},
  { label: "污水处理设计规模", props: "handleModel", unit: "m³/天"},
  { label: "投入运行时间", props: "constructionTime"},
  { label: "评估日期", props: "assessmentDate"},
  { label: "管控状态", props: "factoryStatusName"}
];

// 泵站风险详情
export const drainage_pumpRiskConfig = [
  { label: "风险等级", props: "riskLevelName"},
  { label: "风险编号", props: "riskCode"},
  { label: "管线编码", props: "pipelineCode"},
  { label: "泵站类型", props: "stationTypeName"},
  { label: "设计雨水排水能力", props: "drainRainCapacity", unit: "m³/s"},
  { label: "设计污水排水能力", props: "drainSewageCapacity", unit: "m³/s"},
  { label: "位置", props: "address"},
  { label: "投入运行时间", props: "workTime"},
  { label: "评估日期", props: "assessmentDate"},
  { label: "管控状态", props: "stationStatusName"}
];

// 隐患详情
export const drainage_hiddenDangerConfig = [
  { label: "隐患来源", props: "dangerSourceName", type: "row" },
  { label: "隐患描述", props: "dangerDesc", type: "row" },
  { label: "隐患类型", props: "dangerTypeName", type: "row" },
  { label: "隐患对象", props: "dangerObjectName", type: "row" },
  { label: "隐患等级", props: "dangerLevelName", type: "row" },
  { label: "隐患位置", props: "address", type: "row" },
  { label: "整改期限", props: "rectificationDeadline", type: "row" },
  { label: "责任人", props: "responsibleUserName", type: "row" },
  { label: "隐患图片", props: "fileList", isImg: true, fileProp: "picUrls", type: "row" }
];

// 易涝点详情
export const drainage_waterloggingConfig = [
  { label: "易涝点名称", props: "pointName", type: "row" },
  { label: "风险等级", props: "riskLevelName", type: "row" },
  { label: "最长积水时间", props: "maxHydropsTime", unit: "小时", type: "row" },
  { label: "最大积水面积", props: "maxHydropsArea", unit: "m²", type: "row" },
  { label: "积水原因", props: "hydropsReason", type: "row" },
  { label: "易涝点产生时间", props: "happenTime", type: "row" },
  { label: "地理位置", props: "address", type: "row" }
];

// 监测设备基本信息详情
export const drainage_monitoringDeviceConfig = [
  { label: "设备名称", props: "deviceName", type:"row"},
  { label: "设备编码", props: "indexCode", type:"row"},
  { label: "设备类型", props: "deviceTypeName", type:"row"},
  { label: "监测指标", props: "monitorIndexName", type:"row"},
  { label: "安装时间", props: "installTime", type:"row"},
  { label: "权属单位", props: "ownershipUnitName", type:"row"},
  { label: "设备状态", props: "onlineStatus", type:"row"},
  { label: "位置信息", props: "address", type:"row"},
];

// 报警详情
export const drainage_alarmConfig = [
  { label: "报警编号", props: "alarmCode", type: "row" },
  { label: "报警指标", props: "alarmIndex", type: "row" },
  { label: "当前报警等级", props: "currentAlarmLevel", type: "row" },
  { label: "最高报警等级", props: "maxAlarmLevel", type: "row" },
  { label: "报警值", props: "alarmValue", unit: "m", type: "row" },
  { label: "报警状态", props: "alarmStatus", type: "row" },
  { label: "报警时间", props: "alarmTime", type: "row" }
];

// 供热管线详情
export const heating_pipelineConfig = [
  { label: "管段编码", props: "pipelineCode"},
  { label: "管段类型", props: "pipelineTypeName"},
  { label: "管径", props: "pipeDiameter", unit: "mm"},
  { label: "管段长度", props: "pipeLength", unit: "m"},
  { label: "管段材质", props: "materialName"},
  { label: "所属单位", props: "managementUnitName"},
  { label: "所属区域", props: "address", type: "row" },
  { label: "所在道路", props: "roadName", type: "row" }
];

// 供热窨井详情
export const heating_yinjingConfig = [
  { label: "窨井编码", props: "wellCode"},
  { label: "井深", props: "wellDepth", unit: "m"},
  { label: "所在道路", props: "roadName"},
  { label: "窨井形状", props: "wellShapeName"},
  { label: "窨井材质", props: "wellMaterialName"},
  { label: "井盖尺寸", props: "wellSize"},
  { label: "井室直径", props: "wellRoomDiameter", unit: "m"},
  { label: "埋深", props: "buriedDepth", unit: "m"},
  { label: "关联管线", props: "pipelineId"},
  { label: "权属单位", props: "managementUnitName"},
  { label: "建设时间", props: "constructionYear"},
  { label: "位置", props: "address"}
];

// 供热企业详情
export const heating_companyConfig = [
  { label: "企业名称", props: "enterpriseName"},
  { label: "热源信息", props: "factoryName"},
  { label: "额定负荷", props: "ratedLoad"},
  { label: "供热建筑面积", props: "heatBuildingArea", unit: "万m²"},
  { label: "供热使用面积", props: "heatUseArea", unit: "万m²"},
  { label: "供热范围", props: "heatingRange"},
  { label: "统一社会代码", props: "unifiedSocialCreditCode"},
  { label: "联系人", props: "contactPerson"},
  { label: "联系方式", props: "contactInfo"},
  { label: "企业地址", props: "address"}
];

// 供热管点详情
export const heating_pointConfig = [
  { label: "管点编码", props: "pointCode"},
  { label: "管点类型", props: "pointTypeName"},
  { label: "埋深", props: "depth", unit: "m"},
  { label: "高程", props: "elevation", unit: "m"},
  { label: "附属物", props: "attachedFacilities"},
  { label: "所在道路", props: "roadName"},
  { label: "关联管线", props: "connectedPipelineId"},
  { label: "关联管井", props: "connectedWellId"},
  { label: "权属单位", props: "managementUnitName"},
  { label: "安装时间", props: "installTime"},
  { label: "位置", props: "address", type: "row" }
];

// 热源详情
export const heating_sourceConfig = [
  { label: "热源厂名称", props: "factoryName"},
  { label: "热源厂编号", props: "factoryCode"},
  { label: "热源类型", props: "heatTypeName"},
  { label: "供热建筑面积", props: "heatBuildingArea", unit: "万m²"},
  { label: "供热使用面积", props: "heatUseArea", unit: "万m²"},
  { label: "占地面积", props: "floorArea"},
  { label: "供热能力", props: "heatCapacity"},
  { label: "热源出口", props: "heatExport"},
  { label: "锅炉信息", props: "boilerInfo"},
  { label: "投入使用时间", props: "investTime"},
  { label: "泵信息", props: "pumpInfo"},
  { label: "管理人员", props: "managementUser"},
  { label: "联系电话", props: "contactInfo"},
  { label: "权属单位", props: "ownershipUnitName"},
  { label: "所属位置", props: "address", type: "row" }
];

// 换热站详情
export const heating_exchangeStationConfig = [
  { label: "换热站名称", props: "stationName"},
  { label: "换热站编号", props: "stationCode"},
  { label: "热源", props: "factoryName"},
  { label: "供热使用面积", props: "heatUseArea", unit: "m²"},
  { label: "供热建筑面积", props: "heatBuildingArea", unit: "m²"},
  { label: "总换热能力", props: "exchangeCapacity"},
  { label: "所供户数", props: "connectedHouseholds"},
  { label: "投入使用时间", props: "investTime"},
  { label: "权属单位", props: "ownershipUnitName"},
  { label: "联系人", props: "contactPerson"},
  { label: "联系电话", props: "contactInfo"},
  { label: "所属位置", props: "address"}
];

// 供热用户详情
export const heating_userConfig = [
  { label: "用户名称", props: "userName"},
  { label: "用户编号", props: "userCode"},
  { label: "用户类型", props: "userTypeName"},
  { label: "所属建筑", props: "buildingName"},
  { label: "单元号", props: "unitNo"},
  { label: "楼层号", props: "floorNo"},
  { label: "门牌号", props: "houseNo"},
  { label: "所属机组", props: "unitName"},
  { label: "所属热源", props: "factoryName"},
  { label: "所属换热站", props: "stationName"},
  { label: "采暖面积", props: "heatingArea", unit: "m²"},
  { label: "采暖方式", props: "heatingType"},
  { label: "供热企业", props: "ownershipUnitName"},
  { label: "联系人", props: "contactPerson"},
  { label: "联系电话", props: "contactInfo"},
  { label: "所属位置", props: "address"}
];

// 供热危险源详情
export const heating_dangerConfig = [
  { label: "危险源编码", props: "dangerCode"},
  { label: "危险源名称", props: "dangerName"},
  { label: "建筑类型", props: "buildingTypeName"},
  { label: "是否重大危险源", props: "isMajor"},
  { label: "特征描述", props: "featureDesc", type: "row" },
  { label: "风险等级", props: "riskLevelName"},
  { label: "影响半径", props: "influenceRadius", unit: "km"},
  { label: "财产损失", props: "propertyLoss"},
  { label: "所属单位", props: "managementUnitName"},
  { label: "联系人", props: "contactUser"},
  { label: "联系电话", props: "contactInfo"},
  { label: "位置", props: "address", type: "row" }
];

// 供热防护目标详情
export const heating_protectionConfig = [
  { label: "防护目标编码", props: "protectCode"},
  { label: "防护目标名称", props: "protectName"},
  { label: "建筑类型", props: "buildingTypeName"},
  { label: "是否重点防护目标", props: "isMajor"},
  { label: "建筑面积", props: "buildingArea"},
  { label: "满负荷人数", props: "fullPeopleNumber"},
  { label: "建筑年份", props: "buildingYear"},
  { label: "所属单位", props: "managementUnitName"},
  { label: "联系人", props: "contactUser"},
  { label: "联系电话", props: "contactInfo"},
  { label: "位置", props: "address", type: "row" }
];

// 供热管线风险详情
export const heating_pipelineRiskConfig = [
  { label: "风险等级", props: "riskLevelName"},
  { label: "风险编号", props: "riskCode"},
  { label: "管线编码", props: "pipelineCode"},
  { label: "管材", props: "materialName"},
  { label: "管径", props: "pipeDiameter", unit: "mm"},
  { label: "管长", props: "pipeLength", unit: "m"},
  { label: "所在道路", props: "roadName"},
  { label: "建设时间", props: "constructionTime"},
  { label: "评估日期", props: "assessmentDate"},
  { label: "管控状态", props: "pipelineStatusName", type: "row" }
];

// 供热热源风险详情
export const heating_sourceRiskConfig = [
  { label: "风险等级", props: "riskLevelName"},
  { label: "风险编号", props: "riskCode"},
  { label: "名称", props: "factoryName"},
  { label: "供热建筑面积", props: "heatBuildingArea", unit: "m²"},
  { label: "位置", props: "address"},
  { label: "投入运行时间", props: "investTime"},
  { label: "评估日期", props: "assessmentDate"},
  { label: "管控状态", props: "factoryStatusName", type: "row" }
];

// 供热换热站风险详情
export const heating_exchangeStationRiskConfig = [
  { label: "风险等级", props: "riskLevelName"},
  { label: "风险编号", props: "riskCode"},
  { label: "名称", props: "stationName"},
  { label: "供热建筑面积", props: "heatBuildingArea", unit: "m²"},
  { label: "位置", props: "address"},
  { label: "投入运行时间", props: "investTime"},
  { label: "评估日期", props: "assessmentDate"},
  { label: "管控状态", props: "stationStatusName", type: "row" }
];

// 供热隐患详情
export const heating_hiddenDangerConfig = [
  { label: "隐患来源", props: "dangerSourceName", type: "row" },
  { label: "隐患描述", props: "dangerDesc", type: "row" },
  { label: "隐患类型", props: "dangerTypeName", type: "row" },
  { label: "隐患对象", props: "dangerObjectName", type: "row" },
  { label: "隐患等级", props: "dangerLevelName", type: "row" },
  { label: "隐患位置", props: "address", type: "row" },
  { label: "整改期限", props: "rectificationDeadline", type: "row" },
  { label: "责任人", props: "responsibleUserName", type: "row" },
  { label: "隐患图片", props: "fileList", isImg: true, fileProp: "picUrls", type: "row" }
];

// 传感器监测设备详情
export const heating_device_deviceConfig = [
  { label: "设备名称", props: "deviceName", type:"row"},
  { label: "设备编码", props: "indexCode", type:"row"},
  { label: "设备类型", props: "deviceTypeName", type:"row"},
  { label: "监测指标", props: "monitorIndexName", type:"row"},
  { label: "安装时间", props: "installTime", type:"row"},
  { label: "权属单位", props: "ownershipUnitName", type:"row"},
  { label: "设备状态", props: "onlineStatus", type:"row"},
  { label: "位置信息", props: "address", type:"row"},
];

// 报警详情
export const heating_alarm_detailConfig = [
  { label: "报警编号", props: "alarmCode", type: "row" },
  { label: "报警指标", props: "alarmIndex", type: "row" },
  { label: "当前报警等级", props: "currentAlarmLevel", type: "row" },
  { label: "最高报警等级", props: "maxAlarmLevel", type: "row" },
  { label: "报警值", props: "alarmValue", unit: "MPa", type: "row" },
  { label: "报警状态", props: "alarmStatus", type: "row" },
  { label: "报警时间", props: "alarmTime", type: "row" }
];

// 热源监测详情
export const heating_source_monitorConfig = [
  { label: "供水温度", props: "supplyWaterTemp", unit: "℃", type: "row" },
  { label: "回水温度", props: "returnWaterTemp", unit: "℃", type: "row" },
  { label: "供水压力", props: "supplyWaterPressure", unit: "MPa", type: "row" },
  { label: "回水压力", props: "returnWaterPressure", unit: "MPa", type: "row" }
];

// 换热站监测详情
export const heating_station_monitorConfig = [
  { label: "一网供水温度", props: "primarySupplyTemp", unit: "℃", type: "row" },
  { label: "一网回水温度", props: "primaryReturnTemp", unit: "℃", type: "row" },
  { label: "一网供水压力", props: "primarySupplyPressure", unit: "MPa", type: "row" },
  { label: "一网回水压力", props: "primaryReturnPressure", unit: "MPa", type: "row" }
];

// 建筑监测详情
export const heating_building_monitorConfig = [
  { label: "最高温度", props: "maxTemp", unit: "℃", type: "row" },
  { label: "平均温度", props: "avgTemp", unit: "℃", type: "row" },
  { label: "最低温度", props: "minTemp", unit: "℃", type: "row" },
  { label: "供热面积", props: "heatingArea", unit: "万m2", type: "row" },
  { label: "用户", props: "userCount", type: "row" }
];

// 监测设备详情
export const bridge_device_monitorConfig = [
  { label: "设备名称", props: "deviceName"},
  { label: "设备类型", props: "deviceTypeName"},
  { label: "设备编号", props: "indexCode"},
  { label: "监测指标", props: "monitorIndexName"},
  { label: "安装时间", props: "installTime"},
  { label: "权属单位", props: "ownershipUnitName"},
  { label: "设备状态", props: "onlineStatus"},
  { label: "位置信息", props: "address"}
];

// 报警详情
export const bridge_alarm_monitorConfig = [
  { label: "报警编号", props: "alarmCode", type: "row" },
  { label: "报警指标", props: "alarmIndex", type: "row" },
  { label: "当前报警等级", props: "currentAlarmLevel", type: "row" },
  { label: "最高报警等级", props: "maxAlarmLevel", type: "row" },
  { label: "报警值", props: "alarmValue", unit: "Mpa", type: "row" },
  { label: "报警状态", props: "alarmStatus", type: "row" },
  { label: "报警时间", props: "alarmTime", type: "row" }
];

// 桥梁病害详情
export const bridge_disease_detailConfig = [
  { label: "病害名称", props: "diseaseName"},
  { label: "桥梁名称", props: "bridgeName"},
  { label: "部件名称", props: "componentName"},
  { label: "构件名称", props: "structureName"},
  { label: "病害等级", props: "diseaseLevel"},
  { label: "缺损类型", props: "damageType"},
  { label: "缺损范围", props: "damageRange"},
  { label: "病害位置", props: "diseaseLocation"},
  { label: "病害位置坐标", props: "diseaseCoordinate"},
  { label: "病害来源", props: "diseaseSource", type: "row" },
  { label: "处理单位", props: "processingUnit"},
  { label: "处理人", props: "processor"},
  { label: "处理时间", props: "processingTime"},
  { label: "处理结果", props: "processingResult"},
  { label: "养护建议", props: "maintenanceAdvice"},
  { label: "病害照片", props: "diseasePhotos", type: "row" },
  { label: "处理照片", props: "processingPhotos", type: "row" },
  { label: "处理报告", props: "processingReport", type: "row" }
];

// 应急队伍详情
export const emergency_teamConfig = [
  { label: "救援队伍名称", props: "teamName"},
  { label: "级别", props: "teamLevelName"},
  { label: "救援队伍类型", props: "teamTypeName"},
  { label: "所属街道", props: "townName"},
  { label: "社区", props: "communityName"},
  { label: "人数", props: "teamPeopleNum"},
  { label: "成立时间", props: "establishTime"},
  { label: "管理单位", props: "managementUnitName", type: "row" },
  { label: "详细地址", props: "address", type: "row" },
  { label: "负责人", props: "responsibleUser"},
  { label: "负责人电话", props: "responsibleUserPhone"},
  { label: "联系人1", props: "contactUser1"},
  { label: "联系电话1", props: "contactInfo1"},
  { label: "联系人2", props: "contactUser2"},
  { label: "联系电话2", props: "contactInfo2"},
  { label: "座机", props: "landline", type: "row" }
];

// 应急物资详情
export const emergency_suppliesConfig = [
  { label: "物资名称", props: "suppliesName"},
  { label: "所属仓库", props: "storeName"},
  { label: "物质装备类型", props: "suppliesTypeName", type: "row" },
  { label: "具体地址", props: "address", type: "row" },
  { label: "国标物资名称", props: "gbSuppliesName"},
  { label: "国标物资代码", props: "gbSuppliesCode"},
  { label: "国标二级分类", props: "gbSubLevelTypeName"},
  { label: "二级分类代码", props: "gbSubLevelType"},
  { label: "规格型号", props: "suppliesModel"},
  { label: "数量", props: "suppliesNumber"},
  { label: "计量单位", props: "suppliesUnit"},
  { label: "灾害类型", props: "diseaseType"},
  { label: "所属区", props: "countyName"},
  { label: "所属街道", props: "townName"},
];

// 避难场所详情
export const emergency_shelterConfig = [
  { label: "避难场所名称", props: "shelterName", type: "row" },
  { label: "经度", props: "longitude"},
  { label: "纬度", props: "latitude"},
  { label: "详细地址", props: "address", type: "row" },
  { label: "联系人", props: "contactUser"},
  { label: "联系电话", props: "contactInfo"},
  { label: "可容纳人数", props: "shelterCapacity"},
  { label: "当前状态", props: "shelterStatusName"},
  { label: "所属区", props: "countyName"},
  { label: "所属街道", props: "townName"},
  { label: "固定电话", props: "landline"},
  { label: "面积", props: "shelterArea", unit: "㎡", },
  { label: "区值班室电话", props: "dutyPhone"},
  { label: "已容纳人数", props: "currentOccupancy"},
  { label: "登记使用情况", props: "occupancyRegistry", type: "row" },
  { label: "服务范围", props: "serveRange", type: "row" },
  { label: "备注", props: "remarks", type: "row" }
];

// 救援人员详情
export const emergency_personnelConfig = [
  { label: "人员编号", props: "respondersCode"},
  { label: "人员名称", props: "respondersName"},
  { label: "队伍名称", props: "teamName"},
  { label: "职务", props: "respondersPosition"},
  { label: "专业特长", props: "respondersExpertise"},
  { label: "联系电话", props: "contactInfo"},
  { label: "常驻地址", props: "address"},
];

// 医疗机构详情
export const medical_institutionConfig = [
  { label: "机构名称", props: "hospitalName"},
  { label: "机构类型", props: "hospitalTypeName"},
  { label: "运营性质", props: "operationTypeName"},
  { label: "所属区域", props: "countyName"},
  { label: "详细地址", props: "address"},
  { label: "床位数量", props: "bedNumber"},
  { label: "持证医师数量", props: "licensedPhysiciansNumber"},
  { label: "联系人", props: "contactUser"},
  { label: "联系电话", props: "contactInfo"},
  { label: "机构描述", props: "hospitalDesc", type: "row" }
];

// 应急仓库详情
export const emergency_warehouseConfig = [
  { label: "仓库名称", props: "storeName"},
  { label: "仓库类型", props: "storeTypeName"},
  { label: "仓库规模", props: "storeScaleName"},
  { label: "所属区域", props: "countyName"},
  { label: "详细地址", props: "address", type: "row"},
  { label: "主管单位", props: "managementUnitName"},
  { label: "值班电话", props: "dutyPhone"},
  { label: "投入使用时间", props: "investTime"},
  { label: "面积", props: "storeArea",  unit: "㎡"},
  { label: "联系人", props: "contactUser"},
  { label: "联系人电话", props: "contactInfo"},
  { label: "负责人", props: "responsibleUser"},
  { label: "负责人电话", props: "responsiblePhone"}
];

// 综合隐患详情
export const com_hiddenDangerConfig = [
  { label: "隐患来源", props: "sourceTypeName", type: "row" },
  { label: "隐患描述", props: "dangerDesc", type: "row" },
  { label: "隐患类型", props: "dangerTypeName", type: "row" },
  { label: "隐患对象", props: "dangerObjectName", type: "row" },
  { label: "隐患等级", props: "dangerLevelName", type: "row" },
  { label: "隐患位置", props: "address", type: "row" },
  { label: "整改期限", props: "rectificationDeadline", type: "row" },
  { label: "责任人", props: "responsibleUserName", type: "row" },
  { label: "隐患图片", props: "fileList", isImg: true, fileProp: "picUrls", type: "row" }
];

//综合预警信息详情
export const com_warningInfoConfig = [
  { label: "所属行业", props: "relatedBusinessName"},
  { label: "预警分类", props: "warningTypeName"},
  { label: "预警标题", props: "warningTitle"},
  { label: "预警级别", props: "warningLevelName"},
  { label: "预警描述", props: "warningDesc", type: "row" },
  { label: "预警来源", props: "warningSource"},
  { label: "报警源", props: "alarmSource"},
  { label: "报警源描述", props: "alarmSourceDescription"},
  { label: "地理位置", props: "warningLocation", type: "row" },
  { label: "发布单位", props: "publishUnitName"},
  { label: "发布时间", props: "publishTime"},
  { label: "相关附件", props: "fileList", isFile: true, fileProp: "fileUrls", type: "row" },
  { label: "处置单位", props: "dealUnitAndUserList-x", type: "row" },
  { label: "推送方式", props: "pushMethod", type: "row" }
];

// 所有地图撒点要素弹框配置信息
export const popupConfigInfo = {
  high_pressure_gas_pipeline: gas_guanxianConfig, // 高压管线
  mid_pressure_gas_pipeline: gas_guanxianConfig, // 中压管线
  low_pressure_gas_pipeline: gas_guanxianConfig, // 低压管线
  gas_station: gas_changzhanConfig, // 场站
  gas_pipeline_point: gas_guandianConfig, // 管点
  gas_well: gas_yinjingConfig, // 窨井
  gas_dangerous_source: gas_weixianyuanConfig, // 危险源
  gas_protection_target: gas_fanghumubiaoConfig, // 防护目标
  gas_pipeline_risk1: gas_guanxianRiskConfig, // 重大风险
  gas_pipeline_risk2: gas_guanxianRiskConfig, // 较大风险
  gas_pipeline_risk3: gas_guanxianRiskConfig, // 一般风险
  gas_pipeline_risk4: gas_guanxianRiskConfig, // 低风险
  gas_station_risk1: gas_changzhanRiskConfig, // 重大风险
  gas_station_risk2: gas_changzhanRiskConfig, // 较大风险
  gas_station_risk3: gas_changzhanRiskConfig, // 一般风险
  gas_station_risk4: gas_changzhanRiskConfig, // 低风险
  gas_flowmeter: gas_deviceInfoConfig, // 流量监测
  gas_manometer: gas_deviceInfoConfig, // 压力监测
  gas_combustible: gas_deviceInfoConfig, // 可燃气体监测
  gas_temperature: gas_deviceInfoConfig, // 温度监测
  gas_manhole_cover: gas_deviceInfoConfig, // 井盖监测
  gas_video: null, // 视频监控
  gas_alarm1: gas_deviceInfoConfig, // 一级报警
  gas_alarm2: gas_deviceInfoConfig, // 二级报警
  gas_alarm3: gas_deviceInfoConfig, // 三级报警
  drainage_rain_pipeline: drainage_guanxianConfig, // 雨水管线
  drainage_sewage_pipeline: drainage_guanxianConfig, // 污水管线
  drainage_rainAndSewage_pipeline: drainage_guanxianConfig, // 雨污合流管线
  drainage_pump_station: drainage_pumpConfig, // 泵站
  drainage_sewage_works: drainage_sewageConfig, // 污水厂
  drainage_water_outlet: drainage_outletConfig, // 排水口
  drainage_pipeline_point: drainage_guandianConfig, // 管点
  drainage_well: drainage_yinjingConfig, // 窨井
  drainage_flooding_point: drainage_waterloggingConfig, // 易涝点
  drainage_dangerous_source: drainage_dangerConfig, // 危险源
  drainage_protection_target: drainage_protectionConfig, // 防护目标
  drainage_pipeline_risk1: drainage_pipelineRiskConfig, // 重大风险
  drainage_pipeline_risk2: drainage_pipelineRiskConfig, // 较大风险
  drainage_pipeline_risk3: drainage_pipelineRiskConfig, // 一般风险
  drainage_pipeline_risk4: drainage_pipelineRiskConfig, // 低风险
  drainage_sewage_risk1: drainage_sewageRiskConfig, // 重大风险
  drainage_sewage_risk2: drainage_sewageRiskConfig, // 较大风险
  drainage_sewage_risk3: drainage_sewageRiskConfig, // 一般风险
  drainage_sewage_risk4: drainage_sewageRiskConfig, // 低风险
  drainage_pump_risk1: drainage_pumpRiskConfig, // 重大风险
  drainage_pump_risk2: drainage_pumpRiskConfig, // 较大风险
  drainage_pump_risk3: drainage_pumpRiskConfig, // 一般风险
  drainage_pump_risk4: drainage_pumpRiskConfig, // 低风险
  drainage_hidden_risk1: drainage_hiddenDangerConfig, // 重大隐患
  drainage_hidden_risk2: drainage_hiddenDangerConfig, // 较大隐患
  drainage_hidden_risk3: drainage_hiddenDangerConfig, // 一般隐患
  drainage_level: drainage_monitoringDeviceConfig, // 液位计
  drainage_flowmeter: drainage_monitoringDeviceConfig, // 流量计
  drainage_rain: drainage_monitoringDeviceConfig, // 雨量计
  drainage_water_quality: drainage_monitoringDeviceConfig, // 水质监测仪
  drainage_combustible: drainage_monitoringDeviceConfig, // 可燃气体监测仪
  drainage_manhole_cover: drainage_monitoringDeviceConfig, // 井盖监测
  drainage_video: null, // 视频监控
  drainage_alarm1: drainage_alarmConfig, // 一级报警
  drainage_alarm2: drainage_alarmConfig, // 二级报警
  drainage_alarm3: drainage_alarmConfig, // 三级报警
  heating_pipeline_1: heating_pipelineConfig, // 一次网
  heating_pipeline_2: heating_pipelineConfig, // 二次网
  heating_pipeline_point: heating_pointConfig, // 管点
  heating_well: heating_yinjingConfig, // 窨井
  heating_enterprise: heating_companyConfig, // 供热企业
  heating_source_works: heating_sourceConfig, // 热源厂
  heating_station: heating_exchangeStationConfig, // 换热站
  heating_user: heating_userConfig, // 供热用户
  heating_dangerous_source: heating_dangerConfig, // 危险源
  heating_protection_target: heating_protectionConfig, // 防护目标
  heating_pipeline_risk1: heating_pipelineRiskConfig, // 重大风险
  heating_pipeline_risk2: heating_pipelineRiskConfig, // 较大风险
  heating_pipeline_risk3: heating_pipelineRiskConfig, // 一般风险
  heating_pipeline_risk4: heating_pipelineRiskConfig, // 低风险
  heating_source_risk1: heating_sourceRiskConfig, // 重大风险
  heating_source_risk2: heating_sourceRiskConfig, // 较大风险
  heating_source_risk3: heating_sourceRiskConfig, // 一般风险
  heating_source_risk4: heating_sourceRiskConfig, // 低风险
  heating_station_risk1: heating_exchangeStationRiskConfig, // 重大风险
  heating_station_risk2: heating_exchangeStationRiskConfig, // 较大风险
  heating_station_risk3: heating_exchangeStationRiskConfig, // 一般风险
  heating_station_risk4: heating_exchangeStationRiskConfig, // 低风险
  heating_hidden_risk1: heating_hiddenDangerConfig, // 重大隐患
  heating_hidden_risk2: heating_hiddenDangerConfig, // 较大隐患
  heating_hidden_risk3: heating_hiddenDangerConfig, // 一般隐患
  heating_level: heating_device_deviceConfig, // 液位计
  heating_flowmeter: heating_device_deviceConfig, // 流量计
  heating_rain: heating_device_deviceConfig, // 雨量计
  heating_water_quality: heating_device_deviceConfig, // 水质监测仪
  heating_combustible: heating_device_deviceConfig, // 可燃气体监测仪
  heating_temperature: heating_device_deviceConfig, // 温度监测仪
  heating_manhole_cover: heating_device_deviceConfig, // 井盖监测
  heating_video: null, // 视频监控
  heating_alarm1: heating_alarm_detailConfig, // 一级报警
  heating_alarm2: heating_alarm_detailConfig, // 二级报警
  heating_alarm3: heating_alarm_detailConfig, // 三级报警
  bridge_info: null, // 桥梁
  bridge_safety_rating: null, // 安全评分
  bridge_wind_speed: bridge_device_monitorConfig, // 风速仪
  bridge_temperature: bridge_device_monitorConfig, // 温度传感器
  bridge_static_level: bridge_device_monitorConfig, // 静力水平仪
  bridge_humidity: bridge_device_monitorConfig, // 湿度传感器
  bridge_vibration: bridge_device_monitorConfig, // 三向加速度传感器
  bridge_dynamic_weight: bridge_device_monitorConfig, // 动态称重系统
  bridge_displacement: bridge_device_monitorConfig, // 位移计
  bridge_tilt: bridge_device_monitorConfig, // 倾角传感器
  bridge_deflection: bridge_device_monitorConfig, // 挠度仪
  bridge_strain: bridge_device_monitorConfig, // 应变传感器
  bridge_load_cell: bridge_device_monitorConfig, // 测力支座
  bridge_acceleration: bridge_device_monitorConfig, // 加速度传感器
  bridge_crack: bridge_device_monitorConfig, // 车船撞击传感器
  bridge_tilt_alarm: bridge_device_monitorConfig, // 梁体偏位报警器
  bridge_crack_sensor: bridge_device_monitorConfig, // 裂缝传感器
  bridge_vibration_sensor: bridge_device_monitorConfig, // 测振仪
  bridge_video: null, // 视频监控
  com_warning_level1: com_warningInfoConfig, //  综合预警级别1
  com_warning_level2: com_warningInfoConfig, //  综合预警级别2
  com_warning_level3: com_warningInfoConfig, //  综合预警级别3
  com_gas_event: null, // 燃气事件
  com_drainage_event: null, // 排水事件
  com_heating_event: null, // 供热事件
  com_bridge_event: null, // 桥梁事件
  com_shelter: emergency_shelterConfig, // 避难场所
  com_emergency_team: emergency_teamConfig, // 应急队伍
  com_emergency_material: emergency_suppliesConfig, // 应急物资
  com_rescue_personnel: emergency_personnelConfig, // 救援人员
  com_medical_institution: medical_institutionConfig, // 医疗机构
  com_emergency_warehouse: emergency_warehouseConfig, // 应急仓库
  com_gas_hidden: com_hiddenDangerConfig, // 燃气隐患
  com_drainage_hidden: com_hiddenDangerConfig, // 排水隐患
  com_heating_hidden: com_hiddenDangerConfig, // 供热隐患
  com_bridge_hidden: com_hiddenDangerConfig, // 桥梁隐患
};
