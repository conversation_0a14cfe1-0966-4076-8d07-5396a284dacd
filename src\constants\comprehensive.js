// 重要程度选项
export const IMPORTANT_LEVEL_OPTIONS = [
  { label: '重要', value: '7000201' },
  { label: '一般', value: '7000202' }
]

// 主体分类选项
export const MAIN_TYPE_OPTIONS = [
  { label: '活动通知', value: '7000101' },
  { label: '通知公告', value: '7000102' }
]

// 公文种类选项
export const DOCUMENT_TYPE_OPTIONS = [
  { label: '通知', value: '通知' },
  { label: '公告', value: '公告' },
  { label: '通报', value: '通报' },
  { label: '报告', value: '报告' },
  { label: '请示', value: '请示' },
  { label: '批复', value: '批复' },
  { label: '意见', value: '意见' },
  { label: '函', value: '函' }
]

// 工程进展状态选项
export const PROJECT_STATUS_OPTIONS = [
  { label: '未开始', value: '7000401' },
  { label: '进行中', value: '7000402' },
  { label: '已完成', value: '7000403' }
]

// 项目类型选项
export const PROJECT_TYPE_OPTIONS = [
  { label: '燃气项目', value: '7000301' },
  { label: '雨水项目', value: '7000302' },
  { label: '污水项目', value: '7000303' },
  { label: '供热项目', value: '7000304' },
  { label: '桥梁项目', value: '7000305' },
  { label: '其他', value: '7000306' }
]

// 关联行业选项
export const RELATED_BUSINESS_OPTIONS = [
  { label: '燃气', value: 7000501 },
  { label: '排水', value: 7000502 },
  { label: '供热', value: 7000503 },
  { label: '桥梁', value: 7000504 }
]

// 设备在线状态选项
export const DEVICE_ONLINE_STATUS_OPTIONS = [
  { label: '离线', value: 0 },
  { label: '在线', value: 1 }
]

// 数据来源选项
export const PROJECT_SOURCE_OPTIONS = [
  { label: '系统对接', value: '系统对接' },
  { label: '手动新增', value: '手动新增' }
]

// 督查督办 - 所属行业选项
export const SUPERVISE_BUSINESS_OPTIONS = [
  { label: '燃气', value: 7000501 },
  { label: '排水', value: 7000502 },
  { label: '供热', value: 7000503 },  
  { label: '桥梁', value: 7000504 }
]

// 督查督办 - 事项状态选项
export const TASK_STATUS_OPTIONS = [
  { label: '待下发', value: 7000601 },
  { label: '待处理', value: 7000602 },
  { label: '处理中', value: 7000603 },
  { label: '已办结', value: 7000604 },
  { label: '已超期', value: 7000605 }
]

// 督查督办 - 督办状态选项
export const SUPERVISE_STATUS_OPTIONS = [
  { label: '新增', value: 7000701 },
  { label: '下发', value: 7000702 },
  { label: '处理', value: 7000703 }
]

// 督查督办 - 处置状态选项
export const HANDLE_STATUS_OPTIONS = [
  { label: '处理中', value: 7000801 },
  { label: '已办结', value: 7000802 }
]

// 公众报警相关常量

// 报警来源选项
export const ALARM_SOURCE_OPTIONS = [
  { label: '政府热线', value: 7001301 },
  { label: '公安110热线', value: 7001302 },
  { label: '消防119热线', value: 7001303 },
  { label: '公众投诉', value: 7001304 },
  { label: '其他', value: 7001305 }
]

// 报警状态选项
export const ALARM_STATUS_OPTIONS = [
  { label: '待处置', value: 7001501 },
  { label: '处置中', value: 7001502 },
  { label: '已处置', value: 7001503 }
]

// 紧急程度选项
export const URGENT_LEVEL_OPTIONS = [
  { label: '特别重大（Ⅰ级）', value: 7001401 },
  { label: '重大（Ⅱ级）', value: 7001402 },
  { label: '较大（Ⅲ级）', value: 7001403 },
  { label: '一般（Ⅳ级）', value: 7001404 }
]

// 处置状态选项
export const HANDLE_STATUS_OPTIONS_ALARM = [
  { label: '处置中', value: 7001701 },
  { label: '已处置', value: 7001702 }
]

// 专家相关常量定义

// 申请状态选项
export const APPLY_STATUS_OPTIONS = [
  { label: '未回复', value: 7001001 },
  { label: '同意申请', value: 7001002 },
  { label: '驳回申请', value: 7001003 }
]

// 咨询状态选项
export const CONSULT_STATUS_OPTIONS = [
  { label: '未回复', value: 7001101 },
  { label: '已回复', value: 7001102 }
]

// 申请结果选项（用于回复时选择）
export const APPLY_RESULT_OPTIONS = [
  { label: '同意申请', value: 7001002 },
  { label: '驳回申请', value: 7001003 }
]

// 应急事件相关常量定义

// 事件分级选项
export const EVENT_LEVEL_OPTIONS = [
  { label: '特别重大', value: 7001901 },
  { label: '重大', value: 7001902 },
  { label: '较大', value: 7001903 },
  { label: '一般', value: 7001904 }
]

// 事件来源选项
export const EVENT_SOURCE_OPTIONS = [
  { label: '燃气', value: 7009001 },
  { label: '排水', value: 7009002 },
  { label: '供热', value: 7009003 },
  { label: '桥梁', value: 7009004 },
  { label: '系统上报', value: 7009005 }
]

// 事件处置状态选项
export const EVENT_STATUS_OPTIONS = [
  { label: '未处置', value: 7002001 },
  { label: '处置中', value: 7002002 },
  { label: '已处置', value: 7002003 }
]

// 事件分类选项
export const EVENT_TYPE_OPTIONS = [
  { label: '燃气爆炸', value: 7001801 },
  { label: '户内燃气泄露', value: 7001802 },
  { label: '燃气火灾', value: 7001803 },
  { label: '户外燃气泄露', value: 7001804 },
  { label: '供热管网爆管停水', value: 7001805 },
  { label: '排水内涝', value: 7001806 },
  { label: '桥梁倒塌', value: 7001807 },
  { label: '其他', value: 7001808 }
]

// 是否人员伤亡选项
export const IS_CASUALTY_OPTIONS = [
  { label: '是', value: true },
  { label: '否', value: false }
]

// 隐患排查治理常量定义

// 隐患等级选项
export const HIDDEN_DANGER_LEVEL_OPTIONS = [
  { label: '重大隐患', value: 7002701 },
  { label: '较大隐患', value: 7002702 },
  { label: '一般隐患', value: 7002703 }
]

// 隐患状态选项
export const HIDDEN_DANGER_STATUS_OPTIONS = [
  { label: '待整改', value: 7003301 },
  { label: '整改中', value: 7003302 },
  { label: '待复查', value: 7003303 },
  { label: '已整改', value: 7003304 }
]

// 隐患对象编码选项
export const HIDDEN_DANGER_OBJECT_OPTIONS = [
  { label: '管线', value: 7003201 },
  { label: '排水口', value: 7003202 },
  { label: '污水厂', value: 7003203 },
  { label: '泵站', value: 7003204 },
  { label: '窨井', value: 7003205 },
  { label: '易涝点', value: 7003206 },
  { label: '设备', value: 7003207 },
  { label: '热源', value: 7003208 },
  { label: '换热站', value: 7003209 }
]

// 隐患类型选项
export const HIDDEN_DANGER_TYPE_OPTIONS = [
  { label: '管道破裂隐患', value: 7002601 },
  { label: '管道堵塞隐患', value: 7002602 },
  { label: '水质污染隐患', value: 7002603 },
  { label: '排水不足/泄洪隐患', value: 7002604 },
  { label: '管道腐蚀隐患', value: 7002605 },
  { label: '地质灾害隐患', value: 7002606 },
  { label: '管道泄漏隐患', value: 7002607 },
  { label: '管道老化隐患', value: 7002608 },
  { label: '设备设施隐患', value: 7002609 },
  { label: '其他', value: 7002610 }
]

// 隐患来源选项
export const HIDDEN_DANGER_SOURCE_OPTIONS = [
  { label: '燃气专项', value: 7002501 },
  { label: '排水专项', value: 7002502 },
  { label: '供热专项', value: 7002503 },
  { label: '桥梁专项', value: 7002504 },
  { label: '人工上报', value: 7002505 }
]

// 整改状态选项
export const RECTIFICATION_STATUS_OPTIONS = [
  { label: '整改中', value: 7003501 },
  { label: '整改完成', value: 7003502 }
]

// 复查结果选项
export const REVIEW_RESULT_OPTIONS = [
  { label: '通过', value: 1 },
  { label: '驳回', value: 0 }
]

// 是否按期选项
export const ON_SCHEDULE_OPTIONS = [
  { label: '是', value: true },
  { label: '否', value: false }
]

// 应急预案管理常量定义

// 预案级别选项
export const SCHEME_LEVEL_OPTIONS = [
  { label: '国家级预案', value: 7002801 },
  { label: '省级预案', value: 7002802 },
  { label: '地方预案', value: 7002803 },
  { label: '专项预案', value: 7002804 }
]

// 知识类别选项（预案类型）
export const SCHEME_TYPE_OPTIONS = [
  { label: '综合预案', value: 7002901 },
  { label: '专项预案', value: 7002902 },
  { label: '现场处置预案', value: 7002903 },
  { label: '其他预案', value: 7002904 }
]

// 主题分类选项
export const THEME_TYPE_OPTIONS = [
  { label: '城市生命线', value: 7003001 },
  { label: '自然灾害', value: 7003002 },
  { label: '公共安全', value: 7003003 },
  { label: '地质灾害', value: 7003004 },
  { label: '生产安全', value: 7003005 }
]

// 失效状态选项
export const SCHEME_STATUS_OPTIONS = [
  { label: '有效', value: 7003101 },
  { label: '失效', value: 7003102 }
]

// 匹配预警（事件）类型选项（补充已有的WARNING_TYPE_OPTIONS）
export const EMERGENCY_WARNING_TYPE_OPTIONS = [
  { label: '管网爆管预警', value: 7002101 },
  { label: '泄露量预警', value: 7002102 },
  { label: '暴雨洪涝预警', value: 7002103 },
  { label: '排水管网淤积预警', value: 7002104 },
  { label: '排水管网溢流预警', value: 7002105 },
  { label: '桥梁结构损伤预警', value: 7002106 },
  { label: '极端天气预警', value: 7002107 },
  { label: '其他预警', value: 7002108 }
]

// 应急资源管理常量定义

// 应急队伍级别选项
export const EMERGENCY_TEAM_LEVEL_OPTIONS = [
  { label: '市级', value: 6006101 },
  { label: '区级', value: 6006102 }
]

// 应急队伍类型选项
export const EMERGENCY_TEAM_TYPE_OPTIONS = [
  { label: '综合性消防救援队伍', value: 6006001 },
  { label: '社会救援力量队伍', value: 6006002 },
  { label: '其他各类专业救援队伍', value: 6006003 },
  { label: '城市排涝抢险队伍', value: 6006004 },
  { label: '水利抢险救援队伍', value: 6006005 },
  { label: '工程抢险救援队伍', value: 6006006 },
  { label: '通信电力抢险救援队伍', value: 6006007 },
  { label: '人防抢险救援队伍', value: 6006008 },
  { label: '交通抢险救援队伍', value: 6006009 },
  { label: '乡村抢险突击队', value: 6006010 }
]

// 物资装备类型选项
export const EMERGENCY_SUPPLIES_TYPE_OPTIONS = [
  { label: '通信保障', value: 6006201 },
  { label: '运输保障', value: 6006202 },
  { label: '医疗卫生', value: 6006203 }
]

// 避难场所当前状态选项
export const EMERGENCY_SHELTER_STATUS_OPTIONS = [
  { label: '关闭', value: 6006301 },
  { label: '开启', value: 6006302 }
]

// 医疗机构类型选项
export const EMERGENCY_HOSPITAL_TYPE_OPTIONS = [
  { label: '公立医院', value: 6006401 },
  { label: '民营医院', value: 6006402 },
  { label: '社区卫生服务中心(站)', value: 6006403 },
  { label: '乡镇卫生院', value: 6006404 },
  { label: '村卫生室', value: 6006405 },
  { label: '诊所(医务室)', value: 6006406 },
  { label: '疾病预防控制中心', value: 6006407 },
  { label: '卫生监督所（中心）', value: 6006408 },
  { label: '疗养院', value: 6006409 },
  { label: '门诊部', value: 6006410 },
  { label: '急救站', value: 6006411 }
]

// 医疗机构运营性质选项
export const EMERGENCY_OPERATION_TYPE_OPTIONS = [
  { label: '公立', value: 6006501 },
  { label: '私立', value: 6006502 }
]

// 仓库规模选项
export const EMERGENCY_STORE_SCALE_OPTIONS = [
  { label: '大型', value: 6006701 },
  { label: '中型', value: 6006702 },
  { label: '小型', value: 6006703 }
]

// 仓库类型选项
export const EMERGENCY_STORE_TYPE_OPTIONS = [
  { label: '防汛仓库', value: 6006601 },
  { label: '应急仓库', value: 6006602 }
]

// 巡检管理相关常量定义

// 巡检频次选项
export const INSPECTION_FREQUENCY_OPTIONS = [
  { label: '每天一次', value: 7003701 },
  { label: '每周一次', value: 7003702 },
  { label: '每月一次', value: 7003703 },
  { label: '每月两次', value: 7003704 },
  { label: '每季度一次', value: 7003705 },
  { label: '每半年一次', value: 7003706 }
]

// 巡检任务状态选项
export const INSPECTION_TASK_STATUS_OPTIONS = [
  { label: '未开始', value: 7003801 },
  { label: '进行中', value: 7003802 },
  { label: '已结束', value: 7003803 }
]

// 巡检工单状态选项
export const INSPECTION_RECORD_STATUS_OPTIONS = [
  { label: '未开始', value: 7003901 },
  { label: '进行中', value: 7003902 },
  { label: '已完成', value: 7003903 }
]

// 是否存在隐患选项
export const EXIST_DANGER_OPTIONS = [
  { label: '是', value: true },
  { label: '否', value: false }
]

// 设备维修结果选项
export const REPAIR_RESULT_OPTIONS = [
  { label: '未完成', value: 7003601 },
  { label: '已完成', value: 7003602 }
]

// 风险等级选项
export const RISK_LEVEL_OPTIONS = [
  { label: '重大风险', value: 7004001 },
  { label: '较大风险', value: 7004002 },
  { label: '一般风险', value: 7004003 },
  { label: '低风险', value: 7004004 }
]