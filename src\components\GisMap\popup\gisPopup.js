import { defineAsyncComponent } from "vue";

const demoPopup = defineAsyncComponent(() => import("./demoPopup.vue"));

const NormalDetailPopup = defineAsyncComponent(() => import("./NormalDetail/index.vue"));
const DeviceDetailPopup = defineAsyncComponent(() => import("./DeviceDetail/index.vue"));
const VideoDetailPopup = defineAsyncComponent(() => import("./VideoDetail/index.vue"));
const BridgeDetailPopup = defineAsyncComponent(() => import("./BridgeDetail/index.vue"));
const EmergencyEventDetailPopup = defineAsyncComponent(() => import("./EmergencyEventDetail/index.vue"));

const MisNormalDetailPopup = defineAsyncComponent(() => import("./MisNormalDetail/index.vue"));
const MisVideoDetailPopup = defineAsyncComponent(() => import("./MisVideoDetail/index.vue"));

/**
 * 弹窗组件(目前已接入的弹窗)
 */
export const gisPopups= {
  normal: demoPopup,
  high_pressure_gas_pipeline: NormalDetailPopup, // 高压燃气管道
  mid_pressure_gas_pipeline: NormalDetailPopup, // 中压燃气管道
  low_pressure_gas_pipeline: NormalDetailPopup, // 低压燃气管道
  gas_pipeline_point: NormalDetailPopup, // 燃气管道点
  gas_station: NormalDetailPopup, // 燃气场站
  gas_well: NormalDetailPopup, // 燃气窨井
  gas_dangerous_source: NormalDetailPopup, // 燃气危险源
  gas_protection_target: NormalDetailPopup, // 燃气防护目标
  gas_combustible: DeviceDetailPopup, // 燃气可燃气体监测仪
  gas_manhole_cover: DeviceDetailPopup, // 燃气井盖监测
  gas_video: VideoDetailPopup, // 燃气视频
  gas_pipeline_risk1: NormalDetailPopup, // 燃气管道风险
  gas_pipeline_risk2: NormalDetailPopup, // 燃气管道风险
  gas_pipeline_risk3: NormalDetailPopup, // 燃气管道风险
  gas_pipeline_risk4: NormalDetailPopup, // 燃气管道风险
  gas_station_risk1: NormalDetailPopup, // 燃气场站风险
  gas_station_risk2: NormalDetailPopup, // 燃气场站风险
  gas_station_risk3: NormalDetailPopup, // 燃气场站风险
  gas_station_risk4: NormalDetailPopup, // 燃气场站风险
  drainage_rain_pipeline: NormalDetailPopup, // 雨水管道
  drainage_sewage_pipeline: NormalDetailPopup, // 污水管道
  drainage_rainAndSewage_pipeline: NormalDetailPopup, // 雨污合流管道
  drainage_pump_station: NormalDetailPopup, // 排水泵站
  drainage_sewage_works: NormalDetailPopup, // 污水处理厂
  drainage_water_outlet: NormalDetailPopup, // 排水口
  drainage_pipeline_point: NormalDetailPopup, // 排水管点
  drainage_well: NormalDetailPopup, // 排水窨井
  drainage_flooding_point: NormalDetailPopup, // 排水易涝点
  drainage_dangerous_source: NormalDetailPopup, // 排水危险源
  drainage_protection_target: NormalDetailPopup, // 排水防护目标
  drainage_combustible: DeviceDetailPopup, // 排水可燃气体监测仪
  drainage_manhole_cover: DeviceDetailPopup, // 排水井盖监测
  drainage_level: DeviceDetailPopup, // 排水液位计
  drainage_water_quality: DeviceDetailPopup, // 排水水质监测仪
  drainage_video: VideoDetailPopup, // 排水视频
  drainage_pipeline_risk1: NormalDetailPopup, // 排水管道风险
  drainage_pipeline_risk2: NormalDetailPopup, // 排水管道风险
  drainage_pipeline_risk3: NormalDetailPopup, // 排水管道风险
  drainage_pipeline_risk4: NormalDetailPopup, // 排水管道风险
  drainage_sewage_risk1: NormalDetailPopup, // 污水处理厂风险
  drainage_sewage_risk2: NormalDetailPopup, // 污水处理厂风险
  drainage_sewage_risk3: NormalDetailPopup, // 污水处理厂风险
  drainage_sewage_risk4: NormalDetailPopup, // 污水处理厂风险
  drainage_pump_risk1: NormalDetailPopup, // 排水泵站风险
  drainage_pump_risk2: NormalDetailPopup, // 排水泵站风险
  drainage_pump_risk3: NormalDetailPopup, // 排水泵站风险
  drainage_pump_risk4: NormalDetailPopup, // 排水泵站风险
  drainage_hidden_risk1: NormalDetailPopup, // 排水隐患点风险
  drainage_hidden_risk2: NormalDetailPopup, // 排水隐患点风险
  drainage_hidden_risk3: NormalDetailPopup, // 排水隐患点风险
  heating_pipeline_1: NormalDetailPopup, // 供热管道
  heating_pipeline_2: NormalDetailPopup, // 供热管道
  heating_pipeline_point: NormalDetailPopup, // 供热管点
  heating_well: NormalDetailPopup, // 供热窨井
  heating_enterprise: NormalDetailPopup, // 供热企业
  heating_source_works: NormalDetailPopup, // 供热源厂
  heating_station: NormalDetailPopup, // 供热场站
  heating_user: NormalDetailPopup, // 供热用户
  heating_dangerous_source: NormalDetailPopup, // 供热危险源
  heating_protection_target: NormalDetailPopup, // 供热防护目标
  heating_combustible: DeviceDetailPopup, // 供热可燃气体监测仪
  heating_manhole_cover: DeviceDetailPopup, // 供热井盖监测
  heating_temperature: DeviceDetailPopup, // 供热温度监测仪
  heating_video: VideoDetailPopup, // 供热视频
  heating_pipeline_risk1: NormalDetailPopup, // 供热管道风险
  heating_pipeline_risk2: NormalDetailPopup, // 供热管道风险
  heating_pipeline_risk3: NormalDetailPopup, // 供热管道风险
  heating_pipeline_risk4: NormalDetailPopup, // 供热管道风险
  heating_source_risk1: NormalDetailPopup, // 供热源厂风险
  heating_source_risk2: NormalDetailPopup, // 供热源厂风险
  heating_source_risk3: NormalDetailPopup, // 供热源厂风险
  heating_source_risk4: NormalDetailPopup, // 供热源厂风险
  heating_station_risk1: NormalDetailPopup, // 供热场站风险
  heating_station_risk2: NormalDetailPopup, // 供热场站风险
  heating_station_risk3: NormalDetailPopup, // 供热场站风险
  heating_station_risk4: NormalDetailPopup, // 供热场站风险
  heating_hidden_risk1: NormalDetailPopup, // 供热隐患点风险
  heating_hidden_risk2: NormalDetailPopup, // 供热隐患点风险
  heating_hidden_risk3: NormalDetailPopup, // 供热隐患点风险
  bridge_info: BridgeDetailPopup, // 桥梁信息
  bridge_safety_rating: BridgeDetailPopup, // 桥梁安全等级
  bridge_temperature: DeviceDetailPopup, // 桥梁温度监测仪
  bridge_static_level: DeviceDetailPopup, // 桥梁静力水准仪
  bridge_displacement: DeviceDetailPopup, // 桥梁位移监测仪
  bridge_strain: DeviceDetailPopup, // 桥梁应变监测仪
  bridge_crack_sensor: DeviceDetailPopup, // 桥梁裂缝监测仪
  bridge_video: VideoDetailPopup, // 桥梁视频
  com_warning_level1: NormalDetailPopup, //  综合预警级别1
  com_warning_level2: NormalDetailPopup, //  综合预警级别2
  com_warning_level3: NormalDetailPopup, //  综合预警级别3
  com_gas_event: EmergencyEventDetailPopup, // 燃气事件
  com_drainage_event: EmergencyEventDetailPopup, // 排水事件
  com_heating_event: EmergencyEventDetailPopup, // 供热事件
  com_bridge_event: EmergencyEventDetailPopup, // 桥梁事件
  com_shelter: NormalDetailPopup, // 避难场所
  com_emergency_team: NormalDetailPopup, // 应急队伍
  com_emergency_material: NormalDetailPopup, // 应急物资
  com_rescue_personnel: NormalDetailPopup, // 救援人员
  com_medical_institution: NormalDetailPopup, // 医疗机构
  com_emergency_warehouse: NormalDetailPopup, // 应急仓库
  com_gas_hidden: NormalDetailPopup, // 燃气隐患
  com_drainage_hidden: NormalDetailPopup, // 排水隐患
  com_heating_hidden: NormalDetailPopup, // 供热隐患
  com_bridge_hidden: NormalDetailPopup, // 桥梁隐患
};

export const gisMisPopups= {
  normal: demoPopup,
  gas_pipeline_risk1: MisNormalDetailPopup, // 燃气管道风险
  gas_pipeline_risk2: MisNormalDetailPopup, // 燃气管道风险
  gas_pipeline_risk3: MisNormalDetailPopup, // 燃气管道风险
  gas_pipeline_risk4: MisNormalDetailPopup, // 燃气管道风险
  gas_station_risk1: MisNormalDetailPopup, // 燃气场站风险
  gas_station_risk2: MisNormalDetailPopup, // 燃气场站风险
  gas_station_risk3: MisNormalDetailPopup, // 燃气场站风险
  gas_station_risk4: MisNormalDetailPopup, // 燃气场站风险
  drainage_pipeline_risk1: MisNormalDetailPopup, // 排水管道风险
  drainage_pipeline_risk2: MisNormalDetailPopup, // 排水管道风险
  drainage_pipeline_risk3: MisNormalDetailPopup, // 排水管道风险
  drainage_pipeline_risk4: MisNormalDetailPopup, // 排水管道风险
  drainage_sewage_risk1: MisNormalDetailPopup, // 污水处理厂风险
  drainage_sewage_risk2: MisNormalDetailPopup, // 污水处理厂风险
  drainage_sewage_risk3: MisNormalDetailPopup, // 污水处理厂风险
  drainage_sewage_risk4: MisNormalDetailPopup, // 污水处理厂风险
  drainage_pump_risk1: MisNormalDetailPopup, // 排水泵站风险
  drainage_pump_risk2: MisNormalDetailPopup, // 排水泵站风险
  drainage_pump_risk3: MisNormalDetailPopup, // 排水泵站风险
  drainage_pump_risk4: MisNormalDetailPopup, // 排水泵站风险
  drainage_hidden_risk1: MisNormalDetailPopup, // 排水隐患点风险
  drainage_hidden_risk2: MisNormalDetailPopup, // 排水隐患点风险
  drainage_hidden_risk3: MisNormalDetailPopup, // 排水隐患点风险
  heating_pipeline_risk1: MisNormalDetailPopup, // 供热管道风险
  heating_pipeline_risk2: MisNormalDetailPopup, // 供热管道风险
  heating_pipeline_risk3: MisNormalDetailPopup, // 供热管道风险
  heating_pipeline_risk4: MisNormalDetailPopup, // 供热管道风险
  heating_source_risk1: MisNormalDetailPopup, // 供热源厂风险
  heating_source_risk2: MisNormalDetailPopup, // 供热源厂风险
  heating_source_risk3: MisNormalDetailPopup, // 供热源厂风险
  heating_source_risk4: MisNormalDetailPopup, // 供热源厂风险
  heating_station_risk1: MisNormalDetailPopup, // 供热场站风险
  heating_station_risk2: MisNormalDetailPopup, // 供热场站风险
  heating_station_risk3: MisNormalDetailPopup, // 供热场站风险
  heating_station_risk4: MisNormalDetailPopup, // 供热场站风险
  heating_hidden_risk1: MisNormalDetailPopup, // 供热隐患点风险
  heating_hidden_risk2: MisNormalDetailPopup, // 供热隐患点风险
  heating_hidden_risk3: MisNormalDetailPopup, // 供热隐患点风险
  heating_protection1: MisNormalDetailPopup, // 供热防护目标1
  heating_protection2: MisNormalDetailPopup, // 供热防护目标2
  gas_video0: MisVideoDetailPopup, // 燃气视频
  gas_video1: MisVideoDetailPopup, // 燃气视频
};
