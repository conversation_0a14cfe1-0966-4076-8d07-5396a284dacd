<template>
  <PanelBox title="桥梁监测">
    <div class="panel-content">
      <!-- 上部分：总体统计 -->
      <div class="stats-row">
        <div class="stat-item">
          <span class="stat-dot"><span class="stat-dot-inner"></span></span>
          <span class="stat-label">设备总数</span>
          <span class="stat-value-blue">{{ statsData.totalDevices }}</span>
          <span class="stat-unit">台</span>
        </div>
        <div class="stat-item">
          <span class="stat-dot"><span class="stat-dot-inner"></span></span>
          <span class="stat-label">设备在线率</span>
          <span class="stat-value-gradient">{{ statsData.onlineRate }}</span>
        </div>
      </div>
      
      <!-- 下部分：监测设备列表 -->
      <div class="monitoring-container">
        <div class="monitoring-items">
          <div class="monitor-item" v-for="(item, index) in monitoringData" :key="index">
            <div class="item-icon">
              <img :src="item.iconPath" alt="监测图标">
            </div>
            <div class="item-name">{{ item.name }}</div>
            <div class="item-stats">
              <span class="online-count">{{ item.online }}</span>
              <span class="count-divider">/</span>
              <span class="total-count">{{ item.total }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </PanelBox>
</template>

<script setup>
import PanelBox from '@/components/screen/PanelBox.vue'
import { ref, reactive, onMounted } from 'vue'
// 导入API方法
import { getBridgeMonitorStatistics } from '@/api/bridge'

// 导入图片
import wenshiduIcon from '@/assets/images/screen/briadge/wenshidu.png'
import qixiangIcon from '@/assets/images/screen/briadge/qixiang.png'
import naoduIcon from '@/assets/images/screen/briadge/naodu.png'
import yingliIcon from '@/assets/images/screen/briadge/yingli.png'
import liefengIcon from '@/assets/images/screen/briadge/liefeng.png'
import qinxieIcon from '@/assets/images/screen/briadge/qinxie.png'
import zhendongIcon from '@/assets/images/screen/briadge/zhendong.png'
import qiaomianIcon from '@/assets/images/screen/briadge/qiaomian.png'
import zhizuoIcon from '@/assets/images/screen/briadge/zhizuo.png'
import wenduIcon from '@/assets/images/screen/briadge/wendu.png'
import shipinIcon from '@/assets/images/screen/briadge/shipin.png'

// 统计数据
const statsData = reactive({
  totalDevices: 0,
  onlineRate: '0%'
})

// 监测设备数据
const monitoringData = ref([])

// 设备类型与图标映射
const deviceTypeIconMap = {
  'bridgeGxLf': liefengIcon, // 工讯裂缝计
  'bridgeZxWd': wenduIcon,   // 智性结构温度计
  'bridgeZxLf': liefengIcon, // 智性裂缝计
  'bridgeZxQj': qinxieIcon,  // 智性倾角计
  'bridgeZxJl': naoduIcon,   // 智性静力水准仪
  'bridgeZxYb': yingliIcon,  // 智性应变计
  'bridgeGxWd': wenduIcon,   // 工讯结构温度计
  'bridgeGxJl': naoduIcon,   // 工讯静力水准仪
  'bridgeZxWy': zhizuoIcon,  // 智性位移计
  'bridgeGxWy': zhizuoIcon,  // 工讯位移计
  'bridgeGxYb': yingliIcon   // 工讯应变计
}

// 获取监测设备数据
const fetchData = async () => {
  try {
    const res = await getBridgeMonitorStatistics()
    if (res.code === 200 && res.data) {
      // 更新统计数据
      statsData.totalDevices = res.data.totalCount || 0
      statsData.onlineRate = (res.data.onlineRate ? res.data.onlineRate.toFixed(2) + '%' : '0%')
      
      // 更新监测设备数据
      if (res.data.deviceTypeStatistics && res.data.deviceTypeStatistics.length > 0) {
        monitoringData.value = res.data.deviceTypeStatistics.map(item => ({
          name: item.deviceTypeName,
          total: item.totalCount,
          online: item.onlineCount,
          iconPath: deviceTypeIconMap[item.deviceType] || shipinIcon // 如果没有对应图标，使用默认图标
        }))
      }
    }
  } catch (error) {
    console.error('获取桥梁监测设备数据失败:', error)
  }
}

onMounted(() => {
  fetchData()
})
</script>

<style scoped>
.panel-content {
  flex: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

/* 上部分：总体统计样式 */
.stats-row {
  display: flex;
  margin-bottom: 0px;
  gap: 30%;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 0px;
}

.stat-dot {
  width: 9px;
  height: 9px;
  background: rgba(5, 90, 219, 0.4);
  border-radius: 50%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.stat-dot-inner {
  width: 5px;
  height: 5px;
  background: #055ADB;
  border-radius: 50%;
  position: absolute;
}

.stat-label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #FFFFFF;
}

.stat-value-blue {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 24px;
  line-height: 26px;
  text-align: left;
  font-style: normal;
  background: linear-gradient(90deg, #FFFFFF 0%, #055ADB 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stat-value-gradient {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 24px;
  line-height: 26px;
  text-align: left;
  font-style: normal;
  background: linear-gradient(90deg, #FFFFFF 0%, #36F281 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stat-unit {
  color: rgba(255, 255, 255, 0.4);
  font-size: 12px;
}

/* 下部分：监测设备列表样式 */
.monitoring-container {
  flex: 1;
  overflow-y: auto;
}

.monitoring-items {
  display: flex;
  height: 12.5rem;
  flex-wrap: wrap;
  gap: 8px 16px;
  overflow-y: auto;
}

.monitor-item {
  width: 200px;
  height: 34px;
  background: linear-gradient(270deg, rgba(48,71,104,0.5) 0%, #304768 50%, rgba(48,71,104,0.5) 100%);
  border: 1px solid;
  /* opacity: 0.6; */
  border-image: linear-gradient(270deg, rgba(171, 204, 255, 0), rgba(171, 204, 255, 0.5), rgba(171, 204, 255, 0)) 1 1;
  display: flex;
  align-items: center;
  padding: 6px 15px 6px 10px;
}

.item-icon {
  width: 20px;
  height: 20px;
  margin-right: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.item-icon img {
  max-width: 100%;
  max-height: 100%;
}

.item-name {
  flex: 1;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #D3E5FF;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.item-stats {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  display: flex;
}

.online-count {
  color: #FFCD28;
}

.count-divider, .total-count {
  color: #D3E5FF;
}

/* 响应式调整 */
@media (max-height: 940px) {
  .panel-content {
    padding: 10px;
    gap: 10px;
  }

  .stats-row {
    margin-bottom: 5px;
  }

  .stat-item {
    gap: 3px;
  }

  .stat-value-blue,
  .stat-value-gradient {
    font-size: 20px;
  }
  .monitoring-items {
    height: 10rem;
  }

  .monitor-item {
    height: 30px;
    padding: 4px 12px 4px 8px;
  }

  .item-icon {
    width: 18px;
    height: 18px;
    margin-right: 12px;
  }

  .item-name, .item-stats {
    font-size: 12px;
  }
}
</style>