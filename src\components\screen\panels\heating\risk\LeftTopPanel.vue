<template>
  <PanelBox title="风险统计">
    <template #extra>
      <div class="com-select">
        <CommonSelect v-model="selectedPipeType" :options="pipeTypeOptions" @change="handlePipeTypeChange" />
      </div>
    </template>
    <div class="panel-content">
      <div class="content-wrapper">
        <div class="risk-chart">
          <div class="chart-container" ref="chartRef"></div>
          <div class="center-text">
            <div class="risk-total">{{ totalRisk }}</div>
            <div class="unit">{{ unit }}</div>
          </div>
        </div>
        <div class="risk-list">
          <div class="risk-item" v-for="(item, index) in riskItems" :key="index">
            <div class="risk-indicator" :style="{ background: item.color }"></div>
            <div class="risk-name">{{ item.name }}</div>
            <div class="risk-value">{{ item.value }} <span class="unit-text">{{ unit }}</span></div>
          </div>
        </div>
      </div>
    </div>
  </PanelBox>
</template>

<script setup>
import { ref, onMounted, nextTick, computed, watch } from 'vue'
import * as echarts from 'echarts'
import PanelBox from '@/components/screen/PanelBox.vue'
import CommonSelect from '@/components/screen/CommonSelect.vue'
import { getPipelineRiskStatistics, getFactoryRiskStatistics, getStationRiskStatistics } from '@/api/heating'

// 管线类型选项
const pipeTypeOptions = [
  { value: 'pipeline', label: '管线' },
  { value: 'factory', label: '热源厂' },
  { value: 'station', label: '换热站' }
]

// 默认选择管线
const selectedPipeType = ref('pipeline')

// 定义数据源
const chartRef = ref(null)
let chartInstance = null

// 风险数据
const mockData = {
  pipeline: [
    { name: '重大风险', value: 0, color: '#DE6970' },
    { name: '较大风险', value: 0, color: '#FE9150' },
    { name: '一般风险', value: 0, color: '#D8F115' },
    { name: '低风险', value: 0, color: '#00E1B9' }
  ],
  factory: [
    { name: '重大风险', value: 0, color: '#DE6970' },
    { name: '较大风险', value: 0, color: '#FE9150' },
    { name: '一般风险', value: 0, color: '#D8F115' },
    { name: '低风险', value: 0, color: '#00E1B9' }
  ],
  station: [
    { name: '重大风险', value:0, color: '#DE6970' },
    { name: '较大风险', value: 0, color: '#FE9150' },
    { name: '一般风险', value: 0, color: '#D8F115' },
    { name: '低风险', value: 0, color: '#00E1B9' }
  ]
}

// 计算当前展示数据
const riskItems = computed(() => {
  console.log('重新计算riskItems，当前类型:', selectedPipeType.value)
  console.log('当前mockData:', JSON.parse(JSON.stringify(mockData[selectedPipeType.value])))
  return mockData[selectedPipeType.value] || []
})

// 计算总风险值
const totalRisk = computed(() => {
  return riskItems.value.reduce((sum, item) => sum + item.value, 0).toFixed(2)
})

// 计算单位
const unit = computed(() => {
  return selectedPipeType.value === 'pipeline' ? 'KM' : '个'
})

// 处理管线类型变化
const handlePipeTypeChange = () => {
  if (chartInstance) {
    updateChart()
  }
}

// 初始化图表
const initChart = () => {
  console.log('开始初始化图表...')
  if (!chartRef.value) {
    console.warn('图表DOM引用不存在，无法初始化图表')
    return
  }

  try {
    // 如果已经存在图表实例，先销毁它
    if (chartInstance) {
      console.log('销毁已存在的图表实例')
      chartInstance.dispose()
    }
    
    console.log('创建新的图表实例')
    chartInstance = echarts.init(chartRef.value)
    
    // 确保图表容器可见
    console.log('图表容器尺寸:', chartRef.value.clientWidth, chartRef.value.clientHeight)
    
    // 立即更新图表
    console.log('初始化后立即更新图表')
    updateChart()

    // 添加窗口大小变化监听器
    const resizeHandler = () => {
      if (chartInstance) {
        console.log('窗口大小变化，调整图表大小')
        chartInstance.resize()
      }
    }
    
    // 移除可能已存在的监听器，避免重复
    window.removeEventListener('resize', resizeHandler)
    window.addEventListener('resize', resizeHandler)
    
    console.log('图表初始化完成')
  } catch (error) {
    console.error('图表初始化失败:', error)
  }
}

// 更新图表数据
const updateChart = () => {
  if (!chartInstance) {
    console.warn('图表实例不存在，无法更新图表')
    return
  }

  console.log('开始更新图表...')
  const data = riskItems.value
  console.log('当前风险数据:', data)
  
  const colorList = data.map(item => item.color)
  const valueList = data.map(item => item.value)
  
  // 检查是否所有值都为0
  const allZero = valueList.every(value => value === 0)
  console.log('所有值是否为0:', allZero)
  
  // 即使所有值为0，也要显示饼图的轮廓
  const option = {
    backgroundColor: 'transparent',
    series: [{
      type: 'pie',
      radius: ['75%', '85%'],
      center: ['50%', '50%'],
      startAngle: 0,
      itemStyle: {
        borderRadius: 0,
        borderColor: 'transparent',
        borderWidth: 0
      },
      label: {
        show: false
      },
      silent: true,
      data: valueList.map((value, index) => ({
        value: value || 0.001, // 确保即使值为0也能显示饼图轮廓
        name: data[index].name,
        itemStyle: {
          color: colorList[index]
        }
      }))
    }]
  }

  console.log('设置图表选项:', option)
  chartInstance.setOption(option)
  console.log('图表更新完成')
}

// 从后端获取数据的方法
const fetchData = async (pipeType) => {
  console.log(`开始获取${pipeType}风险数据...`)
  try {
    let response = null
    
    // 根据选择的类型调用相应的API
    if (pipeType === 'pipeline') {
      response = await getPipelineRiskStatistics()
    } else if (pipeType === 'factory') {
      response = await getFactoryRiskStatistics()
    } else if (pipeType === 'station') {
      response = await getStationRiskStatistics()
    }
    
    console.log(`${pipeType}风险数据API响应:`, response)
    
    if (response && response.code === 200 && response.data) {
      // 记录当前的mockData状态
      console.log('更新前的mockData:', JSON.parse(JSON.stringify(mockData[pipeType])))
      
      // 处理返回的数据
      const riskData = response.data.riskLevelStatistics || []
      console.log('获取到的风险数据:', riskData)
      
      // 创建一个临时数组来存储更新后的数据
      const updatedData = [
        { name: '重大风险', value: 0, color: '#DE6970' },
        { name: '较大风险', value: 0, color: '#FE9150' },
        { name: '一般风险', value: 0, color: '#D8F115' },
        { name: '低风险', value: 0, color: '#00E1B9' }
      ]
      
      // 直接遍历前端定义的风险等级，查找对应的后端数据
      for (let i = 0; i < updatedData.length; i++) {
        const riskItem = updatedData[i]
        
        // 尝试精确匹配
        const exactMatch = riskData.find(item => item.name === riskItem.name)
        
        if (exactMatch) {
          console.log(`精确匹配成功: ${riskItem.name}`)
          if (pipeType === 'pipeline') {
            riskItem.value = parseFloat(exactMatch.length || 0)
          } else {
            riskItem.value = parseInt(exactMatch.count || 0)
          }
          console.log(`更新${riskItem.name}数据:`, riskItem.value)
          continue
        }
        
        // 尝试模糊匹配 - 检查名称是否包含关键词
        const fuzzyMatch = riskData.find(item => 
          item.name.includes(riskItem.name) || riskItem.name.includes(item.name)
        )
        
        if (fuzzyMatch) {
          console.log(`模糊匹配成功: ${fuzzyMatch.name} -> ${riskItem.name}`)
          if (pipeType === 'pipeline') {
            riskItem.value = parseFloat(fuzzyMatch.length || 0)
          } else {
            riskItem.value = parseInt(fuzzyMatch.count || 0)
          }
          console.log(`更新${riskItem.name}数据:`, riskItem.value)
          continue
        }
        
        // 尝试通过code匹配
        const codeMatch = riskData.find(item => {
          // 检查是否有风险等级代码映射
          const riskCodes = {
            '重大风险': ['2002801'],
            '较大风险': ['2002802'],
            '一般风险': ['2002803'],
            '低风险': ['2002804']
          }
          
          return riskCodes[riskItem.name] && riskCodes[riskItem.name].includes(item.code)
        })
        
        if (codeMatch) {
          console.log(`通过code匹配成功: ${codeMatch.code} -> ${riskItem.name}`)
          if (pipeType === 'pipeline') {
            riskItem.value = parseFloat(codeMatch.length || 0)
          } else {
            riskItem.value = parseInt(codeMatch.count || 0)
          }
          console.log(`更新${riskItem.name}数据:`, riskItem.value)
        }
        
        // 直接检查风险名称中是否包含关键词
        if (!exactMatch && !fuzzyMatch && !codeMatch) {
          for (const item of riskData) {
            if (item.name && (
                item.name.includes('较大') || 
                item.name.includes('较高') || 
                item.name.toLowerCase().includes('high')
              ) && riskItem.name === '较大风险') {
              console.log(`关键词匹配成功: ${item.name} -> ${riskItem.name}`)
              if (pipeType === 'pipeline') {
                riskItem.value = parseFloat(item.length || 0)
              } else {
                riskItem.value = parseInt(item.count || 0)
              }
              console.log(`更新${riskItem.name}数据:`, riskItem.value)
              break
            }
          }
        }
      }
      
      // 直接替换整个数组，确保响应式更新
      console.log('更新后的数据:', updatedData)
      
      // 使用新数据替换原数据，确保响应式更新
      for (let i = 0; i < mockData[pipeType].length; i++) {
        console.log(`更新${mockData[pipeType][i].name}:`, mockData[pipeType][i].value, '->', updatedData[i].value)
        mockData[pipeType][i].value = updatedData[i].value
      }
      
      // 记录更新后的mockData状态
      console.log('更新后的mockData:', JSON.parse(JSON.stringify(mockData[pipeType])))
      
      // 强制更新图表
      nextTick(() => {
        if (chartInstance) {
          console.log('更新图表...')
          updateChart()
        } else {
          console.log('图表实例不存在，无法更新')
        }
      })
    }
  } catch (error) {
    console.error('获取风险数据失败:', error)
  }
  console.log(`${pipeType}风险数据处理完成`)
}

// 监听管线类型变化
watch(selectedPipeType, (newValue) => {
  fetchData(newValue)
})

onMounted(async () => {
  await nextTick()
  // 先获取数据，再初始化图表
  await fetchData(selectedPipeType.value)
  initChart()
})
</script>

<style scoped>
.panel-content {
  flex: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
}

.com-select {
  margin-right: 20px;
}

.content-wrapper {
  display: flex;
  align-items: center;
  gap: 20px;
  height: 100%;
}

.risk-chart {
  width: 190px;
  height: 190px;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

.chart-container {
  width: 190px;
  height: 190px;
  background-image: url('@/assets/images/screen/gas/guanwangfengxian.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  position: relative;
}

.center-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #fff;
}

.risk-total {
  font-size: 24px;
  font-weight: bold;
  color: #22CBFF;
  margin-bottom: 4px;
}

.unit {
  font-size: 12px;
  color: #85A5C3;
}

.risk-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.risk-item {
  display: flex;
  align-items: center;
  width: 225px;
  height: 34px;
  background: linear-gradient(270deg, rgba(48, 71, 104, 0.5) 0%, #304768 50%, rgba(48, 71, 104, 0.5) 100%);
  border: 1px solid;
  opacity: 1;
  border-image: linear-gradient(270deg, rgba(171, 204, 255, 0), rgba(171, 204, 255, 0.5), rgba(171, 204, 255, 0)) 1 1;
  padding: 0 15px;
}

.risk-indicator {
  width: 9px;
  height: 8px;
  transform: skew(-20deg);
  margin-right: 8px;
}

.risk-name {
  width: 70px;
  color: #D3E5FF;
  font-size: 14px;
}

.risk-value {
  color: #ffffff;
  font-size: 14px;
  width: 80px;
  text-align: right;
  margin-left: auto;
}

.unit-text {
  color: #85A5C3;
  font-size: 12px;
}

/* 媒体查询适配不同分辨率 */
@media screen and (min-width: 1920px) and (max-width: 2560px) {
  .left-top-panel {
    height: 310px;
  }
}

@media screen and (max-width: 1919px) {
  .left-top-panel {
    height: 310px;
  }
}

@media screen and (min-width: 2561px) {
  .left-top-panel {
    height: 310px;
  }
}

/* 添加全屏模式(1080px高度)的适配 */
@media screen and (min-height: 1056px) and (max-height: 1080px) {
  .left-top-panel {
    height: 310px;
  }
}

@media screen and (min-height: 940px) and (max-height: 1055px) {
  .left-top-panel {
    height: 310px;
  }

  .panel-content {
    padding: 10px;
  }
}

@media screen and (min-height: 900px) and (max-height: 940px) {
  .left-top-panel {
    height: 252px;
  }

  .panel-content {
    padding: 8px;
  }

  .risk-chart {
    width: 180px;
    height: 180px;
  }

  .chart-container {
    width: 180px;
    height: 180px;
  }
}
</style>